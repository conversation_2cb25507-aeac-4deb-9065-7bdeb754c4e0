<?php

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_SYSTEM', true);
require NV_ROOTDIR . '/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

// Cập nhật giá trị của một hoặc nhiều trường lên ES
// Do ở bảng lớn thì việc chạy update_by_query rất nặng nên chạy tool này để cập nhật một trường mới lên ES

$index = NV_LANG_ELASTIC . 'dauthau_result';
$table = NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result';

$fields = [
    'cperiod' => 0,
    'cperiod_unit' => ''
];

$hosts = array(
    $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
);
$client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
    ->setHosts($hosts)
    ->setRetries(0)
    ->build();

$file_name = NV_ROOTDIR . '/tools/elastic_update_field_' . NV_LANG_DATA . '.txt';
if (file_exists($file_name)) {
    $strID = file_get_contents($file_name);
    $arrID = explode('_', $strID);
    $id = intval($arrID[0]);
    $max_id = intval($arrID[1]);
} else {
    $id = 0;
    $result = $db->query('SELECT MAX(id) FROM ' . $table);
    $max_id = $result->fetchColumn();
    $result->closeCursor();
}

print_r("Max id: " . NV_PREFIXLANG . ': ' . $max_id . PHP_EOL);

for ($i = 0; $i < 100; $i++) {
    $first = $id;
    $id2 = $id + 1000;
    $sql = "SELECT id, " . implode(', ', array_keys($fields)) . " FROM " . $table . " WHERE id > " . $id . " AND id <= " . $id2 . " LIMIT 100";
    $result = $db->query($sql);
    $params = [];
    $fileds_param = $fields;
    $count = 0;
    while ($row = $result->fetch()) {
        $params['body'][] = [
            'update' => [
                '_index' => $index,
                '_id' => $row['id']
            ]
        ];
        foreach ($fields as $key => $value) {
            $fileds_param[$key] = $row[$key];
        }
        $params['body'][] = [
            'doc' => $fileds_param
        ];
        $count++;
        $id = $row['id'];
    }
    $result->closeCursor();

    if (!empty($params)) {
        $client->bulk($params);
        print_r("Done from $first to $id\n");
    } elseif ($id2 < $max_id) {
        echo "empty param\n";
        $id = $id2;
        $count++;
    } elseif ($id2 >= $max_id) {
        echo "DONE!!\n";
        exit(1);
    }
    file_put_contents($file_name, $id2 . '_' . $max_id);

    usleep(50000); // sleep 0.05 giây
    unset($params);
}
