<?php

/**
 * @Project DauThau
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License commercial
 * @Createdate 26/03/2021, 10:36
 */
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';
// Tạo content tìm kiếm hàng hóa cho KQLCNT

$crconfig = array();
$crconfig['dbhost'] = 'db_dauthau'; // Sửa lại cho đúng theo config trên server
$crconfig['dbport'] = '';
$crconfig['dbname'] = 'dauthau_crawls';
$crconfig['dbuname'] = 'root';
$crconfig['dbpass'] = '';
$crconfig['dbtype'] = 'mysql';
$crconfig['collation'] = 'utf8_unicode_ci';
$crconfig['charset'] = 'utf8';
$crconfig['persistent'] = false;
$crconfig['prefix'] = 'nv4';

$dsn = $crconfig['dbtype'] . ':dbname=' . $crconfig['dbname'] . ';host=' . $crconfig['dbhost'] . ';charset=' . $crconfig['charset'];
if (!empty($crconfig['dbport'])) {
    $dsn .= ';port=' . $crconfig['dbport'];
}
$driver_options[PDO::ATTR_ERRMODE] = PDO::ERRMODE_EXCEPTION;
try {
    $dbcr = new PDO($dsn, $crconfig['dbuname'], $crconfig['dbpass'], $driver_options);
    $dbcr->exec("SET SESSION time_zone='" . $_time_zone_db . "'");
} catch (PDOException $e) {
    trigger_error($e);
    print_r($e);
    die();
}

$id = 0;
$separator = ' あ ';
$result = $db->query('SELECT MAX(id) FROM nv4_vi_bidding_result');
$max_id = $result->fetchColumn();
$result->closeCursor();
$hosts = array(
    $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
);
$client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();
try {
    loop:
    $id2 = $id + 1000;
    $run = 0;
    $array_id = $params = [];
    $result = $db->query('SELECT id, code, is_new_msc FROM nv4_vi_bidding_result WHERE id > ' . $id . ' AND id < ' . $id2 . ' ORDER BY id ASC LIMIT 50');
    while ($row = $result->fetch()) {
        $id_province = '0';
        $run++;
        $array_id[$row['id']] = [
            'id' => $row['id'],
            'content_goods' => ''
        ];
        $row['code_explode'] = explode('-', $row['code']);
        if ($row['is_new_msc']) {
            $result2 = $dbcr->query('SELECT other_data FROM nv23_url WHERE notifyno = ' . $dbcr->quote($row['code_explode'][0]) . ' AND notifyversion = ' . $dbcr->quote($row['code_explode'][1]));
            if ($row2 = $result2->fetchColumn()) {
                $other_data = json_decode($row2, true);
                if (!empty($other_data['locations'][0]['provCode'])) {
                    $id_province = implode(',', array_filter(array_unique(array_column($other_data['locations'], 'provCode'))));
                }
            } else {
                $result3 = $db->query('SELECT province_id FROM nv4_vi_bidding_row WHERE so_tbmt = ' . $db->quote($row['code']));
                $id_province = $result3->fetchColumn();
                $result3->closeCursor();
            }
            $result2->closeCursor();
        } else {
            $result3 = $db->query('SELECT province_id FROM nv4_vi_bidding_row WHERE so_tbmt = ' . $db->quote($row['code']));
            $id_province = $result3->fetchColumn();
            $result3->closeCursor();
        }
        empty($id_province) && $id_province = '0';
        $array_id[$row['id']]['id_province'] = $id_province;
        $id = $row['id'];
    }
    $result->closeCursor();
    if (empty($array_id)) {
        if ($id >= $max_id) {
            exit("\nDONE\n");
        } else {
            $id = $id2;
            goto loop;
        }
    }

    $result = $db->query('SELECT result_id, GROUP_CONCAT(DISTINCT goods_name SEPARATOR "' . $separator . '") AS content_goods FROM nv4_bidding_result_goods WHERE result_id IN (' . implode(',', array_keys($array_id)) . ') GROUP BY result_id');
    while ($row = $result->fetch()) {
        $array_id[$row['result_id']]['content_goods'] = strtolower(implode($separator, array_filter(array_unique(explode($separator, $row['content_goods'])))));
    }
    $result->closeCursor();

    foreach ($array_id as $k => $v) {
        $params['body'][] = [
            'update' => [
                '_index' => 'dauthau_result',
                '_id' => $k
            ]
        ];
        $params['body'][] = [
            'doc' => [
                'content_goods' => $v['content_goods'],
                'id_province' => $v['id_province']
            ]
        ];
        $params['body'][] = [
            'update' => [
                '_index' => 'en_dauthau_result',
                '_id' => $k
            ]
        ];
        $params['body'][] = [
            'doc' => [
                'content_goods' => $v['content_goods'],
                'id_province' => $v['id_province']
            ]
        ];
    }


    if (!empty($params['body'])) {
        $responses = $client->bulk($params)->asArray();
        if (!empty($responses['items'])) {
            foreach ($responses['items'] as $value) {
                print_r("Xử lý id: \033[33m" . $value['update']['_id']);
                if (preg_match('/^en_/', $value['update']['_index'])) {
                    echo " \033[34m en\033[0m";
                    if ($value['update']['status'] != 200) {
                        print_r("\033[31m ERROR\033[0m: " . $value['update']['error']['type'] . "\n");
                        file_put_contents(NV_ROOTDIR . '/tools/result_goods_content_error.log', 'en ' . $value['update']['_id'] . ': ' . $value['update']['error']['type'] . PHP_EOL, FILE_APPEND);
                    } else {
                        $db->exec('UPDATE nv4_en_bidding_result SET content_goods = ' . $db->quote($array_id[$value['update']['_id']]['content_goods']) . ', id_province = ' . $db->quote($array_id[$value['update']['_id']]['id_province']) . ' WHERE id = ' . $value['update']['_id']);
                        print_r("\033[32m OK\033[0m\n");
                    }
                } else {
                    echo " \033[34m vi\033[0m";
                    if ($value['update']['status'] != 200) {
                        print_r("\033[31m ERROR\033[0m: " . $value['update']['error']['type'] . "\n");
                        file_put_contents(NV_ROOTDIR . '/tools/result_goods_content_error.log', 'vi ' . $value['update']['_id'] . ': ' . $value['update']['error']['type'] . PHP_EOL, FILE_APPEND);
                    } else {
                        $db->exec('UPDATE nv4_en_bidding_result SET content_goods = ' . $db->quote($array_id[$value['update']['_id']]['content_goods']) . ', id_province = ' . $db->quote($array_id[$value['update']['_id']]['id_province']) . ' WHERE id = ' . $value['update']['_id']);
                        print_r("\033[32m OK\033[0m\n");
                    }
                }
            }
        }
    }
    goto loop;
} catch (Exception $e) {
    trigger_error($e);
    print_r($e);
}
