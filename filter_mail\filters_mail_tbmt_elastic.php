<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
$waitTimeoutInSeconds = 2;
$filters_mail_tbmt_log = NV_ROOTDIR . '/data/filters_mail_tbmt_log_' . $prefix_lang . '_' . date('Ymd') . '.txt';

if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
    file_put_contents($filters_mail_tbmt_log, "Server Elasticsearch didn't work.\n", FILE_APPEND);
    die();
}
fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
}
/*
 * Thuật toán như sau:
 * - Bảng nv4_vi_bidding_logs có trường
 * - status: Trạng thái bằng 0 là đang chạy, nếu truy cập lại thì tiếp tục chạy.
 * - user: ID của users đã chạy đến, Mỗi lần tìm kiếm chỉ khoảng 2 tài khoản để test. (Sau đó có thể nâng lên 50 khi số lựong nhiều)
 * - Bảng nv4_vi_bidding_logs được thêm mới khi không tìm thấy status = 0 và có thêm bài đăng.
 * - Khi tìm kiếm theo từng tài khỏan, sẽ tìm lần luợt bộ lọc của từng người, nếu tìm thấy thì lưu vào bảng nv4_vi_bidding_mail để chương trình send_mail.php lấy ra để gửi.
 */

/*
 * Dùng ?testuserid=USERID để kiểm tra trực tiếp đối với thành viên
 * Nếu testuserid hệ thống sẽ bỏ qua ghi dữ liệu vào CSLD chỉ hiển thị kết quả
 */

$testUserid = 0;

if (!$testUserid) {
    $new_loop = 0;
    // Bước 1: Kiểm tra có bảng logs nào đang chạy không, nếu không thì bắt đầu chạy mới, nếu có thì tiếp tục chạy đến userid tiếp theo
    $logs_info = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs WHERE status=0 ORDER BY id DESC limit 1')->fetch();

    if (!empty($logs_info)) {
        echo "\n\nLogid: " . $logs_info['id'] . " date: " . nv_date('H:i:s d/m/Y', $logs_info['from_time']) . "\n";
        $start_id = $logs_info['from_id'];
        $end_id = $logs_info['to_id'];
        $last_userid = $logs_info['userid'];
    } else {
        // Nếu không có phần nào đang chạy thì kiểm tra xem có tin đấu thầu mới không. Nếu có tin mới thì chạy vòng lặp mới, nếu không thì kết thúc
        $last_logs = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs ORDER BY id DESC limit 1')->fetch();

        $array_query_elastic = [];
        $array_query_elastic['size'] = 1;
        $array_query_elastic['sort'] = [
            [
                "id" => [
                    "order" => "desc"
                ]
            ]
        ];
        $array_query_elastic['_source'] = array(
            'id'
        );
        $params = [];
        $params['index'] = NV_LANG_ELASTIC . 'dauthau_bidding';
        // $params['type'] = 'nv4_vi_bidding_row';
        $params['body'] = $array_query_elastic;
        $response = $client->search($params)->asArray();
        if ($response['hits']['total']['value'] > 0) {
            $info['to_id'] = $response['hits']['hits'][0]['_source']['id'];
        }
        if (!isset($info['to_id']) or $info['to_id'] <= 0) {
            file_put_contents($filters_mail_tbmt_log, "Lỗi maxid ES \n", FILE_APPEND);
            die('Lỗi');
        }
        if (empty($last_logs)) {
            $start_id = 0; // Chạy lần đầu
            $end_id = $info['to_id'];
            $last_userid = 0;
            $new_loop = 1;
        } else {
            if ($last_logs['to_id'] < $info['to_id']) {
                // Nếu có tin thầu mới hoặc kế hoạch chọn nhà thầu mới, thông báo mời sơ tuyển mới thì lấy ra các bài mới để chạy vòng lặp mới
                $last_userid = 0;
                $start_id = $last_logs['to_id']; // Lấy id tin mời thầu của lần cuối cùng chạy
                $end_id = $info['to_id']; // Lấy id mời thầu lớn nhất của dữ liệu hiện có
                $new_loop = 1;
            } else {
                // Nếu không có dữ liệu mới để chạy thì dừng lại ngay
                file_put_contents($filters_mail_tbmt_log, "Lỗi select logs L71: Không có tin mới trong csdl. Hãy chạy tiếp khi có bài đăng mới\n", FILE_APPEND);
                die('Không có tin mới');
            }
        }
    }

    $time = time();
    $start_time = $time;
    if ($new_loop) {
        /*
         * if ($end_id > $start_id + 500) {
         * // Nếu có quá nhiều thì chỉ tìm kiếm trong 500
         * $end_id = $start_id + 500;
         * }
         */

        if ($start_id == $end_id) {
            die('No Data new');
        }

        try {
            // thêm logs mới vào csdl
            $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs (from_id, to_id, from_time, to_time, total_time, userid, status) VALUES (:from_id, :to_id, :from_time, :to_time, 0, 0, 0)');
            $stmt->bindParam(':from_id', $start_id, PDO::PARAM_INT);
            $stmt->bindParam(':to_id', $end_id, PDO::PARAM_INT);
            $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
            $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
            $exc = $stmt->execute();
            $logs_id = $db->lastInsertId();
            $run_time = 0;
        } catch (PDOException $e) {
            file_put_contents($filters_mail_tbmt_log, "Lỗi thêm logs mới vào csdl L92: " . print_r($e, true) . "\n", FILE_APPEND);
            die('Lỗi thêm logs mới');
        }
    } else {
        $logs_id = $logs_info['id'];
        $run_time = $logs_info['total_time'];
    }
} else {
    // Khi test thành viên
    $start_id = 0;
    $info = $db->query('SELECT max(id) as to_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row')->fetch();
    $end_id = $info['to_id']; // Lấy id mời thầu lớn nhất của dữ liệu hiện có
    $end_id = 982585;
    $time = time();
    $start_time = $time;
    $run_time = 0;
}

$arr_id_vip = [];
$arr_vip = [];

// Bước 2: Lấy ra ds các VIP mỗi lần 100 người
// 2.1 Lấy danh sách các VIP còn hạn sử dụng
$time_start_thisday = mktime(date('H'), date('i'), date('s'), date('n'), date('j'), date('Y'));

$number_user_scan_all = 50;
if ($testUserid) {
    // Test 1 tài khoản VIP
    $query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id =' . $testUserid . ' AND (vip = 1 OR vip = 11 OR vip = 5) AND prefix_lang = ' . $prefix_lang);
} else {
    // Lấy tài khoản vip
    $query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id >' . $last_userid . ' AND (vip = 1 OR vip = 11 OR vip = 5) AND prefix_lang = ' . $prefix_lang . ' ORDER BY user_id ASC limit ' . $number_user_scan_all);
}
$number_user_scan = 0;
$user_id_end = 0;
while ($vip_info = $query_vip->fetch()) {
    ++$number_user_scan;
    $user_id_end = $vip_info['user_id'];
    $arr_vip[$vip_info['user_id']]['from_time'] = $vip_info['from_time'];
    $arr_vip[$vip_info['user_id']]['to_time'] = $vip_info['end_time'];
    $arr_vip[$vip_info['user_id']]['filter'] = [];
    $arr_vip[$vip_info['user_id']]['bidding'] = [];
    $arr_vip[$vip_info['user_id']]['vip'][$vip_info['vip']] = $vip_info['vip'];

    $arr_id_vip[$vip_info['user_id']] = $vip_info['user_id'];
}

if ($config_bidding['use_reward_points'] and !$testUserid) {
    // 2.2 Lấy các user đăng ký gửi mail bằng điểm
    $array_id_user = $array_id_customs_points_email = $array_id_customs_points_excel = [];

    // lấy thêm các user mà lớn hơn $user_id_end của vòng chạy cuối cùng
    if ($new_loop) {
        $last_logs['userid'] = (empty($last_logs['userid'])) ? 0 : $last_logs['userid'];
        $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_logs['userid'] . ' AND (vip = 1 OR vip = 11 OR vip = 5) AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC');
        while ($points_email_info = $query_user->fetch()) {
            $arr_vip[$points_email_info['userid']]['vip'][$points_email_info['vip']] = $points_email_info['vip'];
            $arr_vip[$points_email_info['userid']]['to_time'] = $points_email_info['dateexpired'];
            $arr_vip[$points_email_info['userid']]['filter'][$points_email_info['filterid']] = [];
            $arr_vip[$points_email_info['userid']]['bidding'] = [];

            $array_id_user[$points_email_info['userid']] = $points_email_info['userid'];
            $array_id_customs_points_email[$points_email_info['userid']] = $points_email_info['id'];
        }
    }

    // các user đăng ký gửi email bằng điểm được lấy theo limit 50 và theo giới hạn các tk trong phạm vi của nhóm vip trên
    $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_userid . ' AND userid < ' . $user_id_end . ' AND (vip = 1 OR vip = 11 OR vip = 5) AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC limit ' . $number_user_scan_all);
    while ($points_email_info = $query_user->fetch()) {
        $arr_vip[$points_email_info['userid']]['vip'][$points_email_info['vip']] = $points_email_info['vip'];
        $arr_vip[$points_email_info['userid']]['to_time'] = $points_email_info['dateexpired'];
        $arr_vip[$points_email_info['userid']]['filter'][$points_email_info['filterid']] = [];
        $arr_vip[$points_email_info['userid']]['bidding'] = [];

        $array_id_user[$points_email_info['userid']] = $points_email_info['userid'];
        $array_id_customs_points_email[$points_email_info['userid']] = $points_email_info['id'];
    }

    // 2.3 Lấy các user đăng ký xuất excel bằng điểm
    // lấy thêm các user mà lớn hơn $user_id_end của vòng chạy cuối cùng
    if ($new_loop) {
        $last_logs['userid'] = (empty($last_logs['userid'])) ? 0 : $last_logs['userid'];
        $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_excel WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_logs['userid'] . ' AND vip = 1 AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC');
        while ($points_excel_info = $query_user->fetch()) {
            $arr_vip[$points_excel_info['userid']]['vip'][$points_excel_info['vip']] = $points_excel_info['vip'];
            $arr_vip[$points_excel_info['userid']]['to_time'] = $points_excel_info['dateexpired'];
            $arr_vip[$points_excel_info['userid']]['filter'][$points_excel_info['filterid']] = [];
            $arr_vip[$points_excel_info['userid']]['bidding'] = [];

            $array_id_user[$points_excel_info['userid']] = $points_excel_info['userid'];
            $array_id_customs_points_excel[$points_excel_info['userid']] = $points_excel_info['id'];
        }
    }

    // các user đăng ký xuất excel bằng điểm được lấy theo limit 50 và theo giới hạn các tk trong phạm vi của nhóm vip trên
    $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_excel WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_userid . ' AND userid < ' . $user_id_end . ' AND vip = 1 AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC limit ' . $number_user_scan_all);
    while ($points_excel_info = $query_user->fetch()) {
        $arr_vip[$points_excel_info['userid']]['vip'][$points_excel_info['vip']] = $points_excel_info['vip'];
        $arr_vip[$points_excel_info['userid']]['to_time'] = $points_excel_info['dateexpired'];
        $arr_vip[$points_excel_info['userid']]['filter'][$points_excel_info['filterid']] = [];
        $arr_vip[$points_excel_info['userid']]['bidding'] = [];

        $array_id_user[$points_excel_info['userid']] = $points_excel_info['userid'];
        $array_id_customs_points_excel[$points_excel_info['userid']] = $points_excel_info['id'];
    }
    ksort($arr_vip);
    $arr_id_all = array_merge($arr_id_vip, $array_id_user);
} else {
    $arr_id_all = $arr_id_vip;
}

// Xuất thông tin test tài khoản VIP
if ($testUserid) {
    if (empty($arr_vip)) {
        echo ("User này không có tài khoản VIP<br/>");
    } else {
        echo ("Tài khoản VIP OK<br/>" . debugArray($arr_vip) . "<br/><br/>");
    }
}

// Bước 3: Lấy ra ds các bộ lọc của VIP đc chọn
$user_all = [];
if (!empty($arr_id_all)) {
    // thông tin user
    $query_user = $db->query('SELECT userid, email FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_id_all) . ')');
    while ($user = $query_user->fetch()) {
        $user_all[$user['userid']] = $user['email'];
    }

    $query_filter = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE userid IN (' . implode(',', $arr_id_all) . ') AND (status=1 OR send_excel = 1) AND (vip_use=0 OR vip_use=1 OR vip_use=11 OR vip_use=5) AND prefix_lang = ' . $prefix_lang);
    while ($result = $query_filter->fetch()) {
        if (in_array($result['vip_use'], $arr_vip[$result['userid']]['vip'])) {
            $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
            $arr_vip[$result['userid']]['email'] = isset($user_all[$result['userid']]) ? $user_all[$result['userid']] : '';
        } else if (isset($arr_vip[$result['userid']]['filter'][$result['id']])) {
            $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
            $arr_vip[$result['userid']]['email'] = isset($user_all[$result['userid']]) ? $user_all[$result['userid']] : '';
        }
    }
}

// Xuất thông tin test bộ lọc
if ($testUserid) {
    if (empty($arr_vip[$testUserid]['filter'])) {
        echo ("User này không có bộ lọc<br/>");
    } else {
        echo ("Bộ lọc OK<br/>" . debugArray($arr_vip[$testUserid]['filter']) . "<br/><br/>");
    }
}

// Bước 4: Lấy ra các tin trong ds tin mới mà thỏa mãn bộ lọc tương ứng với từng VIP để thêm vào bảng _tmp
if (empty($arr_vip)) {
    if ($testUserid) {
        die("Kết thúc test thành viên<br />");
    }

    // Nếu không lấy được VIP nào thì coi như đã chạy xong một vòng và update lại status là đã chạy xong
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $change = $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_tbmt_log, "Đã quét xong một lượt các vip. L172 Tổng thời gian cho một lượt:" . $total_time . "\n", FILE_APPEND);
    die();
} else {
    /*
     * bỏ lưu $user_id_end vào log do nếu lỗi trong quá trình gửi các vip vẫn ghi nhận $user_id_end
     * if (!$testUserid) {
     * $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs SET userid =' . $user_id_end . ' WHERE id =' . $logs_id);
     * }
     */

    file_put_contents($filters_mail_tbmt_log, "ArrayID TBMT:" . $start_id . "-" . $end_id . ";\n", FILE_APPEND);

    $db->beginTransaction();

    try {
        foreach ($arr_vip as $vip_id => $vip) {
            echo "Bắt đầu quét:" . $vip_id . "\n";
            file_put_contents($filters_mail_tbmt_log, "Bắt đầu quét:" . $vip_id . "; ", FILE_APPEND);

            // Lấy ra danh sách các tin mời thầu, KHLCNT, TBMST thỏa mãn điều kiện tìm kiếm từ các bộ lọc
            $arr_id = [];
            $priority = 0;
            if (!empty($vip['filter'])) {
                foreach ($vip['filter'] as $filter) {
                    $check_request = 0;
                    if (!empty($filter)) {
                        file_put_contents($filters_mail_tbmt_log, "id bộ lọc:" . $filter['id'] . "; ", FILE_APPEND);
                        // Từ bộ lọc chọn ra điều kiện lọc where tương ứng
                        $search_elastic = [];
                        $search_elastic['must'][]['range']['id'] = [
                            "gt" => $start_id,
                            "lte" => $end_id
                        ];

                        // Loại trừ các thông báo có ngày đăng tải trước 15/02/2025 và hình thức lựa chọn là CGTTRG
                        if (NV_CURRENTTIME < 1739552400) {
                            $search_elastic['must_not'][] = [
                                'bool' => [
                                    'must' => [
                                        ['range' => ['ngay_dang_tai' => ['lt' => 1739552400]]],
                                        ['match' => ['hinh_thuc_lua_chon' => 'CGTTRG']]
                                    ]
                                ]
                            ];
                        }

                        // tìm kiếm theo khoảng thời gian
                        if ($filter['time_find'] > 0) {
                            $time_find = mktime(0, 0, 0, date('n'), date('j'), date('Y')) - 86400 * $filter['time_find'];
                            if ($filter['type'] == 0) {
                                $search_elastic['must'][]['range']['ngay_dang_tai'] = [
                                    "gte" => $time_find
                                ];
                            } else {
                                $search_elastic['must'][]['range']['den_ngay'] = [
                                    "gte" => $time_find
                                ];
                            }
                        }
                        if ($filter['cat'] > 0) {
                            $search_elastic['must'][] = [
                                'match' => [
                                    'type_bid' => [
                                        'query' => $filter['cat'] == 1 ? "1" : "0"
                                    ]
                                ]
                            ];
                        }
                        if ($filter['type_org'] == 2) {
                            $search_elastic['must'][] = [
                                'match' => [
                                    'type_org' => [
                                        'query' => "1"
                                    ]
                                ]
                            ];
                        }

                        if (!empty($filter['phanmuc'])) {
                            $filter['phanmuc'] = explode(',', $filter['phanmuc']);
                            $_phanmucterm = '~[0-9](' . implode('|', $filter['phanmuc']) . ')~[0-9]';
                            $search_elastic['must'][] = [
                                'regexp' => [
                                    'phanmuc' => [
                                        'value' => $_phanmucterm
                                    ]
                                ]
                            ];
                            /*
                             * $filter['phanmuc'] = explode(',', $filter['phanmuc']);
                             * foreach ($filter['phanmuc'] as $value) {
                             * $search_elastic['must'][] = [
                             * 'match_phrase_prefix' => [
                             * 'phanmuc' => $value
                             * ]
                             * ];
                             * }
                             */
                        }
                        if (!empty($filter['vsic'])) {
                            $filter['vsic'] = explode(',', $filter['vsic']);
                            foreach ($filter['vsic'] as $v) {
                                if ($v != 0) {
                                    $vsic_regex[] = $v;
                                }
                            }
                            if (!empty($vsic_regex)) {
                                $vsic_regex = '.*(' . implode('|', $vsic_regex) . ').*';
                                $vsic_query['bool']['should'][] = [
                                    'regexp' => [
                                        'vsic.keyword' => [
                                            'value' => $vsic_regex
                                        ]
                                    ]
                                ];
                            }
                            if (in_array(0, $filter['vsic'])) {
                                $vsic_query['bool']['should'][] = [
                                    'terms' => [
                                        'vsic.keyword' => ['', '0']
                                    ]
                                ];
                            }
                            $search_elastic['must'][] = $vsic_query;
                        }

                        // Tìm kiếm theo tỉnh thành
                        if (!empty($filter['idprovince'])) {
                            $filter['idprovince'] = explode(',', $filter['idprovince']);
                            $search_idprovince = [];
                            foreach ($filter['idprovince'] as $key_idp) {
                                $search_idprovince[] = [
                                    'regexp' => [
                                        'province_id' => [
                                            'value' => '~[0-9]' . $key_idp . '~[0-9]',
                                            'flags' => 'ALL'
                                        ]
                                    ]
                                ];
                            }
                            $search_elastic['must'][]['bool']['should'] = $search_idprovince;
                        }

                        if (!empty($filter['type_choose_id'])) {
                            $search_elastic['must'][] = [
                                'match' => [
                                    'type_choose_id' => [
                                        'query' => $filter['type_choose_id']
                                    ]
                                ]
                            ];
                        }

                        // tìm theo tiền bảo đảm
                        if ($filter['money_from'] > 0 and $filter['money_to']) {
                            $search_elastic['must'][]['range']['money_bid'] = [
                                "gte" => $filter['money_from'],
                                "lte" => $filter['money_to']
                            ];
                        } else {
                            if ($filter['money_from'] > 0) {
                                $search_elastic['must'][]['range']['money_bid'] = [
                                    "gte" => $filter['money_from']
                                ];
                            }
                            if ($filter['money_to'] > 0) {
                                $search_elastic['must'][]['range']['money_bid'] = [
                                    "lte" => $filter['money_to']
                                ];
                            }
                        }

                        // tìm theo giá mời thầu
                        if ($filter['price_from'] > 0 and $filter['price_to']) {
                            $search_elastic['must'][]['range']['price'] = [
                                "gte" => $filter['price_from'],
                                "lte" => $filter['price_to']
                            ];
                        } else {
                            if ($filter['price_from'] > 0) {
                                $search_elastic['must'][]['range']['price'] = [
                                    "gte" => $filter['price_from']
                                ];
                            }
                            if ($filter['price_to'] > 0) {
                                $search_elastic['must'][]['range']['price'] = [
                                    "lte" => $filter['price_to']
                                ];
                            }
                        }

                        // tim kiếm theo lĩnh vực thông báo
                        if ($filter['vip_use'] == 5) {
                            if ($filter['vip5_open'] == 1) {
                                // mở rộng: tìm theo is_vip5: được lọc theo từ khóa có liên quan đến vốn khác
                                $search_elastic['must'][] = [
                                    'term' => [
                                        'is_vip5' => 1
                                    ]
                                ];
                            } else {
                                $search_elastic['must'][] = [
                                    'term' => [
                                        'pham_vi' => 1
                                    ]
                                ];
                                // phạm vi =1: ngoài lĩnh vực điều chỉnh của luật, vốn khác
                            }

                            if (!empty($filter['field'])) {
                                $search_field = [];
                                $filter['field'] = explode(',', $filter['field']);
                                foreach ($filter['field'] as $key) {
                                    $search_field[] = [
                                        'term' => [
                                            'linh_vuc_thong_bao' => $key
                                        ]
                                    ];
                                }
                                $search_elastic['must'][]['bool']['should'] = $search_field;
                            }
                        } else {
                            if (!empty($filter['field'])) {
                                $search_field = [];
                                $filter['field'] = explode(',', $filter['field']);
                                foreach ($filter['field'] as $key) {
                                    $search_field[] = [
                                        'term' => [
                                            'linh_vuc_thong_bao' => $key
                                        ]
                                    ];
                                }
                                $search_elastic['must'][]['bool']['should'] = $search_field;
                            }

                            // phạm vi !=1: trong lĩnh vực điều chỉnh của luật, gửi mail VIP1, 1QT,
                            $search_elastic['must_not'][] = [
                                'term' => [
                                    'pham_vi' => 1
                                ]
                            ];
                        }

                        if (!empty($filter['phuongthuc'])) {
                            $search_field = [];
                            $filter['phuongthuc'] = explode(',', $filter['phuongthuc']);
                            foreach ($filter['phuongthuc'] as $key) {
                                $search_field[] = [
                                    'term' => [
                                        'phuong_thuc_num' => $key
                                    ]
                                ];
                            }
                            $search_elastic['must'][]['bool']['should'] = $search_field;
                        }

                        if ($filter['vip_use'] == 11) {
                            $search_isDomestic = [];
                            $search_isDomestic[] = [
                                'match' => [
                                    'id_hinhthucluachon' => [
                                        'query' => 2
                                    ]
                                ]
                            ];
                            // thêm id_hinhthucluachon do isDomestic msc mới là 0
                            $search_isDomestic[] = [
                                'match' => [
                                    'id_hinhthucluachon' => [
                                        'query' => 0
                                    ]
                                ]
                            ];

                            $search_elastic['must'][]['bool']['should'] = $search_isDomestic;
                        }

                        if ($filter['search_type'] == 1) {
                            if ($filter['par_search']) {
                                $search_fields = [
                                    'so_tbmt',
                                    'goi_thau'
                                ];
                            } else {
                                $search_fields = [
                                    'content_full'
                                ];
                            }
                        } else {
                            if ($filter['par_search']) {
                                $search_fields = [
                                    'so_tbmt',
                                    'goi_thau_search'
                                ];
                            } else {
                                $search_fields = [
                                    'content'
                                ];
                            }
                        }
                        if ($filter['goods_search'] == 1) {
                            $search_fields[] = 'content_goods';
                        } elseif ($filter['goods_search'] == 2) {
                            $search_fields = [
                                'content_goods'
                            ];
                        }
                        // từ khóa loại trừ
                        if (!empty($filter['without_key'])) {
                            $arr_key = explode(',', $filter['without_key']);
                            foreach ($search_fields as $f) {
                                foreach ($arr_key as $key) {
                                    if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                                        continue;
                                    }
                                    if (empty($key)) {
                                        continue;
                                    }
                                    if ($filter['search_type'] == 1) {
                                        $key = trim($key);
                                        $search_elastic['must_not'][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    } else {
                                        $f != 'content_goods' && $key = str_replace('-', ' ', change_alias($key));
                                        $f == 'content_goods' && $key = strtolower($key);
                                        $key = trim($key);
                                        $search_elastic['must_not'][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    }
                                }
                            }
                        }

                        if (!empty($filter['key_search2'])) {
                            // Tìm theo từ khóa cùng
                            $arr_key = explode(',', $filter['key_search2']);
                            $search_bosung = [];
                            if (sizeof($search_fields) > 1) {
                                // Nếu số lượng trường tìm kiếm > 1 thì sẽ dùng bool must từng từ khóa
                                // mỗi từ khóa thì lại should trong từng trường
                                foreach ($arr_key as $key) {
                                    if (empty($key)) {
                                        continue;
                                    }
                                    $search_bosung_should = [];
                                    foreach ($search_fields as $f) {
                                        if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                                            continue;
                                        }
                                        if ($filter['search_type'] == 1) {
                                            $key = trim($key);
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                            $search_bosung[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        } else {
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => ($f != 'content_goods' ? trim(str_replace('-', ' ', change_alias($key))) : trim(strtolower($key)))
                                                ]
                                            ];
                                            $search_bosung[] = [
                                                "match_phrase" => [
                                                    $f => ($f != 'content_goods' ? trim(str_replace('-', ' ', change_alias($key))) : trim(strtolower($key)))
                                                ]
                                            ];
                                        }
                                    }
                                    if (empty($filter['search_one_key'])) {
                                        $search_elastic['must'][]['bool'] = [
                                            'should' => $search_bosung_should,
                                            'minimum_should_match' => 1,
                                        ];
                                    }
                                }
                                if (!empty($filter['search_one_key']) and !empty($search_bosung)) {
                                    //Một trong các từ khóa là điều kiện bắt buộc
                                    $search_elastic['must'][] = [
                                        "bool" => [
                                            "should" => $search_bosung,
                                            "minimum_should_match" => 1
                                        ]
                                    ];
                                }
                            } else {
                                foreach ($search_fields as $f) {
                                    $search_bosung_should = [];
                                    foreach ($arr_key as $key) {
                                        if (empty($key)) {
                                            continue;
                                        }
                                        if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                                            continue;
                                        }
                                        if ($filter['search_type'] == 1) {
                                            $key = trim($key);
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        } else {
                                            $f != 'content_goods' && $key = str_replace('-', ' ', change_alias($key));
                                            $f == 'content_goods' && $key = strtolower($key);
                                            $key = trim($key);
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        }
                                    }
                                    if (!empty($filter['search_one_key'])) {
                                        $search_elastic['must'][]['bool']['should'] = $search_bosung_should;
                                    } else {
                                        $search_elastic['must'] = array_merge($search_elastic['must'], $search_bosung_should);
                                        //$search_elastic['must'] = $search_bosung_should;
                                    }
                                }
                            }
                        }

                        if (!empty($filter['key_search'])) {
                            $arr_key = explode(',', $filter['key_search']);

                            foreach ($search_fields as $f) {
                                foreach ($arr_key as $key) {
                                    if ($filter['searchkind'] >= 1) { // 1. Khớp từ hoặc một số từ 2. Khớp tất cả từ
                                        $arr_key_temp = explode(' ', $key);
                                        $tmp_search_elastic = [];
                                        foreach ($arr_key_temp as $_key_temp) {
                                            if ($filter['search_type'] == 0 && $f != 'content_goods') {
                                                $_key_temp = str_replace('-', ' ', change_alias($_key_temp));
                                            } else if ($filter['search_type'] == 0 && $f == 'content_goods') {
                                                $_key_temp = strtolower($_key_temp);
                                            }
                                            $_key_temp = trim($_key_temp);
                                            if (empty($_key_temp)) {
                                                continue;
                                            }
                                            if (!preg_match('/[0-9]/', $_key_temp) && $f == 'so_tbmt') {
                                                continue;
                                            }

                                            if ($filter['searchkind'] == 1) {
                                                if (preg_match('/^[0-9]+?$/', $_key_temp)) {
                                                    // Nếu keyword chỉ là dãy số thì tìm wildcard
                                                    $search_elastic['should'][] = [
                                                        "wildcard" => [
                                                            $f => [
                                                                "value" => '*' . $_key_temp . '*'
                                                            ]
                                                        ]
                                                    ];
                                                } else {
                                                    $search_elastic['should'][] = [
                                                        "match_phrase" => [
                                                            $f => $_key_temp
                                                        ]
                                                    ];
                                                }
                                            } else {
                                                if (preg_match('/^[0-9]+?$/', $_key_temp)) {
                                                    // Nếu keyword chỉ là dãy số thì tìm wildcard
                                                    $tmp_search_elastic[] = [
                                                        "wildcard" => [
                                                            $f => [
                                                                "value" => '*' . $_key_temp . '*'
                                                            ]
                                                        ]
                                                    ];
                                                } else {
                                                    $tmp_search_elastic[] = [
                                                        "match_phrase" => [
                                                            $f => $_key_temp
                                                        ]
                                                    ];
                                                }
                                            }
                                        }

                                        if (!empty($tmp_search_elastic)) {
                                            $search_elastic['should'][]['bool']['must'] = $tmp_search_elastic;
                                        }
                                    } else { // 0.Khớp chính xác cụm từ
                                        if ($filter['search_type'] == 0 && $f != 'content_goods') {
                                            $key = str_replace('-', ' ', change_alias($key));
                                        } else if ($filter['search_type'] == 0 && $f == 'content_goods') {
                                            $key = strtolower($key);
                                        }
                                        $key = trim($key);
                                        if (empty($key)) {
                                            continue;
                                        }
                                        if (!preg_match('/[0-9]/', $key) && $f == 'so_tbmt') {
                                            continue;
                                        }
                                        if (preg_match('/^[0-9]+?$/', $key)) { // Nếu keyword chỉ là dãy số thì tìm wildcard
                                            $search_elastic['should'][] = [
                                                "wildcard" => [
                                                    $f => [
                                                        "value" => '*' . $key . '*'
                                                    ]
                                                ]
                                            ];
                                        } else {
                                            $search_elastic['should'][] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        }
                                    }
                                }
                            }
                        }

                        if (!empty($search_elastic['should'])) {
                            $search_elastic['minimum_should_match'] = '1';
                            $search_elastic['boost'] = '1.0';
                        }
                        if (!empty($search_elastic['must'])) {
                            foreach ($search_elastic['must'] as $k => $v) {
                                if (!empty($v['bool']['should'])) {
                                    $search_elastic['must'][$k]['bool']['minimum_should_match'] = '1';
                                    $search_elastic['must'][$k]['bool']['boost'] = '1';
                                }
                            }
                        }
                        $array_query_elastic = [];
                        if (!empty($search_elastic)) {
                            $array_query_elastic['query']['bool'] = $search_elastic;
                        }
                        $array_query_elastic['size'] = 500;
                        $array_query_elastic['sort'] = [
                            [
                                "ngay_dang_tai" => [
                                    "order" => "desc"
                                ]
                            ]
                        ];

                        if ($testUserid) {
                            echo "Tìm kiếm với query: <br />" . debugArray($array_query_elastic) . "<br /><br />";
                        }

                        $array_query_elastic['_source'] = array(
                            'id'
                        );

                        $params = [];
                        $params['index'] = NV_LANG_ELASTIC . 'dauthau_bidding';
                        // $params['type'] = 'nv4_vi_bidding_row';
                        $params['body'] = $array_query_elastic;
                        $response = $client->search($params)->asArray();

                        if ($testUserid) {
                            echo "Kết quả: <br />" . debugArray($response) . "<br /><br />";
                        }

                        $check_filter = 0;
                        if (isset($response['hits']['hits'])) {
                            $num_row = $response['hits']['total']['value'];
                            foreach ($response['hits']['hits'] as $value) {
                                if (!empty($value['_source'])) {
                                    $bidding = $value['_source'];
                                    $arr_id[$bidding['id']] = $bidding['id'];
                                    if (!$testUserid) {
                                        if ($filter['vip_use'] == 11 && $filter['status'] == 1) {
                                            // Nếu tìm ra tin thầu thỏa mãn điều kiện thì thêm bảng tmp
                                            $stmt = $db->prepare('INSERT IGNORE INTO `' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bidqt_id` (userid, filter_id, bid_id, send_status, addtime) VALUES (:userid, :filter_id, :bid_id , 0, :addtime)');
                                            $time = NV_CURRENTTIME;
                                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                            $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                            $stmt->bindParam(':bid_id', $bidding['id'], PDO::PARAM_INT);
                                            $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                            $exc = $stmt->execute();
                                        } else if ($filter['vip_use'] == 5 && $filter['status'] == 1) {
                                            // Nếu tìm ra tin thầu thỏa mãn điều kiện thì thêm bảng tmp
                                            $stmt = $db->prepare('INSERT IGNORE INTO `' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bidvk_id` (userid, filter_id, bid_id, send_status, addtime) VALUES (:userid, :filter_id, :bid_id , 0, :addtime)');
                                            $time = NV_CURRENTTIME;
                                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                            $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                            $stmt->bindParam(':bid_id', $bidding['id'], PDO::PARAM_INT);
                                            $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                            $exc = $stmt->execute();
                                        } else {
                                            // Nếu tìm ra tin thầu thỏa mãn điều kiện thì thêm bảng tmp
                                            $stmt = $db->prepare('INSERT IGNORE INTO `' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id` (userid, filter_id, bid_id, notify_vip, send_status, addtime) VALUES (:userid, :filter_id, :bid_id, :notify_vip , :send_status, :addtime)');
                                            $notify = '';
                                            $time = NV_CURRENTTIME;
                                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                            $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                            $stmt->bindParam(':bid_id', $bidding['id'], PDO::PARAM_INT);
                                            $stmt->bindParam(':notify_vip', $notify, PDO::PARAM_STR);
                                            $stmt->bindValue(':send_status', ($filter['status'] == 0 ? -100 : 0), PDO::PARAM_INT);
                                            $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                            $exc = $stmt->execute();

                                            // Thêm vào bảng request nếu khách hàng yêu cầu gửi excel
                                            if ($filter['send_excel'] && $filter['vip_use'] == 1) { //
                                                if (empty($check_request)) {
                                                    $check_request = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_request WHERE status = 0 AND type_request = 0 AND filter_type = 1 AND filter_id = ' . $filter['id'])->fetch();
                                                }

                                                $email = '';
                                                if (empty($check_request)) {
                                                    // thêm vào bảng request
                                                    // mức độ ưu tiên
                                                    if (empty($priority)) {
                                                        $priority = $db->query('SELECT count(*) FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_request WHERE userid=' . $vip_id . ' AND addtime >= ' . mktime(0, 0, 0, date('m', NV_CURRENTTIME), date('d', NV_CURRENTTIME), date('Y', NV_CURRENTTIME)))->fetchColumn();
                                                    } else {
                                                        $priority++;
                                                    }

                                                    // các điều kiện lọc
                                                    $array_param = [
                                                        'title' => $filter['title'],
                                                        'vip_use' => $filter['vip_use'],
                                                        'key' => $filter['key_search'],
                                                        'key2' => $filter['key_search2'],
                                                        'search_type_content' => $filter['search_type'],
                                                        'without_key' => $filter['without_key'],
                                                        'time_find' => $filter['time_find'],
                                                        'invest_from' => $filter['invest_from'],
                                                        'invest_to' => $filter['invest_to'],
                                                        'price_plan_from' => $filter['price_plan_from'],
                                                        'price_plan_to' => $filter['price_plan_to'],
                                                        'par_search' => $filter['par_search'],
                                                        'goods_search' => $filter['goods_search'],
                                                        'searchkind' => $filter['searchkind']
                                                    ];

                                                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_request (userid, email, request_params, addtime, send_mail, priority, filter_id, filter_type) VALUES (:userid, :email, :request_params, :addtime, :send_mail, :priority, :filter_id, :filter_type)');

                                                    $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                                    $email = !empty($vip['email']) ? $vip['email'] : '';
                                                    $stmt->bindParam(':email', $email, PDO::PARAM_STR);
                                                    $stmt->bindValue(':request_params', json_encode($array_param, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), PDO::PARAM_STR);
                                                    $stmt->bindValue(':addtime', NV_CURRENTTIME, PDO::PARAM_INT);
                                                    $stmt->bindValue(':send_mail', 1, PDO::PARAM_INT);
                                                    $stmt->bindValue(':priority', !empty($priority) ? $priority : 0, PDO::PARAM_INT);
                                                    $stmt->bindValue(':filter_id', $filter['id'], PDO::PARAM_INT);
                                                    $stmt->bindValue(':filter_type', 1, PDO::PARAM_INT);

                                                    $exc = $stmt->execute();
                                                    if ($exc) {
                                                        $check_request = $db->lastInsertId();
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            // ghi log bộ lọc
                            if ($num_row > 0) {
                                $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastresult = ' . NV_CURRENTTIME . ', stat_totalresult = stat_totalresult + ' . $num_row . ' WHERE id=' . $filter['id']);
                            }
                        } else {
                            file_put_contents($filters_mail_tbmt_log, " Lỗi Search ES:" . print_r($response, true) . "\n", FILE_APPEND);
                        }
                    }
                }
            }
            file_put_contents($filters_mail_tbmt_log, " Bid:" . implode(',', $arr_id) . "\n", FILE_APPEND);
            if (!$testUserid) {
                // ghi lại logs cho quá trình tìm kiếm tin cho mỗi vip
                $time = NV_CURRENTTIME;
                $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs SET to_time = :to_time, userid = :userid WHERE id =' . $logs_id);
                $_vip_id = ($vip_id >= $user_id_end) ? $user_id_end : $vip_id;
                $stmt->bindParam(':userid', $_vip_id, PDO::PARAM_INT);
                $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
                $exc = $stmt->execute();
            }
        }
        $db->commit();
    } catch (PDOException $e) {
        file_put_contents($filters_mail_tbmt_log, "Lỗi INSERT  L401:\n" . print_r($e, true) . "\n", FILE_APPEND);
        notify_to_slack($filters_mail_tbmt_log, "filters_mail_tbmt_elastic.php Lỗi INSERT  L401:\n" . print_r($e, true) . "\n");
        $db->rollBack();
        die(); // Remove this line after checks finished
    }

    // Dừng nếu test
    if ($testUserid) {
        die("Kết thúc test thành viên<br />");
    }

    // Sau khi chạy xong một vòng 50 VIP thì thông báo chạy xong và thời gian chạy cho một vòng
    $time = time();
    $this_time = $time - NV_CURRENTTIME;
    $total_time = intval($run_time + $this_time);
    try {
        if ($number_user_scan < $number_user_scan_all) {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ', status=1 where id =' . $logs_id);
        } else {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ' where id =' . $logs_id);
        }
        if (sizeof($arr_id_vip)) {
            $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs  SET filters_mail = ' . NV_CURRENTTIME . ' WHERE user_id IN (' . implode(',', $arr_id_vip) . ') AND (vip=1 OR vip = 11 OR vip = 5) AND prefix_lang = ' . $prefix_lang . '');
        }
        if ($config_bidding['use_reward_points']) {
            if (sizeof($array_id_customs_points_email) > 0) {
                $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs_points_email  SET timefilter = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_id_customs_points_email) . ') AND prefix_lang = ' . $prefix_lang . '');
            }

            if (sizeof($array_id_customs_points_excel) > 0) {
                $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs_points_excel  SET timefilter = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_id_customs_points_excel) . ') AND prefix_lang = ' . $prefix_lang . '');
            }
        }
    } catch (PDOException $e) {
        file_put_contents($filters_mail_tbmt_log, "Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP  L415:\n" . print_r($e, true) . "\n", FILE_APPEND);
        notify_to_slack($filters_mail_tbmt_log, "filters_mail_tbmt_elastic.php Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP  L415:\n" . print_r($e, true) . "\n");
        die();
    }

    $note = 'L420 Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
    file_put_contents($filters_mail_tbmt_log, $note . "\n", FILE_APPEND);
    die($note . "\n");
}

/**
 *
 * @param array $data
 * @return string
 */
function debugArray($data)
{
    return "<pre><code>" . print_r($data, true) . "</code></pre>";
}
