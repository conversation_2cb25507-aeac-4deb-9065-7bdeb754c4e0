<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
set_time_limit(0);

if (file_exists(NV_ROOTDIR . '/tools/update_tbmt_aita.txt')) {
    $minid = file_get_contents(NV_ROOTDIR . '/tools/update_tbmt_aita.txt');
    $minid = intval($minid);
} else {
    $minid = $db->query("SELECT id FROM nv4_vi_aita_bid_id WHERE bid_id >= 1085332 ORDER BY id ASC LIMIT 1")->fetchColumn();
}
try {
    $sql = "SELECT id, bid_id FROM nv4_vi_aita_bid_id WHERE id > " . $minid . " ORDER BY id ASC LIMIT 1000";
    $query = $db->query($sql);

    $arr_id = [];
    $newid = $minid;
    $arr_id = array_column($query->fetchAll(), 'bid_id', 'id');
    if (empty($arr_id)) {
        echo "Đã chạy hết!!";
        exit(1);
    }
    $newid = array_key_last($arr_id);
    $list_id = implode(',', $arr_id);
    $arr_id_new_msc = $db->query("SELECT id FROM nv4_vi_bidding_row WHERE is_new_msc = 1 AND id IN(" . $list_id . ")")->fetchAll();

    $list_id_new_msc = implode(',', array_column($arr_id_new_msc, 'id'));
    if (!empty($list_id_new_msc)) {
        $db->query("UPDATE `nv4_vi_aita_bid_id` SET `send_status`='0',`error_info`='' WHERE bid_id IN(" . $list_id_new_msc . ")");
    }
} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
}
if ($newid > $minid) {
    file_put_contents(NV_ROOTDIR . '/tools/update_tbmt_aita.txt', $newid);
}
echo "minid:" . $minid . "\n";
