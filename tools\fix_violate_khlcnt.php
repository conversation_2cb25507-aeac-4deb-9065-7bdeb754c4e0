<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

$time1 = mktime(0, 0, 0, 1, 1, 2022); // 01/01/2022 1640970000
$time2 = mktime(0, 0, 0, 8, 1, 2022); // 08/01/2022 1659286800

$minid = $db->query("SELECT MIN(id) FROM `nv4_vi_bidding_plans` WHERE " . $time1 . " <= addtime AND addtime < " . $time2)->fetchColumn();
$start = $minid-1;
$id = $start;
$dem = 0;
try {

    $maxid = $db->query("SELECT MAX(id) FROM `nv4_vi_bidding_plans` WHERE " . $time1 . " <= addtime AND addtime < " . $time2)->fetchColumn();
    echo "max $maxid\n";

    do {
        $num_rows = 0;
        $id2 = $id + 1000;

        $query_url = $db->query("SELECT * FROM `nv4_vi_bidding_plans` WHERE id > " . $id . " AND id < " . $id2 . " ORDER BY `id` ASC LIMIT 100");
        $id = $start;
        
        $violated_ids = [];
        $non_violated_ids = [];
        $params = [];
        
        while ($item = $query_url->fetch()) {
            $is_violated = 0;
            
            // Tìm $arr_contract
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_plans_contract WHERE id_plan IN (' . $item['id'] . ')');
            $arr_contract = $result->fetchAll();
            
            // Chỉ tính những gói thầu trong phạm vi điều chỉnh của Luật Đấu thầu
            $arr_contract = array_filter($arr_contract, fn($t) => $t['process_apply'] == 'LDT');
            // Tính tổng
            $count_contract_ldt = count($arr_contract);
                
            // Tổng số gói thầu thực hiện qua mạng
            $arr_internet = array_filter($arr_contract, function($contract) use ($item)
            {
                if (empty($contract['type_choose_list'])) return false;
                $type_list = is_string($contract['type_choose_list']) ? explode(',', $contract['type_choose_list']) : $contract['type_choose_list'];
                return $item['is_new_msc'] ? 
                $contract['is_internet'] :
                in_array('6', $type_list);
            });
            $count_internet = count($arr_internet);
            
            if ($count_contract_ldt > 0) {
                // a) Tổ chức lựa chọn nhà thầu qua mạng tối thiểu 70% các gói thầu thuộc phạm vi điều chỉnh của Luật đấu thầu
                if ($count_internet / $count_contract_ldt * 100 < 70) {
                    $is_violated = 1;
                }
                
                // b) Tổ chức lựa chọn nhà thầu qua mạng toàn bộ (100%) gói thầu sử dụng vốn nhà nước để mua sắm nhằm duy trì hoạt động thường xuyên của cơ quan nhà nước, 
                // tổ chức chính trị, tổ chức chính trị - xã hội, tổ chức chính trị xã hội - nghề nghiệp, tổ chức xã hội - nghề nghiệp, 
                // tổ chức xã hội, đơn vị thuộc lực lượng vũ trang nhân dân, đơn vị sự nghiệp công lập;
                if ($item['classify'] == 'TX') {
                    if ($count_internet < $count_contract_ldt) {
                        $is_violated = 1;
                    }
                }
            }
            
            if ($is_violated) {
                $violated_ids[] = $item['id'];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'dauthau_plans',
                        '_id' => $item['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 1
                    ]
                ];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'en_dauthau_plans',
                        '_id' => $item['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 1
                    ]
                ];
            } else {
                $non_violated_ids[] = $item['id'];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'dauthau_plans',
                        '_id' => $item['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 0
                    ]
                ];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'en_dauthau_plans',
                        '_id' => $item['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 0
                    ]
                ];
            }
            
            $id = $item['id'];
            $num_rows++;
            $dem++;
        }
        $query_url->closeCursor();
        
        if (!empty($violated_ids)) {
            $str_ids = implode(',', $violated_ids);
            $db->exec('UPDATE nv4_vi_bidding_plans SET is_violated = 1 WHERE id IN (' . $str_ids . ')');
            $db->exec('UPDATE nv4_en_bidding_plans SET is_violated = 1 WHERE id IN (' . $str_ids . ')');
        }
        
        if (!empty($non_violated_ids)) {
            $str_ids = implode(',', $non_violated_ids);
            $db->exec('UPDATE nv4_vi_bidding_plans SET is_violated = 0 WHERE id IN (' . $str_ids . ')');
            $db->exec('UPDATE nv4_en_bidding_plans SET is_violated = 0 WHERE id IN (' . $str_ids . ')');
        }
        
        if (!empty($params['body'])) {
            $hosts = array(
                $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
            );
    
            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();
            $responses = $client->bulk($params)->asArray();
            $params = null;
            $responses = null;
            $client = null;
        }

        echo 'Fix violate Plan id: ' . number_format($id) . '/' . number_format($maxid) . PHP_EOL;
        if ($id == $start and $id2 < $maxid) {
            $id = $id2;
        } elseif ($id == $start and $id2 >= $maxid) {
            break;
        }
    } while ($id < $maxid);
} catch (PDOException $e) {
    trigger_error(print_r($e, true));
    die($e->getMessage());
}
echo "Totally fixed violated Plan: " . $dem . " - Kết thúc!!!\n\n";
