#!/bin/sh

#Cài đặt aws-cli theo hướng dẫn tại Bước 3: https://wiki.vinades.vn/tai-lieu:ky-thuat:change:amazon-s3?s[]=aws#bu%E1%BB%9Bc_3installing_aws-cli_on_centos_7

chmod 777 /root/ses-delete-suppressed/ses-delete-suppressed-email.sh
aws sesv2 list-suppressed-destinations > /root/ses-delete-suppressed/ses-delete-suppressed.json
chmod 777 /root/ses-delete-suppressed/ses-delete-suppressed.json
sleep 1
php /root/ses-delete-suppressed/ses-delete-suppressed.php
sleep 1
bash /root/ses-delete-suppressed/ses-delete-suppressed-email.sh

#Xóa file smartycache của marketing.dauthau.info
find /home/<USER>/web/marketing.dauthau.info/public_html/data/cache/smarty-compile -type f -mtime +5 -name '*.php' | xargs /bin/rm -f

#Xóa các file tạm của tool bóc tin
find /home/<USER>/domains/dauthau.info/private/crawls/error_dom -type f -mtime +30 | xargs /bin/rm -f
find /home/<USER>/domains/dauthau.info/private/logs -type f -mtime +30 | xargs /bin/rm -f
find /home/<USER>/domains/dauthau.info/private/update/data -type f -mtime +30 | xargs /bin/rm -f