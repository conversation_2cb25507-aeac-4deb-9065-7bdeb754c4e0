<?php

/**
 * @Project DauThau
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License commercial
 * @Createdate 26/03/2021, 10:36
 */
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';
// Tool đưa alias của dự án phát triển lên es
$id = 0;
try {
    do {
        print_r("Xử lý id: " . $id . "\n");
        $run = 0;
        $params = [];
        $result = $db->query('SELECT id, code, name, alias FROM ' . NV_PREFIXLANG . '_project_investment WHERE id > ' . $id . ' ORDER BY id ASC LIMIT 500');
        while ($row = $result->fetch()) {
            $run++;
            if (empty($row['alias'])) {
                $row['alias'] = nv_clean_alias($row['name'], 255, false);
                if (empty($row['alias'])) {
                    $row['alias'] = nv_clean_alias($row['code']);
                }
                $db->query('UPDATE ' . NV_PREFIXLANG . '_project_investment SET `alias` = ' . $db->quote($row['alias']) . ' WHERE `id` = ' . $row['id']);
            }
            $params['body'][] = [
                'update' => [
                    '_index' => NV_LANG_ELASTIC . 'dauthau_project_investment',
                    '_id' => $row['id']
                ]
            ];
            $params['body'][] = [
                'doc' => [
                    'alias' => $row['alias']
                ]
            ];
            $id = $row['id'];
        }
        $result->closeCursor();
        if (!empty($params['body'])) {
            $hosts = array(
                $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
            );

            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();
            $responses = $client->bulk($params)->asArray();
            if (!empty($responses['items'])) {
                foreach ($responses['items'] as $value) {
                    if ($value['update']['status'] != 200) {
                        print_r("Xử lý id: \033[33m" . $value['update']['_id']);
                        print_r("\033[31m ERROR\033[0m\n");
                        file_put_contents(NV_ROOTDIR . '/tools/devprojects_alias_to_es_error.log', NV_LANG_DATA . ' ' . $value['update']['_id'] . PHP_EOL, FILE_APPEND);
                    }
                }
            }
        }
    } while ($run > 0);
} catch (Exception $e) {
    print_r($e);
    exit();
}

function nv_clean_alias($string, $num = 110, $lower = true)
{
    $search = [
        '&amp;',
        '&#039;',
        '&quot;',
        '&lt;',
        '&gt;',
        '&#x005C;',
        '&#x002F;',
        '&#40;',
        '&#41;',
        '&#42;',
        '&#91;',
        '&#93;',
        '&#33;',
        '&#x3D;',
        '&#x23;',
        '&#x25;',
        '&#x5E;',
        '&#x3A;',
        '&#x7B;',
        '&#x7D;',
        '&#x60;',
        '&#x7E;'
    ];
    $replace = [
        '&',
        '\'',
        '"',
        '<',
        '>',
        '\\',
        '/',
        '(',
        ')',
        '*',
        '[',
        ']',
        '!',
        '=',
        '#',
        '%',
        '^',
        ':',
        '{',
        '}',
        '`',
        '~'
    ];

    $string = str_replace($search, $replace, $string);
    $string = nv_compound_unicode($string);
    $string = nv_EncString($string);
    $string = preg_replace(array(
        '/[^a-zA-Z0-9]/',
        '/[ ]+/',
        '/^[\-]+/',
        '/[\-]+$/'
    ), array(
        ' ',
        '-',
        '',
        ''
    ), $string);

    $len = strlen($string);
    if ($num and $len > $num) {
        $_substring = substr($string, 0, $num);
        while (str_contains($_substring, '-') and substr($string, $num, 1) != '-') {
            --$num;
            $_substring = substr($_substring, 0, $num);
        }
        $string = substr($string, 0, $num);
    }

    if ($lower) {
        $string = strtolower($string);
    }
    return $string;
}
die("END\n");
