<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */

/*
 * Thuật toán như sau:
 * - Bảng nv4_vi_bidding_kqlcnt_logs có trường
 * - status: Trạng thái bằng 0 là đang chạy, nếu truy cập lại thì tiếp tục chạy.
 * - user: ID của users đã chạy đến, Mỗi lần tìm kiếm chỉ khoảng 2 tài khoản để test. (Sau đó có thể nâng lên 50 khi số lựong nhiều)
 * - Bảng nv4_vi_bidding_kqlcnt_logs được thêm mới khi không tìm thấy status = 0 và có thêm bài đăng.
 * - Khi tìm kiếm theo từng tài khỏan, sẽ tìm lần luợt bộ lọc của từng người, nếu tìm thấy thì lưu vào bảng nv4_vi_bidding_mail để chương trình send_mail.php lấy ra để gửi.
 */
$arr_field = array(
    1 => 'Hàng hóa',
    2 => 'Xây lắp',
    3 => 'Tư vấn',
    4 => 'Phi tư vấn',
    5 => 'Hỗn hợp'
);
$filters_mail_kqlcnt_log = NV_ROOTDIR . '/data/filters_mail_kqlcnt_log_' . $prefix_lang . '_' . date('Ymd') . '.txt';
$new_loop = 0;
$arr_id_vip = array();
$arr_vip = array();
// Bước 1: Kiểm tra có bảng logs nào đang chạy không, nếu không thì bắt đầu chạy mới, nếu có thì tiếp tục chạy đến userid tiếp theo
try {
    $logs_info = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs WHERE status=0 limit 1')->fetch();
} catch (PDOException $e) {
    print_r('Lỗi select logs');
    trigger_error($e->getMessage());
    die($e->getMessage()); // Remove this line after checks finished
}

if (!empty($logs_info)) {
    $start_id = $logs_info['from_id'];
    $end_id = $logs_info['to_id'];
    $last_userid = $logs_info['userid'];
} else {
    // Nếu không có phần nào đang chạy thì kiểm tra xem có tin đấu thầu mới không. Nếu có tin mới thì chạy vòng lặp mới, nếu không thì kết thúc
    $last_logs = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs ORDER BY id DESC limit 1')->fetch();
    $info = $db->query('SELECT max(id) as to_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result')->fetch();
    if (empty($last_logs)) {
        $start_id = 0; // Chạy lần đầu
        $end_id = $info['to_id'];
        $last_userid = 0;
        $new_loop = 1;
    } else {
        if ($last_logs['to_id'] < $info['to_id']) {
            // Nếu có tin thầu mới hoặc kế hoạch chọn nhà thầu mới, thông báo mời sơ tuyển mới thì lấy ra các bài mới để chạy vòng lặp mới
            $last_userid = 0;
            $start_id = $last_logs['to_id']; // Lấy id tin mời thầu của lần cuối cùng chạy
            $end_id = $info['to_id']; // Lấy id mời thầu lớn nhất của dữ liệu hiện có
            $new_loop = 1;
        } else {
            // Nếu không có dữ liệu mới để chạy thì dừng lại ngay
            file_put_contents($filters_mail_kqlcnt_log, "Lỗi select logs L71: Không có tin mới trong csdl. Hãy chạy tiếp khi có bài đăng mới\n", FILE_APPEND);
            die('Không có tin mới');
        }
    }
}

$time = time();
$start_time = $time;
if ($new_loop) {
    try {
        // thêm logs mới vào csdl
        $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs (from_id, to_id, from_time, to_time, total_time, userid, status) VALUES (:from_id, :to_id, :from_time, :to_time, 0, 0, 0)');
        $stmt->bindParam(':from_id', $start_id, PDO::PARAM_INT);
        $stmt->bindParam(':to_id', $end_id, PDO::PARAM_INT);
        $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
        $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
        $exc = $stmt->execute();
        $logs_id = $db->lastInsertId();
        $run_time = 0;
    } catch (PDOException $e) {
        file_put_contents($filters_mail_kqlcnt_log, "Lỗi thêm logs mới vào csdl L92: " . print_r($e, true) . "\n", FILE_APPEND);
        die('Lỗi thêm logs mới');
    }
} else {
    $logs_id = $logs_info['id'];
    $run_time = $logs_info['total_time'];
}

// Bước 2: Lấy ra ds các VIP mỗi lần 50 người
// 2.1 Lấy danh sách các VIP còn hạn sử dụng
$number_user_scan_all = 100;
$time_start_thisday = mktime(date('H'), date('i'), date('s'), date('n'), date('j'), date('Y'));
$query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id >' . $last_userid . ' AND vip = 7 AND prefix_lang = ' . $prefix_lang . ' ORDER BY user_id ASC limit ' . $number_user_scan_all);
$number_user_scan = 0;
$user_id_end = 0;
while ($vip_info = $query_vip->fetch()) {
    ++$number_user_scan;
    $user_id_end = $vip_info['user_id'];
    $arr_vip[$vip_info['user_id']]['from_time'] = $vip_info['from_time'];
    $arr_vip[$vip_info['user_id']]['to_time'] = $vip_info['end_time'];
    $arr_vip[$vip_info['user_id']]['filter'] = array();
    $arr_vip[$vip_info['user_id']]['vip'][$vip_info['vip']] = $vip_info['vip'];
    $arr_id_vip[$vip_info['user_id']] = $vip_info['user_id'];
}
$arr_id_all = $arr_id_vip;

// Bước 3: Lấy ra ds các bộ lọc của VIP đc chọn
if (!empty($arr_id_all)) {
    $query_filter = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE userid IN (' . implode(',', $arr_id_all) . ') AND status=1 AND vip_use=7 AND prefix_lang = ' . $prefix_lang . '');
    while ($result = $query_filter->fetch()) {
        if (in_array($result['vip_use'], $arr_vip[$result['userid']]['vip'])) {
            $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
        } else if (isset($arr_vip[$result['userid']]['filter'][$result['id']])) {
            $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
        }
    }
}

// Bước 4: Lấy ra các tin trong ds tin mới mà thỏa mãn bộ lọc tương ứng với từng VIP để thêm vào bảng _tmp
if (empty($arr_vip)) {
    // Nếu không lấy được VIP nào thì coi như đã chạy xong một vòng và update lại status là đã chạy xong
    print_r("không có gói vip nào");
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $change = $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_kqlcnt_log, "Đã quét xong một lượt các vip. L172 Tổng thời gian cho một lượt:" . $total_time . "\n", FILE_APPEND);
    die();
} else {
    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs SET userid =' . $user_id_end . ' WHERE id =' . $logs_id);
    $db->beginTransaction();
    try {
        foreach ($arr_vip as $vip_id => $vip) {
            echo "Bắt đầu quét:" . $vip_id . "\n";
            file_put_contents($filters_mail_kqlcnt_log, "L175 Bắt đầu quét:" . $vip_id . "\n", FILE_APPEND);
            // Lấy ra danh sách các tin mời thầu, KHLCNT, TBMST thỏa mãn điều kiện tìm kiếm từ các bộ lọc
            if (!empty($vip['filter'])) {
                foreach ($vip['filter'] as $filter) {
                    if (!empty($filter)) {
                        // Từ bộ lọc chọn ra điều kiện lọc where tương ứng
                        $where = 'id>' . $start_id . ' AND id<=' . $end_id . ' AND type_kqlcnt != 2'; // Điều kiện lọc tin mời thầu
                        if ($filter['time_find'] > 0) {
                            $time_find = mktime(0, 0, 0, date('n'), date('j'), date('Y')) - 86400 * $filter['time_find'];
                            $where .= ' AND finish_time >=' . $time_find;
                        }
                        if ($filter['cat'] > 0) {
                            $where .= $filter['cat'] == 1 ? " AND type_data=1" : " AND type_data=0";
                        }

                        if ($filter['type_kqlcnt'] == 1) {
                            $where .= ' AND type_kqlcnt=0';
                        } elseif ($filter['type_kqlcnt'] == 2) {
                            $where .= ' AND type_kqlcnt>0';
                        }
                        // Tìm kiếm theo tỉnh thành
                        if (!empty($filter['idprovince'])) {
                            $where .= ' AND id_province IN (' . $filter['idprovince'] . ')';
                        }
                        if ($filter['price_plan_from'] > 0) {
                            $where .= " AND bid_price_number >= " . $filter['price_plan_from'];
                        }
                        if ($filter['price_plan_to'] > 0) {
                            $where .= " AND bid_price_number <= " . $filter['price_plan_to'];
                        }
                        if ($filter['win_price_from'] > 0) {
                            $where .= " AND win_price_number >= " . $filter['win_price_from'];
                        }
                        if ($filter['win_price_to'] > 0) {
                            $where .= " AND win_price_number <= " . $filter['win_price_to'];
                        }
                        if ($filter['type_bid'] > 0) {
                            $where .= " AND type_bid_id = " . $filter['type_id'];
                        }
                        if ($filter['type_choose_id'] > 0) {
                            $where .= " AND type_choose_id = " . $filter['type_choose_id'];
                        }
                        if ($filter['bidfieid'] != '') {
                            $where .= " AND bidfieid IN (" . $filter['bidfieid'] . ")";
                        }

                        if (!empty($filter['without_key'])) {
                            // Tìm theo từ khóa loại trừ
                            $arr_key = explode(',', $filter['without_key']);
                            foreach ($arr_key as $key) {
                                if ($filter['search_type'] == 1) {
                                    $key = trim($key);
                                    $where .= " AND content_full NOT LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
                                } else {
                                    $key = str_replace('-', ' ', change_alias($key));
                                    $key = trim($key);
                                    if ($filter['good_search'] == 1 || $filter['good_search'] == 2) {
                                        $where .= " AND content_goods NOT LIKE " . $db->quote('%' . $key . '%') . " OR content NOT LIKE " . $db->quote('%' . $key . '%');
                                    } else {
                                        $where .= " AND content NOT LIKE " . $db->quote('%' . $key . '%');
                                    }
                                }
                            }
                        }

                        if (!empty($filter['key_search2'])) {
                            // Tìm theo từ khóa cùng
                            $arr_key = explode(',', $filter['key_search2']);
                            $operators = !empty($filter['search_one_key']) ? ' OR ' : ' AND ';
                            foreach ($arr_key as $key) {
                                if ($filter['search_type'] == 1) {
                                    $key = trim($key);
                                    $where .= $operators . "content_full LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
                                } else {
                                    $key = str_replace('-', ' ', change_alias($key));
                                    $key = trim($key);
                                    if ($filter['good_search'] == 1) {
                                        $where .= $operators . "(content_goods LIKE " . $db->quote('%' . $key . '%') . " OR content LIKE " . $db->quote('%' . $key . '%') . ')';
                                    } elseif ($filter['good_search'] == 2) {
                                        $where .= $operators . 'content_goods LIKE ' . $db->quote('%' . $key . '%');
                                    } else {
                                        $where .= $operators . "content LIKE " . $db->quote('%' . $key . '%');
                                    }
                                }
                            }
                        }

                        if (!empty($filter['key_search'])) {
                            $where .= ' AND (';
                            $num = 0;
                            $arr_key = explode(',', $filter['key_search']);
                            foreach ($arr_key as $key) {
                                if ($filter['search_type'] == 1) {
                                    $key = trim($key);
                                    if ($num == 0) {
                                        $where .= "content_full LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
                                    } else {
                                        $where .= " OR content_full LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
                                    }
                                } else {
                                    $key = str_replace('-', ' ', change_alias($key));
                                    $key = trim($key);
                                    if ($num == 0) {
                                        if ($filter['good_search'] == 1) {
                                            $where .= "(content_goods LIKE " . $db->quote('%' . $key . '%') . " OR content LIKE " . $db->quote('%' . $key . '%') . ')';
                                        } elseif ($filter['good_search'] == 2) {
                                            $where .= "content_goods LIKE " . $db->quote('%' . $key . '%');
                                        } else {
                                            $where .= "content LIKE " . $db->quote('%' . $key . '%');
                                        }
                                    } else {
                                        if ($filter['good_search'] == 1) {
                                            $where .= " OR (content_goods LIKE " . $db->quote('%' . $key . '%') . " OR content LIKE " . $db->quote('%' . $key . '%') . ')';
                                        } elseif ($filter['good_search'] == 2) {
                                            $where .= ' OR content_goods LIKE ' . $db->quote('%' . $key . '%');
                                        } else {
                                            $where .= " OR content LIKE " . $db->quote('%' . $key . '%');
                                        }
                                    }
                                }
                                $num++;
                            }
                            $where .= ')';
                        }

                        // Select tin đầu thầu thỏa mãn từng bộ lọc
                        $sql = 'SELECT id from ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result WHERE ' . $where;
                        try {
                            $result = $db->query($sql);
                        } catch (PDOException $e) {
                            print_r('Lỗi chọn tin thầu thỏa mãn bộ lọc');
                            echo '<pre>';
                            print_r($e);
                            echo '</pre>';
                            die($sql); // Remove this line after checks finished
                        }
                        $array_id = array();
                        $num_row = 0;
                        while ($bidding = $result->fetch()) {
                            $num_row++;
                            $stmt = $db->prepare('INSERT IGNORE INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bidkqlc_id(userid, filter_id, bid_id, send_status, addtime) VALUES (:userid, :filter_id, :bid_id, 0, :addtime)');
                            $time = NV_CURRENTTIME;
                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                            $stmt->bindParam(':bid_id', $bidding['id'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                            $exc = $stmt->execute();
                        }

                        // ghi log bộ lọc
                        if ($num_row > 0) {
                            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastresult = ' . NV_CURRENTTIME . ', stat_totalresult = stat_totalresult + ' . $num_row . ' WHERE id=' . $filter['id']);
                        }
                    }
                }
            }
            // ghi lại logs cho quá trình tìm kiếm tin cho mỗi vip
            $time = NV_CURRENTTIME;
            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs SET to_time = :to_time, userid = :userid WHERE id =' . $logs_id);
            $_vip_id = ($vip_id >= $user_id_end) ? $user_id_end : $vip_id;
            $stmt->bindParam(':userid', $_vip_id, PDO::PARAM_INT);
            $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
            $exc = $stmt->execute();
        }
        $db->commit();
    } catch (PDOException $e) {
        file_put_contents($filters_mail_kqlcnt_log, "Lỗi INSERT  L401:\n" . print_r($e, true) . "\n", FILE_APPEND);
        $db->rollBack();
        notify_to_slack($filters_mail_kqlcnt_log, "filters_mail_kqlcnt_mysql.php Lỗi INSERT  L401:\n" . print_r($e, true) . "\n");
        die(); // Remove this line after checks finished
    }
    // Sau khi chạy xong một vòng 50 VIP thì thông báo chạy xong và thời gian chạy cho một vòng
    $time = time();
    $this_time = $time - NV_CURRENTTIME;
    $total_time = intval($run_time + $this_time);
    try {
        if ($number_user_scan < $number_user_scan_all) {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ', status=1 where id =' . $logs_id);
        } else {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ' where id =' . $logs_id);
        }
        if (sizeof($arr_id_vip)) {
            $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs  SET filters_mail = ' . NV_CURRENTTIME . ' WHERE user_id IN (' . implode(',', $arr_id_vip) . ') AND vip= 7 AND prefix_lang = ' . $prefix_lang . '');
        }
    } catch (PDOException $e) {
        file_put_contents($filters_mail_kqlcnt_log, "Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP  L415:\n" . print_r($e, true) . "\n", FILE_APPEND);
        notify_to_slack($filters_mail_kqlcnt_log, "filters_mail_kqlcnt_mysql.php Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP  L415:\n" . print_r($e, true) . "\n");
        die();
    }

    $note = 'L420 Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
    file_put_contents($filters_mail_kqlcnt_log, $note . "\n", FILE_APPEND);
    die($note . "\n");
}
