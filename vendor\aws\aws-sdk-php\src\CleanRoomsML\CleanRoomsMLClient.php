<?php
namespace Aws\CleanRoomsML;

use Aws\AwsClient;

/**
 * This client is used to interact with the **cleanrooms-ml** service.
 * @method \Aws\Result createAudienceModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createAudienceModelAsync(array $args = [])
 * @method \Aws\Result createConfiguredAudienceModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createConfiguredAudienceModelAsync(array $args = [])
 * @method \Aws\Result createTrainingDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createTrainingDatasetAsync(array $args = [])
 * @method \Aws\Result deleteAudienceGenerationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAudienceGenerationJobAsync(array $args = [])
 * @method \Aws\Result deleteAudienceModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteAudienceModelAsync(array $args = [])
 * @method \Aws\Result deleteConfiguredAudienceModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConfiguredAudienceModelAsync(array $args = [])
 * @method \Aws\Result deleteConfiguredAudienceModelPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConfiguredAudienceModelPolicyAsync(array $args = [])
 * @method \Aws\Result deleteTrainingDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteTrainingDatasetAsync(array $args = [])
 * @method \Aws\Result getAudienceGenerationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAudienceGenerationJobAsync(array $args = [])
 * @method \Aws\Result getAudienceModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAudienceModelAsync(array $args = [])
 * @method \Aws\Result getConfiguredAudienceModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConfiguredAudienceModelAsync(array $args = [])
 * @method \Aws\Result getConfiguredAudienceModelPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConfiguredAudienceModelPolicyAsync(array $args = [])
 * @method \Aws\Result getTrainingDataset(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTrainingDatasetAsync(array $args = [])
 * @method \Aws\Result listAudienceExportJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAudienceExportJobsAsync(array $args = [])
 * @method \Aws\Result listAudienceGenerationJobs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAudienceGenerationJobsAsync(array $args = [])
 * @method \Aws\Result listAudienceModels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAudienceModelsAsync(array $args = [])
 * @method \Aws\Result listConfiguredAudienceModels(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listConfiguredAudienceModelsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listTrainingDatasets(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTrainingDatasetsAsync(array $args = [])
 * @method \Aws\Result putConfiguredAudienceModelPolicy(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putConfiguredAudienceModelPolicyAsync(array $args = [])
 * @method \Aws\Result startAudienceExportJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startAudienceExportJobAsync(array $args = [])
 * @method \Aws\Result startAudienceGenerationJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startAudienceGenerationJobAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateConfiguredAudienceModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateConfiguredAudienceModelAsync(array $args = [])
 */
class CleanRoomsMLClient extends AwsClient {}
