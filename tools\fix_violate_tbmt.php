<?php

/*
    @issue: https://vinades.org/dauthau/dauthau.info/-/issues/951?show=7912
*/

// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

$time1 = mktime(0, 0, 0, 1, 1, 2022); // 01/01/2022 1640970000
$time2 = mktime(0, 0, 0, 8, 1, 2022); // 08/01/2022 1659286800

$minid = $db->query("SELECT MIN(id) FROM `nv4_vi_bidding_row` WHERE " . $time1 . " <= ngay_dang_tai AND ngay_dang_tai < " . $time2)->fetchColumn();
$start = $minid-1;
$id = $start;
$dem = 0;
try {

    $maxid = $db->query("SELECT MAX(id) FROM `nv4_vi_bidding_row` WHERE " . $time1 . " <= ngay_dang_tai AND ngay_dang_tai < " . $time2)->fetchColumn();
    
    do {
        $num_rows = 0;
        $id2 = $id + 1000;

        $query_url = $db->query("SELECT * FROM `nv4_vi_bidding_row` WHERE id > " . $id . " AND id < " . $id2 . " ORDER BY `id` ASC LIMIT 100");
        $id = $start;
        
        $violated_ids = [];
        $non_violated_ids = [];
        $params = [];
        
        while ($bidding_row = $query_url->fetch()) {
            $is_violated = 0;
            $trong_pham_vi = true;

            if ($bidding_row['pham_vi'] == 'Ngoài phạm vi điều chỉnh của Luật đấu thầu' or $bidding_row['pham_vi'] == 1 or $bidding_row['type_bid'] == 1) $trong_pham_vi = false;

            if ($trong_pham_vi) {
                $pattern = "/mua sắm tập trung/i";
                if (preg_match($pattern, $bidding_row['goi_thau'])) {
                    $is_violated = 1;
                } else {
                    // Tìm ở tên kế hoạch
                    $id_plan = $db->query("SELECT id_plan FROM `nv4_vi_bidding_plans_contract` WHERE tbmt_id = " . $bidding_row['id'])->fetchColumn();
                    
                    if ($id_plan) {
                        $plan_name = $db->query("SELECT title FROM `nv4_vi_bidding_plans` WHERE id = " . $id_plan)->fetchColumn();
                        preg_match($pattern, $plan_name) && $is_violated = 1;
                    }
                }
            }
            
            if ($is_violated) {
                $violated_ids[] = $bidding_row['id'];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'dauthau_bidding',
                        '_id' => $bidding_row['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 1,
                    ]
                ];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'en_dauthau_bidding',
                        '_id' => $bidding_row['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 1,
                    ]
                ];
            } else {
                $non_violated_ids[] = $bidding_row['id'];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'dauthau_bidding',
                        '_id' => $bidding_row['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 0,
                    ]
                ];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'en_dauthau_bidding',
                        '_id' => $bidding_row['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 0,
                    ]
                ];
            }
            
            $id = $bidding_row['id'];
            $num_rows++;
            $dem++;
        }
        $query_url->closeCursor();
        
        if (!empty($violated_ids)) {
            $str_ids = implode(',', $violated_ids);
            $db->exec('UPDATE nv4_vi_bidding_row SET is_violated = 1 WHERE id IN (' . $str_ids . ')');
            $db->exec('UPDATE nv4_en_bidding_row SET is_violated = 1 WHERE id IN (' . $str_ids . ')');
        }
        
        if (!empty($non_violated_ids)) {
            $str_ids = implode(',', $non_violated_ids);
            $db->exec('UPDATE nv4_en_bidding_row SET is_violated = 0 WHERE id IN (' . $str_ids . ')');
            $db->exec('UPDATE nv4_vi_bidding_row SET is_violated = 0 WHERE id IN (' . $str_ids . ')');
        }
        
        if (!empty($params['body'])) {
            $hosts = array(
                $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
            );
    
            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();
            $responses = $client->bulk($params)->asArray();
            $params = null;
            $responses = null;
            $client = null;
        }

        echo 'Fix violate TBMT id: ' . number_format($id) . '/' . number_format($maxid) . PHP_EOL;
        if ($id == $start and $id2 < $maxid) {
            $id = $id2;
        } elseif ($id == $start and $id2 >= $maxid) {
            break;
        }
    } while ($id < $maxid);
} catch (PDOException $e) {
    trigger_error(print_r($e, true));
    die($e->getMessage());
}
echo "Totally fix violate TBMT: " . $dem . " - Kết thúc!!!\n\n";
