<?php

function call_api($connect, $module, $action, $data = [])
{
    $api_remote_url = $connect['api_url'];
    $agent = 'NukeViet Remote API Lib';

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $api_remote_url);
    curl_setopt($ch, CURLOPT_HEADER, 0);

    $safe_mode = (ini_get('safe_mode') == '1' || strtolower(ini_get('safe_mode')) == 'on') ? 1 : 0;
    $open_basedir = ini_get('open_basedir') ? true : false;
    if (!$safe_mode and !$open_basedir) {
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
    }

    $apikey = $connect['api_key'];
    $apisecret = $connect['api_secret'];
    $timestamp = time();
    $arr_connect = [
        'apikey' => $apikey,
        'timestamp' => $timestamp,
        'hashsecret' => password_hash($apisecret . '_' . $timestamp, PASSWORD_DEFAULT),
        'language' => 'vi',
        'action' => $action,
        'module' => $module
    ];
    $request = array_merge($arr_connect, $data);

    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_USERAGENT, $agent);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_POST, sizeof($request));
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($request));
    $res = curl_exec($ch);
    curl_close($ch);
    $response = json_decode($res, true);
    return $response;
}

function insertInform($userid, $arrMess, $alias) {
    $connect_api = [
        'api_url' => ID_API_URL,
        'api_key' => ID_API_USER_KEY,
        'api_secret' => ID_API_USER_SECRET
    ];

    // Thực hiện thông báo cho khách hàng
    $params['operation'] = 'add';
    $params['receiver_type'] = 'ids';
    $params['receiver_ids'] = $userid;
    $params['isdef'] = 'vi';
    $params['message'] = $arrMess;
    $params['add_time'] = date('d/m/Y');
    $params['add_hour'] = date('H');
    $params['add_min'] = date('i');
    $params['link'] = [
        'vi' => $alias,
        'en' => $alias,
    ];

    $result = call_api($connect_api, 'inform', 'InformAction', $params);
    if (!empty($result['status']) and $result['status'] != 'success') {
        return 0;
    } else {
        return 1;
    }

    // Push notification
    // $arrMess = [
    //     'vi' => 'test222',
    //     'en' => 'test2222',
    // ];
    // $resPush = insertInform($userid, $arrMess);
}

function insertMutiInform($arrInfo) {
    $connect_api = [
        'api_url' => ID_API_URL,
        'api_key' => ID_API_USER_KEY,
        'api_secret' => ID_API_USER_SECRET
    ];

    // Thực hiện thông báo cho khách hàng
    $params['operation'] = 'add';
    $params['receiver_type'] = 'ids';
    $params['isdef'] = 'vi';
    $params['add_time'] = date('d/m/Y');
    $params['add_hour'] = date('H');
    $params['add_min'] = date('i');
    $params['list_info'] = $arrInfo;
    // $params['exp_time'] = date('d/m/Y', time() + 7 * 60 * 60 * 24);
    // $params['receiver_ids'] = $userid;

    $result = call_api($connect_api, 'inform', 'InformMutiAction', $params);
    if (!empty($result['status']) and $result['status'] == 'success') {
        return [
            'status' => $result['status']
        ];
    } else {
        return [
            'status' => $result['status'],
            'error' => $result['error_detail'],
            'key_error' => $result['key_error']
        ];
    }
}


function get_lang($lang, $key) {
    $arrlang = [
        'vi' => [
            'title_notification_khlcnt_online' => 'Có tin mới KHLCNT ngày %s',
            'title_search_by_filter' => 'Có Tin mới từ bộ lọc: %s',
            'title_search_by_result' => 'Có Tin mới kết quả lựa chọn nhà thầu: %s',
            'title_search_by_result_open' => 'Có Tin mới kết quả mở thầu: %s',
            'title_search_by_notify' => 'Có Thay đổi/đính chính ngày %s: %s',
            'title_search_by_huy_thau' => 'Huỷ thầu: %s',
            'title_new_tbmt' => 'Có tin mới: %s',
            'title_new_khlcnt' => 'Có Kế hoạch lựa chọn nhà thầu ngày %s: %s',
            'title_new_tbmt_1' => 'Có tin mới đấu thầu ngày %s: %s',
            'title_new_khlcnt_qt' => 'Có Kế hoạch lựa chọn nhà thầu Quốc tế ngày %s: %s',
            'title_search_by_ycbg' => 'Có Yêu cầu báo giá mới từ bộ lọc: %s',
            'title_new_ycbg' => 'Có Yêu cầu báo giá mới: %s',
        ],
        'en' => [
            'title_notification_khlcnt_online' => 'There is a new news on KHLCNT date %s',
            'title_search_by_filter' => 'New from filter: %s',
            'title_search_by_result' => 'Yes News contractor selection results: %s',
            'title_search_by_result_open' => 'Yes News Bidding result: %s',
            'title_search_by_notify' => 'Yes Date change/correction %s: %s',
            'title_search_by_huy_thau' => 'Cancellation of bid: %s',
            'title_new_tbmt' => 'Breaking news: %s',
            'title_new_khlcnt' => 'There is a contractor selection plan on %s: %s',
            'title_new_tbmt_1' => 'There is a new auction date %s: %s',
            'title_new_khlcnt_qt' => 'There is an international contractor selection plan on %s: %s',
            'title_search_by_ycbg' => 'There is a new request quote from the filter: %s',
            'title_new_ycbg' => 'New quote request: %s',
        ],
    ];

    if (isset($arrlang[$lang]) and isset($arrlang[$lang][$key])) {
        return $arrlang[$lang][$key];
    }
    if (isset($arrlang['en']) and isset($arrlang['en'][$key])) {
        return $arrlang['en'][$key];
    }
    return $key;
}
