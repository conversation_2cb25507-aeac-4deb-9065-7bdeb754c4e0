<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
set_time_limit(0);

$id_arr = [];
$id_arr['vi'] = 0;
$id_arr['en'] = 0;
if (file_exists(NV_ROOTDIR . '/tools/update_content_code_KHLCNT.txt')) {
    $arr_id = file_get_contents(NV_ROOTDIR . '/tools/update_content_code_KHLCNT.txt');
    $arr_id = json_decode($arr_id, true);
    $id_arr['vi'] = intval($arr_id['vi']);
    $id_arr['en'] = intval($arr_id['en']);
}
try {

    $waitTimeoutInSeconds = 2;
    if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
        // It worked
        $elastic_online = 1;
    } else {
        // It didn't work
        $elastic_online = 0;
        echo "Server Elasticsearch didn't work: ";
        echo "ERROR: $errCode - $errStr<br />\n";
    }
    fclose($fp);
    if ($elastic_online) {

        $hosts = array(
            $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
        );
        $id_tbmt = $id_arr['vi'];
        do {

            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();

            $query_url = $db->query('SELECT * FROM nv4_vi_bidding_plans WHERE id > ' . $id_tbmt . ' AND is_new_msc =1 ORDER BY id ASC LIMIT 100');
            $params = [
                'body' => []
            ];
            $id_tbmt = 0;
            while ($row = $query_url->fetch()) {
                $id_tbmt = $row['id'];
                /*
                 * $solicitor = $db->query('SELECT title FROM nv4_bidding_solicitor WHERE id = ' . $row['solicitor_id'])->fetchColumn();
                 *
                 * $row['content_full'] = $row['code'] . ' ' . $row['title'] . ' ' . $row['investor'] . ' ' . $solicitor;
                 *
                 * $query_plans_contract = $db->query('SELECT title FROM nv4_vi_bidding_plans_contract WHERE id_plan = ' . $row['id'] . ' ORDER BY id ASC');
                 * while ($_plans_contract = $query_plans_contract->fetch()) {
                 * $row['content_full'] .= ' ' . $_plans_contract['title'];
                 * }
                 */

                // tách chuỗi PL2300107757-00 do content cũ chỉ lưu PL2300107757
                $code = explode('-', $row['code']);
                $row['content_full'] = str_replace($code[0], '', $row['content_full']);
                $row['content_full'] .= ' ' . $row['code']; // gán lại cho content_full

                $row['content'] = str_replace($code[0], '', $row['content']);
                $row['content'] .= ' ' . $row['code']; // gán lại cho content

                $db->query('UPDATE nv4_vi_bidding_plans SET content = ' . $db->quote($row['content']) . ', content_full = ' . $db->quote($row['content_full']) . ' WHERE id =' . $row['id']);

                $params['body'][] = [
                    'index' => [
                        '_index' => 'dauthau_plans',
                        '_id' => $row['id']
                    ]
                ];
                $params['body'][] = $row;
                echo "update VI id: " . $id_tbmt . " <br><br>";
            }
            $query_url->closeCursor();

            if (!empty($params['body'])) {
                $responses = $client->bulk($params)->asArray();
                if (empty($responses['errors'])) {
                    unset($responses['items']);
                    echo "update thành công es<br><br>\n";
                } else {
                    echo '<pre>';
                    print_r($responses);
                    echo '</pre>';
                }
            } else {
                echo 'Ko co du du lieu';
            }

            if ($id_tbmt > 0) {
                $id_arr['vi'] = $id_tbmt;
                file_put_contents(NV_ROOTDIR . '/tools/update_content_code_KHLCNT.txt', json_encode($id_arr));
            }
        } while ($id_tbmt > 0);

        // ENG
        $id_tbmt = $id_arr['en'];
        do {

            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();

            $query_url = $db->query('SELECT * FROM nv4_en_bidding_plans WHERE id > ' . $id_tbmt . ' AND is_new_msc =1 ORDER BY id ASC LIMIT 100');
            $params = [
                'body' => []
            ];
            $id_tbmt = 0;
            while ($row = $query_url->fetch()) {
                $id_tbmt = $row['id'];

                /*
                 * $solicitor = $db->query('SELECT title FROM nv4_bidding_solicitor WHERE id = ' . $row['solicitor_id'])->fetchColumn();
                 *
                 * $row['content_full'] = $row['code'] . ' ' . $row['title'] . ' ' . $row['investor'] . ' ' . $solicitor;
                 *
                 * $query_plans_contract = $db->query('SELECT title FROM nv4_en_bidding_plans_contract WHERE id_plan = ' . $row['id'] . ' ORDER BY id ASC');
                 * while ($_plans_contract = $query_plans_contract->fetch()) {
                 * $row['content_full'] .= ' ' . $_plans_contract['title'];
                 * }
                 */
                $code = explode('-', $row['code']);
                $row['content_full'] = str_replace($code[0], '', $row['content_full']);
                $row['content_full'] .= ' ' . $row['code']; // gán lại cho content_full

                $row['content'] = str_replace($code[0], '', $row['content']);
                $row['content'] .= ' ' . $row['code']; // gán lại cho content

                $db->query('UPDATE nv4_en_bidding_plans SET content = ' . $db->quote($row['content']) . ', content_full = ' . $db->quote($row['content_full']) . ' WHERE id =' . $row['id']);

                $params['body'][] = [
                    'index' => [
                        '_index' => 'en_dauthau_plans',
                        '_id' => $row['id']
                    ]
                ];
                $params['body'][] = $row;
                echo "update EN id: " . $id_tbmt . " <br><br>";
            }
            $query_url->closeCursor();

            if (!empty($params['body'])) {
                $responses = $client->bulk($params)->asArray();
                if (empty($responses['errors'])) {
                    unset($responses['items']);
                    echo "update thành công es<br><br>\n";
                } else {
                    echo '<pre>';
                    print_r($responses);
                    echo '</pre>';
                }
            } else {
                echo 'Ko co du du lieu';
            }

            if ($id_tbmt > 0) {
                $id_arr['en'] = $id_tbmt;
                file_put_contents(NV_ROOTDIR . '/tools/update_content_code_KHLCNT.txt', json_encode($id_arr));
            }
        } while ($id_tbmt > 0);
    }
} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
}
echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
