<?php

/**
 * Copy trườ<PERSON> phuong_thuc từ bảng bidding_detail sang bảng bidding_row, biến đổi thành số để tối ưu, đồng thời đẩy lên ES
 * Issue: https://vinades.org/dauthau/dauthau.info/-/issues/2534
 */

// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

$id = 718269;
$dem = 0;

$pt_num_map = [
    '1_mths' => 11,
    'một giai đoạn một túi hồ sơ' => 11,
    '1 giai đoạn 1 túi hồ sơ' => 11,

    '1_hths' => 12,
    'một giai đoạn hai túi hồ sơ' => 12,
    '1 giai đoạn 2 túi hồ sơ' => 12,

    '2_mths' => 21,
    'hai giai đoạn một túi hồ sơ' => 21,
    '2 giai đoạn 1 túi hồ sơ' => 21,

    '2_hths' => 22,
    'hai giai đoạn hai túi hồ sơ' => 22,
    '2 giai đoạn 2 túi hồ sơ' => 22,
];

try {

    $maxid = $db->query("SELECT MAX(id) FROM `nv4_vi_bidding_detail`")->fetchColumn();

    do {
        $num_rows = 0;
        $id2 = $id + 1000;
        $params = [];
        $query_url = $db->query("SELECT id, phuong_thuc FROM `nv4_vi_bidding_detail` WHERE id > " . $id . " AND id < " . $id2 . " ORDER BY `id` ASC LIMIT 100");
        $id = 0;
        $batches = [];

        while ($data = $query_url->fetch()) {
            $num_phuongthuc = 0;
            if ($data['phuong_thuc']) {
                $num_phuongthuc = $pt_num_map[strtolower(trim($data['phuong_thuc']))] ?? 0;
            }

            $batches[$num_phuongthuc][] = $data['id'];

            $params['body'][] = [
                'update' => [
                    '_index' => 'dauthau_bidding',
                    '_id' => $data['id']
                ]
            ];
            $params['body'][] = [
                'doc' => [
                    'phuong_thuc_num' => $num_phuongthuc
                ]
            ];
            $params['body'][] = [
                'update' => [
                    '_index' => 'en_dauthau_bidding',
                    '_id' => $data['id']
                ]
            ];
            $params['body'][] = [
                'doc' => [
                    'phuong_thuc_num' => $num_phuongthuc
                ]
            ];

            $id = $data['id'];
            $num_rows++;
            $dem++;
        }
        $query_url->closeCursor();

        if (!empty($batches)) {
            foreach ($batches as $k => $ids) {
                if ($k) {
                    $str_ids = implode(',', $ids);
                    $db->exec('UPDATE nv4_vi_bidding_row SET phuong_thuc_num=' . $num_phuongthuc . ' WHERE id IN (' . $str_ids . ')');
                    $db->exec('UPDATE nv4_en_bidding_row SET phuong_thuc_num=' . $num_phuongthuc . ' WHERE id IN (' . $str_ids . ')');
                }
            }
            $batches = null;
        }

        if (!empty($params['body'])) {
            $hosts = array(
                $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
            );

            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();
            $responses = $client->bulk($params)->asArray();
            $params = null;
            if (!empty($responses['items'])) {
                foreach ($responses['items'] as $value) {
                    if ($value['update']['status'] == 404) {
                        if (str_starts_with($value['update']['_index'], 'en_')) {
                            $db->exec('UPDATE nv4_en_bidding_row SET elasticsearch=0 WHERE id = ' . $value['update']['_id']);
                        } else {
                            $db->exec('UPDATE nv4_vi_bidding_row SET elasticsearch=0 WHERE id = ' . $value['update']['_id']);
                        }
                    } elseif ($value['update']['status'] != 200) {
                        print_r("Xử lý id: \033[33m" . $value['update']['_id']);
                        print_r("\033[31m ERROR\033[0m\n");
                        file_put_contents(NV_ROOTDIR . '/tools/tbmt_phuong_thuc_num_to_es.log', (str_starts_with($value['update']['_index'], 'en_') ? 'en' : 'vi')  . ' ' . $value['update']['_id'] . PHP_EOL, FILE_APPEND);
                    }
                }
            }
            $responses = null;
            $client = null;
        }

        echo 'Run TBMT id: ' . number_format($id) . '/' . number_format($maxid) . PHP_EOL;
        sleep(1);
        if ($id == 0 and $id2 < $maxid) {
            $id = $id2;
        } elseif ($id == 0 and $id2 >= $maxid) {
            break;
        }
    } while ($id < $maxid);
} catch (PDOException $e) {
    trigger_error($e);
    file_put_contents(NV_ROOTDIR . '/tools/error_cp_phuong_thuc_' . $data['id'] . '.log', print_r($e, true));
}
echo "Total copied TBMT phuong_thuc: " . $dem . " - Kết thúc!!!\n\n";
