<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
$hosts = array(
    $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
);

$client = Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
    ->setHosts($hosts)
    ->setRetries(0)
    ->build();

$my_index = 'dauthau_plans';
try {
    // Get mappings
    $params = [
        'index' => $my_index
    ];
    $response = $client->indices()->getMapping($params);
    $properties = $response[$my_index]['mappings']['properties'];
    $properties['alias']['type'] = 'text';
    $properties['alias']['null_value'] = '';

    $params = [
        'index' => $my_index,
        'body' => [
            'properties' => $properties
        ]
    ];

    // Update the index mapping
    $client->indices()->putMapping($params);

    die('đã thực hiện xong');
} catch (Exception $e) {

}

die('lỗi gì đó');