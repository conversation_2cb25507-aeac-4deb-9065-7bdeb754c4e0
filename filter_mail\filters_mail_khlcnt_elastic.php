<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
$waitTimeoutInSeconds = 2;
$filters_mail_khlcnt_log = NV_ROOTDIR . '/data/filters_mail_khlcnt_log_' . $prefix_lang . '_' . date('Ymd') . '.txt';

if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    file_put_contents($filters_mail_khlcnt_log, "Server Elasticsearch didn't work.\n", FILE_APPEND);
    die();
}
fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
}
/*
 * Thuật toán như sau:
 * - Bảng nv4_vi_bidding_logs_plan có trường
 * - status: Trạng thái bằng 0 là đang chạy, nếu truy cập lại thì tiếp tục chạy.
 * - user: ID của users đã chạy đến, Mỗi lần tìm kiếm chỉ khoảng 2 tài khoản để test. (Sau đó có thể nâng lên 50 khi số lựong nhiều)
 * - Bảng nv4_vi_bidding_logs_plan được thêm mới khi không tìm thấy status = 0 và có thêm bài đăng.
 * - Khi tìm kiếm theo từng tài khoản, sẽ tìm lần luợt bộ lọc của từng người, nếu tìm thấy thì lưu vào bảng nv4_vi_bidding_mail để chương trình send_mail.php lấy ra để gửi.
 */

/*
 * Dùng ?testuserid=USERID để kiểm tra trực tiếp đối với thành viên
 * Nếu testuserid hệ thống sẽ bỏ qua ghi dữ liệu vào CSLD chỉ hiển thị kết quả
 */

$testUserid = 0;

if (!$testUserid) {
    $new_loop = 0;
    $arr_id_vip = [];
    $arr_vip = [];
    // Bước 1: Kiểm tra có bảng logs nào đang chạy không, nếu không thì bắt đầu chạy mới, nếu có thì tiếp tục chạy đến userid tiếp theo
    try {
        $logs_info = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_plan WHERE status=0 limit 1')->fetch();
    } catch (PDOException $e) {
        file_put_contents($filters_mail_khlcnt_log, "Lỗi select logs L46\n", FILE_APPEND);
        print_r('Lỗi select logs');
        trigger_error($e->getMessage());
        die($e->getMessage()); // Remove this line after checks finished
    }

    if (!empty($logs_info)) {
        $start_plan_id = $logs_info['from_plan'];
        $end_plan_id = $logs_info['to_plan'];
        $last_userid = $logs_info['userid'];
    } else {
        // Nếu không có phần nào đang chạy thì kiểm tra xem có tin đấu thầu mới không. Nếu có tin mới thì chạy vòng lặp mới, nếu không thì kết thúc
        $last_logs = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_plan ORDER BY id DESC limit 1')->fetch();
        // $info_plan = $db->query('SELECT max(id) as to_plan FROM nv4_vi_bidding_plans')->fetch();

        $array_query_elastic = [];
        $array_query_elastic['size'] = 1;
        $array_query_elastic['sort'] = [
            [
                "id" => [
                    "order" => "desc"
                ]
            ]
        ];
        $array_query_elastic['_source'] = array(
            'id'
        );
        $params = [];
        $params['index'] = NV_LANG_ELASTIC . 'dauthau_plans';
        $params['body'] = $array_query_elastic;
        $response = $client->search($params)->asArray();
        if ($response['hits']['total']['value'] > 0) {
            $info_plan['to_plan'] = $response['hits']['hits'][0]['_source']['id'];
        }
        if (!isset($info_plan['to_plan']) or $info_plan['to_plan'] <= 0) {
            file_put_contents($filters_mail_khlcnt_log, "Lỗi maxid ES \n", FILE_APPEND);
            die('Lỗi');
        }

        if (empty($last_logs)) {
            $start_plan_id = 0; // Chạy lần đầu
            $end_plan_id = $info_plan['to_plan'];
            $last_userid = 0;
            $new_loop = 1;
        } else {
            if ($last_logs['to_plan'] < $info_plan['to_plan']) {
                // Nếu có kế hoạch chọn nhà thầu mới thì lấy ra các bài mới để chạy vòng lặp mới
                $last_userid = 0;
                $start_plan_id = $last_logs['to_plan']; // Lấy id KHLCNT của lần cuối cùng chạy
                $end_plan_id = $info_plan['to_plan']; // Lấy id KHLCNT lớn nhất của dữ liệu hiện có
                $new_loop = 1;
            } else {
                file_put_contents($filters_mail_khlcnt_log, "Không có tin mới trong csdl. Hãy chạy tiếp khi có kế hoạch LCNT đăng mới\n\n", FILE_APPEND);
                // Nếu không có dữ liệu mới để chạy thì dừng lại ngay
                die('Không có tin mới trong csdl. Hãy chạy tiếp khi có kế hoạch LCNT đăng mới!');
            }
        }
    }

    $time = time();
    $start_time = $time;
    if ($new_loop) {
        if ($end_plan_id > $start_plan_id + 500) {
            // Nếu có quá nhiều thì chỉ tìm kiếm trong 500
            $end_plan_id = $start_plan_id + 500;
        }

        if ($start_plan_id == $end_plan_id) {
            die('No Data new');
        }

        try {
            // thêm logs mới vào csdl
            $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_plan (from_plan, to_plan, from_time, to_time, total_time, userid, status) VALUES ( :from_plan, :to_plan, :from_time, :to_time, 0, 0, 0)');
            $stmt->bindParam(':from_plan', $start_plan_id, PDO::PARAM_INT);
            $stmt->bindParam(':to_plan', $end_plan_id, PDO::PARAM_INT);
            $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
            $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
            $exc = $stmt->execute();
            $logs_id = $db->lastInsertId();
            $run_time = 0;
        } catch (PDOException $e) {
            file_put_contents($filters_mail_khlcnt_log, "Lỗi thêm logs mới vào csdl L94\n", FILE_APPEND);
            print_r('Lỗi thêm logs mới vào csdl');
            trigger_error($e->getMessage());
            die($e->getMessage()); // Remove this line after checks finished
        }
    } else {
        $logs_id = $logs_info['id'];
        $run_time = $logs_info['total_time'];
    }
} else {
    // Khi test thành viên
    $start_plan_id = 0;
    $info = $db->query('SELECT max(id) as to_plan FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_plans')->fetch();
    $end_plan_id = $info['to_plan']; // Lấy id lớn nhất của dữ liệu hiện có

    $last_userid = 0;
    $time = time();
    $start_time = $time;
    $run_time = 0;
}

// Bước 2: Lấy ra ds các VIP mỗi lần 50 người
// 2.1 Lấy danh sách các VIP còn hạn sử dụng
$number_user_scan_all = 50;
$number_user_scan = 0;
$user_id_end = 0;
$time_start_thisday = mktime(date('H'), date('i'), date('s'), date('n'), date('j'), date('Y'));

if ($testUserid) {
    // Test 1 tài khoản VIP
    $query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND (vip = 2 OR vip = 21) AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id =' . $testUserid . ' AND prefix_lang = ' . $prefix_lang . ' ORDER BY user_id ASC limit ' . $number_user_scan_all);
} else {
    $query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND (vip = 2 OR vip = 21) AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id >' . $last_userid . ' AND prefix_lang = ' . $prefix_lang . ' ORDER BY user_id ASC limit ' . $number_user_scan_all);
}

while ($vip_info = $query_vip->fetch()) {
    ++$number_user_scan;
    $user_id_end = $vip_info['user_id'];
    $arr_vip[$vip_info['user_id']]['from_time'] = $vip_info['from_time'];
    $arr_vip[$vip_info['user_id']]['to_time'] = $vip_info['end_time'];
    $arr_vip[$vip_info['user_id']]['filter'] = [];
    $arr_vip[$vip_info['user_id']]['bidding'] = [];
    $arr_vip[$vip_info['user_id']]['vip'][$vip_info['vip']] = $vip_info['vip'];

    $arr_id_vip[$vip_info['user_id']] = $vip_info['user_id'];
}
if ($config_bidding['use_reward_points']) {
    // 2.2 Lấy các user đăng ký gửi mail bằng điểm
    $array_id_user = $array_id_customs_points_email = [];
    // lấy thêm các user mà lớn hơn $user_id_end của vòng chạy cuối cùng
    if ($new_loop) {
        $last_logs['userid'] = (empty($last_logs['userid'])) ? 0 : $last_logs['userid'];
        $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_logs['userid'] . ' AND (vip = 2 OR vip = 21) AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC');
        while ($points_email_info = $query_user->fetch()) {
            $arr_vip[$points_email_info['userid']]['vip'][$points_email_info['vip']] = $points_email_info['vip'];
            $arr_vip[$points_email_info['userid']]['to_time'] = $points_email_info['dateexpired'];
            $arr_vip[$points_email_info['userid']]['filter'][$points_email_info['filterid']] = [];
            $arr_vip[$points_email_info['userid']]['bidding'] = [];

            $array_id_user[$points_email_info['userid']] = $points_email_info['userid'];
            $array_id_customs_points_email[$points_email_info['userid']] = $points_email_info['id'];
        }
    }

    $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_userid . ' AND userid < ' . $user_id_end . ' AND (vip = 2 OR vip = 21) AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC limit ' . $number_user_scan_all);
    while ($points_email_info = $query_user->fetch()) {
        $arr_vip[$points_email_info['userid']]['vip'][$points_email_info['vip']] = $points_email_info['vip'];
        $arr_vip[$points_email_info['userid']]['to_time'] = $points_email_info['dateexpired'];
        $arr_vip[$points_email_info['userid']]['filter'][$points_email_info['filterid']] = [];
        $arr_vip[$points_email_info['userid']]['bidding'] = [];

        $array_id_user[$points_email_info['userid']] = $points_email_info['userid'];
        $array_id_customs_points_email[$points_email_info['userid']] = $points_email_info['id'];
    }

    $array_id_customs_points_excel = [];
    // 2.3 Lấy các user đăng ký xuất excel bằng điểm
    // lấy thêm các user mà lớn hơn $user_id_end của vòng chạy cuối cùng
    if ($new_loop) {
        $last_logs['userid'] = (empty($last_logs['userid'])) ? 0 : $last_logs['userid'];
        $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_excel WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_logs['userid'] . ' AND vip = 2 AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC');
        while ($points_excel_info = $query_user->fetch()) {
            $arr_vip[$points_excel_info['userid']]['vip'][$points_excel_info['vip']] = $points_excel_info['vip'];
            $arr_vip[$points_excel_info['userid']]['to_time'] = $points_excel_info['dateexpired'];
            $arr_vip[$points_excel_info['userid']]['filter'][$points_excel_info['filterid']] = [];
            $arr_vip[$points_excel_info['userid']]['bidding'] = [];

            $array_id_user[$points_excel_info['userid']] = $points_excel_info['userid'];
            $array_id_customs_points_excel[$points_excel_info['userid']] = $points_excel_info['id'];
        }
    }

    // các user đăng ký xuất excel bằng điểm được lấy theo limit 50 và theo giới hạn các tk trong phạm vi của nhóm vip trên
    $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_excel WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_userid . ' AND userid < ' . $user_id_end . ' AND vip = 2 AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC limit ' . $number_user_scan_all);
    while ($points_excel_info = $query_user->fetch()) {
        $arr_vip[$points_excel_info['userid']]['vip'][$points_excel_info['vip']] = $points_excel_info['vip'];
        $arr_vip[$points_excel_info['userid']]['to_time'] = $points_excel_info['dateexpired'];
        $arr_vip[$points_excel_info['userid']]['filter'][$points_excel_info['filterid']] = [];
        $arr_vip[$points_excel_info['userid']]['bidding'] = [];

        $array_id_user[$points_excel_info['userid']] = $points_excel_info['userid'];
        $array_id_customs_points_excel[$points_excel_info['userid']] = $points_excel_info['id'];
    }
    ksort($arr_vip);
    $arr_id_all = array_merge($arr_id_vip, $array_id_user);
} else {
    $arr_id_all = $arr_id_vip;
}

// Xuất thông tin test tài khoản VIP
if ($testUserid) {
    if (empty($arr_vip)) {
        echo ("User này không có tài khoản VIP<br/>");
    } else {
        echo ("Tài khoản VIP OK<br/>" . debugArray($arr_vip) . "<br/><br/>");
    }
}

// Bước 3: Lấy ra ds các bộ lọc của VIP đc chọn
if (!empty($arr_id_all)) {
    // thông tin user
    $query_user = $db->query('SELECT userid, email FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_id_all) . ')');
    while ($user = $query_user->fetch()) {
        $user_all[$user['userid']] = $user['email'];
    }

    $query_filter = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE userid IN (' . implode(',', $arr_id_all) . ') AND (status=1 OR send_excel = 1) AND (vip_use=21 OR vip_use=2) AND prefix_lang = ' . $prefix_lang . '');
    while ($result = $query_filter->fetch()) {
        if (in_array($result['vip_use'], $arr_vip[$result['userid']]['vip'])) {
            $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
            $arr_vip[$result['userid']]['email'] = isset($user_all[$result['userid']]) ? $user_all[$result['userid']] : '';
        } else if (isset($arr_vip[$result['userid']]['filter'][$result['id']])) {
            $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
            $arr_vip[$result['userid']]['email'] = isset($user_all[$result['userid']]) ? $user_all[$result['userid']] : '';
        }
    }
}

// Xuất thông tin test bộ lọc
if ($testUserid) {
    if (empty($arr_vip[$testUserid]['filter'])) {
        echo ("User này không có bộ lọc<br/>");
    } else {
        echo ("Bộ lọc OK<br/>" . debugArray($arr_vip[$testUserid]['filter']) . "<br/><br/>");
    }
}

// Bước 4: Lấy ra các tin trong ds tin mới mà thỏa mãn bộ lọc tương ứng với từng VIP để thêm vào bảng _tmp
if (empty($arr_vip)) {
    if ($testUserid) {
        die("Kết thúc test thành viên<br />");
    }

    // Nếu không lấy được VIP nào thì coi như đã chạy xong một vòng và update lại status là đã chạy xong
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $change = $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_plan SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_khlcnt_log, "Đã quét xong một lượt các vip. Tổng thời gian cho một lượt:" . $total_time . "\n", FILE_APPEND);
    die('Đã quét xong một lượt các vip. Tổng thời gian cho một lượt:' . $total_time);
} else {
    if (!$testUserid) {
        $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_plan SET userid =' . $user_id_end . ' WHERE id =' . $logs_id);
    }
    file_put_contents($filters_mail_khlcnt_log, "ArrayID plans:" . $start_plan_id . "-" . $end_plan_id . ";\n", FILE_APPEND);

    foreach ($arr_vip as $vip_id => $vip) {
        file_put_contents($filters_mail_khlcnt_log, "Bắt đầu quét:" . $vip_id . ";", FILE_APPEND);
        // Lấy ra danh sách các KHLCNT thỏa mãn điều kiện tìm kiếm từ các bộ lọc
        $arr_id = [];
        $priority = 0;
        if (!empty($vip['filter'])) {
            foreach ($vip['filter'] as $filter) {
                $check_request = 0;
                if (!empty($filter)) {
                    // Từ bộ lọc chọn ra điều kiện lọc where tương ứng
                    $search_elastic = [];
                    $search_elastic['must'][]['range']['id'] = [
                        "gt" => $start_plan_id,
                        "lte" => $end_plan_id
                    ];

                    if ($filter['time_find'] > 0) {
                        $time_find = mktime(0, 0, 0, date('n'), date('j'), date('Y')) - 86400 * $filter['time_find'];

                        $search_elastic['must'][]['range']['addtime'] = [
                            "gte" => $time_find
                        ];
                    }
                    if ($filter['search_type'] == 1) {
                        if ($filter['par_search']) {
                            $search_fields = ['code', 'title'];
                        } else {
                            $search_fields = ['content_full'];
                        }
                    } else {
                        if ($filter['par_search']) {
                            $search_fields = ['code', 'title_search'];
                        } else {
                            $search_fields = ['content'];
                        }
                    }
                    // từ khóa loại trừ
                    if (!empty($filter['without_key'])) {
                        $arr_key = explode(',', $filter['without_key']);
                        foreach($search_fields as $f) {
                            foreach ($arr_key as $key) {
                                if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                                    continue;
                                }
                                if (empty($key)){
                                    continue;
                                }
                                if ($filter['search_type'] == 1) {
                                    $key = trim($key);
                                    $search_elastic['must_not'][] = [
                                        "match_phrase" => [
                                            $f => $key
                                        ]
                                    ];
                                } else {
                                    $key = str_replace('-', ' ', change_alias($key));
                                    $key = trim($key);
                                    $search_elastic['must_not'][] = [
                                        "match_phrase" => [
                                            $f => $key
                                        ]
                                    ];
                                }
                            }
                        }
                    }

                    if (!empty($filter['key_search2'])) {
                        // Tìm theo từ khóa cùng
                        $arr_key = explode(',', $filter['key_search2']);
                        $search_bosung_should = [];
                        foreach($search_fields as $f) {
                            foreach ($arr_key as $key) {
                                if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                                    continue;
                                }
                                if (empty($key)){
                                    continue;
                                }
                                if (!empty($filter['search_one_key'])) {
                                    //Một trong các từ khóa là điều kiện bắt buộc
                                    if ($filter['search_type'] == 1) {
                                        $key = trim($key);
                                        $search_bosung_should[] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    } else {
                                        $key = str_replace('-', ' ', change_alias($key));
                                        $key = trim($key);
                                        $search_bosung_should[] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    }
                                } else {
                                    if ($filter['search_type'] == 1) {
                                        $key = trim($key);
                                        $search_elastic['must'][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    } else {
                                        $key = str_replace('-', ' ', change_alias($key));
                                        $key = trim($key);
                                        $search_elastic['must'][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    }
                                }
                            }
                        }
                        if (!empty($search_bosung_should)) {
                            $search_elastic['must'][] = [
                                "bool" => [
                                    "should" => $search_bosung_should,
                                    "minimum_should_match" => 1
                                ]
                            ];
                        }
                    }

                    if (!empty($filter['key_search'])) {
                        $arr_key = explode(',', $filter['key_search']);
                        if (!empty($filter['searchkind'])) {
                            $arr_key = array_map(function ($a) {return explode(' ', $a);}, $arr_key);
                            $arr_key = new RecursiveIteratorIterator(new RecursiveArrayIterator($arr_key));
                        }
                        $keyword_type = $filter['searchkind'] <= 1 ? 'should' : 'must';
                        foreach($search_fields as $f) {
                            foreach ($arr_key as $key) {
                                if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                                    continue;
                                }
                                if ($filter['search_type'] == 0) {
                                    $key = str_replace('-', ' ', change_alias($key));
                                }
                                $key = trim($key);
                                if (empty($key)){
                                    continue;
                                }
                                if (preg_match('/^[0-9]+?$/', $key)) { // Nếu keyword chỉ là dãy số thì tìm wildcard
                                    $search_elastic[$keyword_type][] = [
                                        "wildcard" => [
                                            $f => [
                                                "value" => '*' . $key . '*'
                                            ]
                                        ]
                                    ];
                                } else {
                                    $search_elastic[$keyword_type][] = [
                                        "match_phrase" => [
                                            $f => $key
                                        ]
                                    ];
                                }
                            }
                        }
                    }

                    // Tìm kiếm theo tỉnh thành
                    if (!empty($filter['idprovince'])) {
                        $filter['idprovince'] = explode(',', $filter['idprovince']);
                        $search_idprovince = [];
                        foreach ($filter['idprovince'] as $key_idp) {
                            $search_idprovince[] = [
                                'term' => [
                                    'list_id_province' => $key_idp
                                ]
                            ];
                        }
                        $search_elastic['must'][]['bool']['should'] = $search_idprovince;
                    }

                    // tìm theo tổng mức đầu tư
                    if ($filter['invest_from'] > 0 and $filter['invest_to']) {
                        $search_elastic['must'][]['range']['total_invest_number'] = [
                            "gte" => $filter['invest_from'],
                            "lte" => $filter['invest_to']
                        ];
                    } else {
                        if ($filter['invest_from'] > 0) {
                            $search_elastic['must'][]['range']['total_invest_number'] = [
                                "gte" => $filter['invest_from']
                            ];
                        }
                        if ($filter['invest_to'] > 0) {
                            $search_elastic['must'][]['range']['total_invest_number'] = [
                                "lte" => $filter['invest_to']
                            ];
                        }
                    }
                    if ($filter['price_plan_from'] > 0 and $filter['price_plan_to'] > 0) {
                        $search_elastic['must'][]['range']['price_min'] = [
                            "gte" => $filter['price_plan_from'],
                            "lte" => $filter['price_plan_to']
                        ];
                        $search_elastic['must'][]['range']['price_max'] = [
                            "gte" => $filter['price_plan_from'],
                            "lte" => $filter['price_plan_to']
                        ];
                    } else {
                        // tìm theo giá gói thầu
                        if ($filter['price_plan_from'] > 0) {
                            $search_elastic['must'][]['range']['price_min'] = [
                                "gte" => $filter['price_plan_from']
                            ];
                            $search_elastic['must'][]['range']['price_max'] = [
                                "gte" => $filter['price_plan_from']
                            ];
                        }
                        if ($filter['price_plan_to'] > 0) {
                            $search_elastic['must'][]['range']['price_min'] = [
                                "lte" => $filter['price_plan_to']
                            ];
                            $search_elastic['must'][]['range']['price_max'] = [
                                "lte" => $filter['price_plan_to']
                            ];
                        }
                    }

                    if ($filter['vip_use'] == 21) {
                        $search_elastic['must'][] = [
                            'match' => [
                                'is_domestic' => [
                                    'query' => '0'
                                ]
                            ]
                        ];
                    }

                    if (!empty($search_elastic['should'])) {
                        $search_elastic['minimum_should_match'] = '1';
                        $search_elastic['boost'] = '1.0';
                    }

                    $array_query_elastic = [];
                    if (!empty($search_elastic)) {
                        $array_query_elastic['query']['bool'] = $search_elastic;
                    }
                    $array_query_elastic['size'] = 1000;
                    $array_query_elastic['sort'] = [
                        [
                            "addtime" => [
                                "order" => "desc"
                            ]
                        ]
                    ];
                    if ($testUserid) {
                        echo "Tìm kiếm với query: <br />" . debugArray($array_query_elastic) . "<br /><br />";
                    }

                    $params = [];
                    $params['index'] = NV_LANG_ELASTIC . 'dauthau_plans';
                    // $params['type'] = 'nv4_vi_bidding_plans';
                    $params['body'] = $array_query_elastic;
                    try {
                        $response = $client->search($params)->asArray();
                    } catch (Exception $e) {
                        file_put_contents($filters_mail_khlcnt_log, "Lỗi search ES L364:userid: " . $vip_id . "\n\n", FILE_APPEND);
                        print_r('Lỗi INSERT kế hoạch lựa chọn nhà thầu tìm được vào bảng nv4_vi_bidding_plan_id');
                        echo '<pre>';
                        print_r($e);
                        echo '</pre>';
                        notify_to_slack($filters_mail_khlcnt_log, "filters_mail_khlcnt_elastic.php Lỗi search ES L364:userid: " . $vip_id . "\n\n");
                        die();
                    }
                    if ($testUserid) {
                        echo "Kết quả: <br />" . debugArray($response) . "<br /><br />";
                    }
                    $num_row = $response['hits']['total']['value'];
                    foreach ($response['hits']['hits'] as $value) {
                        if (!empty($value['_source'])) {
                            $plan = $value['_source'];
                            $arr_id[$plan['id']] = $plan['id'];
                            try {
                                if (!$testUserid) {
                                    $time = NV_CURRENTTIME;
                                    $notify = '';
                                    if ($filter['vip_use'] == 21 && $filter['status'] == 1) {
                                        $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_planqt_id(userid, filter_id, plan_id, send_status, addtime) VALUES (:userid, :filter_id, :plan_id, 0, :addtime)');

                                        $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                        $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                        $stmt->bindParam(':plan_id', $plan['id'], PDO::PARAM_INT);
                                        $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                        $exc = $stmt->execute();
                                    } else {
                                        // Nếu tìm ra tin thỏa mãn điều kiện thì thêm bảng tmp
                                        
                                        $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_plan_id(userid, filter_id, plan_id, notify_vip, send_status, addtime) VALUES (:userid, :filter_id, :plan_id, :notify_vip , :send_status, :addtime)');

                                        $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                        $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                        $stmt->bindParam(':plan_id', $plan['id'], PDO::PARAM_INT);
                                        $stmt->bindParam(':notify_vip', $notify, PDO::PARAM_STR);
                                        $stmt->bindValue(':send_status', ($filter['status'] == 0 ? -100 : 0), PDO::PARAM_INT);
                                        $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                        $exc = $stmt->execute();

                                        // Thêm vào bảng request nếu khách hàng yêu cầu gửi excel
                                        if ($filter['send_excel'] && $filter['vip_use'] == 2) {//
                                            if (empty($check_request)) {
                                                $check_request = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_request WHERE status = 0 AND type_request = 0 AND filter_type = 2 AND filter_id = ' . $filter['id'])->fetch();
                                            }

                                            $email = '';
                                            if (empty($check_request)) {
                                                // thêm vào bảng request
                                                // mức độ ưu tiên
                                                if (empty($priority)) {
                                                    $priority = $db->query('SELECT count(*) FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_request WHERE userid=' . $vip_id . ' AND addtime >= ' . mktime(0, 0, 0, date('m', NV_CURRENTTIME), date('d', NV_CURRENTTIME), date('Y', NV_CURRENTTIME)))->fetchColumn();
                                                } else {
                                                    $priority++;
                                                }

                                                // các điều kiện lọc
                                                $array_param = [
                                                    'title' => $filter['title'],
                                                    'vip_use' => $filter['vip_use'],
                                                    'key' => $filter['key_search'],
                                                    'key2' => $filter['key_search2'],
                                                    'search_type_content' => $filter['search_type'],
                                                    'without_key' => $filter['without_key'],
                                                    'time_find' => $filter['time_find'],
                                                    'invest_from' => $filter['invest_from'],
                                                    'invest_to' => $filter['invest_to'],
                                                    'price_plan_from' => $filter['price_plan_from'],
                                                    'price_plan_to' => $filter['price_plan_to'],
                                                    'par_search' => $filter['par_search'],
                                                    'searchkind' => $filter['searchkind'],
                                                ];

                                                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_request (userid, email, request_params, addtime, send_mail, priority, filter_id, filter_type) VALUES (:userid, :email, :request_params, :addtime, :send_mail, :priority, :filter_id, :filter_type)');

                                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                                $email = !empty($vip['email']) ? $vip['email'] : '';
                                                $stmt->bindParam(':email', $email, PDO::PARAM_STR);
                                                $stmt->bindValue(':request_params', json_encode($array_param, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), PDO::PARAM_STR);
                                                $stmt->bindValue(':addtime', NV_CURRENTTIME, PDO::PARAM_INT);
                                                $stmt->bindValue(':send_mail', 1, PDO::PARAM_INT);
                                                $stmt->bindValue(':priority', !empty($priority) ? $priority : 0, PDO::PARAM_INT);
                                                $stmt->bindValue(':filter_id', $filter['id'], PDO::PARAM_INT);
                                                $stmt->bindValue(':filter_type', 2, PDO::PARAM_INT);

                                                $exc = $stmt->execute();
                                                if ($exc) {
                                                    $check_request = $db->lastInsertId();
                                                }
                                            }
                                        }
                                    }
                                }
                            } catch (PDOException $e) {
                                file_put_contents($filters_mail_khlcnt_log, "Lỗi INSERT  L301:\n" . print_r($e, true) . "\n", FILE_APPEND);
                                print_r('Lỗi INSERT kế hoạch lựa chọn nhà thầu tìm được vào bảng nv4_vi_bidding_plan_id');
                                echo '<pre>';
                                print_r($e);
                                echo '</pre>';
                                notify_to_slack($filters_mail_khlcnt_log, "filters_mail_khlcnt_elastic.php Lỗi INSERT  L301:\n" . print_r($e, true) . "\n");
                                die(); // Remove this line after checks finished
                            }
                        }
                    }

                    // ghi log bộ lọc
                    if ($num_row > 0) {
                        $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastresult = ' . NV_CURRENTTIME . ', stat_totalresult = stat_totalresult + ' . $num_row . ' WHERE id=' . $filter['id']);
                    }
                }
            }
        }

        file_put_contents($filters_mail_khlcnt_log, " Plansid:" . implode(',', $arr_id) . "; \n", FILE_APPEND);

        try {
            if (!$testUserid) {
                // ghi lại logs cho quá trình tìm kiếm tin cho mỗi vip
                $time = NV_CURRENTTIME;
                $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_plan SET to_time = :to_time, userid = :userid WHERE id =' . $logs_id);
                $_vip_id = ($vip_id >= $user_id_end) ? $user_id_end : $vip_id;
                $stmt->bindParam(':userid', $_vip_id, PDO::PARAM_INT);
                $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
                $exc = $stmt->execute();
            }
        } catch (PDOException $e) {
            file_put_contents($filters_mail_khlcnt_log, "Lỗi update lại logs cho quá trình tìm kiếm tin cho mỗi vip:\n" . print_r($e, true) . "\n", FILE_APPEND);
            print_r('Lỗi update lại logs cho quá trình tìm kiếm tin cho mỗi vip');
            echo '<pre>';
            trigger_error($e->getMessage());
            echo '</pre>';
            notify_to_slack($filters_mail_khlcnt_log, "filters_mail_khlcnt_elastic.php Lỗi update lại logs cho quá trình tìm kiếm tin cho mỗi vip:\n" . print_r($e, true) . "\n");
            die(); // Remove this line after checks finished
        }
    }

    // Dừng nếu test
    if ($testUserid) {
        die("Kết thúc test thành viên<br />");
    }
    // Sau khi chạy xong một vòng 50 VIP thì thông báo chạy xong và thời gian chạy cho một vòng
    $time = time();
    $this_time = $time - $start_time;
    $total_time = $run_time + $this_time;
    try {
        if ($number_user_scan < $number_user_scan_all) {
            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_plan SET to_time = :to_time, total_time = :total_time, status=1 where id =' . $logs_id);
            $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
            $stmt->bindParam(':total_time', $total_time, PDO::PARAM_INT);
            $exc = $stmt->execute();
        } else {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_plan SET to_time = ' . $time . ', total_time = ' . $total_time . ' where id =' . $logs_id);
        }
        if (sizeof($arr_id_vip) > 0) {
            $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs  SET filters_mail = ' . NV_CURRENTTIME . ' WHERE user_id IN (' . implode(',', $arr_id_vip) . ') AND (vip=2 OR vip=21) AND prefix_lang = ' . $prefix_lang . '');
        }
        if ($config_bidding['use_reward_points']) {
            if (sizeof($array_id_customs_points_email) > 0) {
                $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs_points_email SET timefilter = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_id_customs_points_email) . ') AND prefix_lang = ' . $prefix_lang . '');
            }

            if (sizeof($array_id_customs_points_excel) > 0) {
                $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs_points_excel SET timefilter = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_id_customs_points_excel) . ') AND prefix_lang = ' . $prefix_lang . '');
            }
        }
    } catch (PDOException $e) {
        file_put_contents($filters_mail_khlcnt_log, "Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP  L415:\n" . print_r($e, true) . "\n", FILE_APPEND);
        print_r('Lỗi update logs sau khi chạy một vòng 50 VIP');
        echo '<pre>';
        print_r($e);
        echo '</pre>';
        notify_to_slack($filters_mail_khlcnt_log, "filters_mail_khlcnt_elastic.php Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP  L415:\n" . print_r($e, true) . "\n");
        die(); // Remove this line after checks finished
    }

    echo ('Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time);
    file_put_contents($filters_mail_khlcnt_log, 'Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time . "\n\n", FILE_APPEND);
    echo "\n\n";
    die();
}

/**
 *
 * @param array $data
 * @return string
 */
function debugArray($data)
{
    return "<pre><code>" . print_r($data, true) . "</code></pre>";
}
