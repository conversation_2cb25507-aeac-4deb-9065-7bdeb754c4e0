<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */

// Gửi mail follow bằng điểm
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$create_mail_file = NV_ROOTDIR . '/data/create_mail_ycbg_online_' . $prefix_lang . '_' . date('Ymd') . '.txt';
if (file_exists(NV_ROOTDIR . '/data/create_mail_ycbg_online_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "ycbg_follow_online.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit();
}

if (file_exists(NV_ROOTDIR . '/data/create_mail_ycbg_online_' . $prefix_lang . '.txt')) {
    file_put_contents(NV_ROOTDIR . '/data/create_mail_ycbg_online_' . $prefix_lang . '.txt', NV_CURRENTTIME);
}

// Khởi tạo biến lưu thông tin thông báo
$arrInform = $list_info = [];
try {
    $user = [];
    // Lấy danh sách follow bằng điểm, chỉ tính các ycbg_id có filter_id =0
    $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg_id WHERE send_status = 0 AND filter_id = 0 AND remind_ycbg = 0 LIMIT 20');
    $info_user = [];
    while ($row = $query->fetch()) {
        $user[] = $row['userid'];
    }

    // Kiểm tra xem có user theo dõi mua bằng điểm không
    if (!empty($user)) {
        // lấy thông tin user
        $arr_user = $db->query('SELECT userid, username, email, first_name, last_name FROM nv4_users WHERE userid IN (' . implode(',', $user) . ')');

        while ($row = $arr_user->fetch()) {
            $info_user[$row['userid']] = $row;
        }

        if (!empty($info_user)) {
            // Lấy ra tất cả ycbg trong danh sách gửi đi
            foreach ($info_user as $userid => $v) {
                if (file_exists($create_mail_file)) {
                    file_put_contents($create_mail_file, 'BEGIN: ' . $userid . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
                }

                $arr_ycbg = array();
                $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg WHERE id IN (SELECT rq_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg_id WHERE send_status = 0 AND remind_ycbg = 0 AND rq_id>0 AND userid=' . $userid . ')');
                while ($ycbg_row = $query->fetch()) {
                    $arr_ycbg[$ycbg_row['id']] = $ycbg_row;
                }

                // Gộp nhóm những mail chung chủ đề lại theo từng VIP
                $array_ycbg_id = array();
                $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg_id WHERE send_status = 0 AND remind_ycbg = 0 AND userid=' . $userid . ' AND filter_id = 0 LIMIT 50');

                while ($ycbg_data = $query->fetch()) {
                    $array_ycbg_id[$ycbg_data['id']] = $ycbg_data['id'];
                    if ($ycbg_data['rq_id'] > 0) {
                        // $v['follow'][] = $ycbg_data['rq_id'];
                        $v['follow'][]['list_rqid'][] = $arr_ycbg[$ycbg_data['rq_id']];
                        $v['rqid'][$ycbg_data['rq_id']] = $ycbg_data;
                    }

                    if ($ycbg_data['notify_vip'] != '') {
                        $v['notify'][$ycbg_data['rq_id']] = $ycbg_data;
                        $v['rqid'][] = 0;
                    }
                }

                $db->beginTransaction();
                $v['sub_email'] = $v['phone'] = '';

                try {
                    $data_insert = array();
                    $data_insert['addtime'] = NV_CURRENTTIME;
                    $data_insert['send_time'] = 0;
                    $data_insert['status'] = 0;

                    // Gửi mail yêu cầu báo giá nhóm theo từng bộ lọc
                    if (!empty($v['follow'])) {
                        $data_insert['title'] = sprintf($lang_module['mail_title_ycbg'], date('d/m/Y'));
                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 0;
                        if (file_exists($create_mail_file)) {
                            file_put_contents($create_mail_file, "Begin nv_theme_bidding_mail_follow\n", FILE_APPEND);
                        }

                        $data_insert['content'] = nv_theme_bidding_mail_follow($v['follow'], $userid, $arrInform, $v['username']);

                        file_put_contents($create_mail_file, "END nv_theme_bidding_mail_follow\n", FILE_APPEND);
                        // Nội dung htm sẽ gửi cho từng khách
                        try {
                            file_put_contents($create_mail_file, "BIGIN INSERT INTO nv4_vi_bidding_mail\n", FILE_APPEND);
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR);
                            $stmt->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();
                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (PDOException $e) {
                            print_r('Lỗi thêm mail tin mới vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 118: " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "ycbg_follow_online.php ERROR INSERT INTO mail 118: " . $userid . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo ($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }

                    // Gửi mail thay đổổi
                    if (!empty($v['notify'])) {
                        $data_insert['title'] = sprintf($lang_module['mail_title_ycbg_update'], date('d/m/Y'));
                        $data_insert['type'] = 1;
                        $data_insert['vip'] = 0;
                        $data_insert['content'] = nv_theme_change_mail($v['notify'], $userid, $arrInform, $v['username']);

                        try {
                            $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt1->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt1->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR, strlen($row['cc_mail']));
                            $stmt1->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc1 = $stmt1->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (PDOException $e) {
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "ycbg_follow_online.php ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n");
                        }
                    }

                    // Thực hiện lưu biến thông báo tạm
                    foreach ($arrInform as $k => $v) {
                        foreach ($v as $v1) {
                            $list_info[] = $v1;
                        }
                    }

                    // Reset lại biến này về rỗng để tiến hành lưu mảng dữ liệu tiếp theo
                    $arrInform = [];

                    echo "userid = " . $userid . "\n";

                    // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                    if (!empty($array_ycbg_id) and !empty($_mailid)) {
                        $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_ycbg_id) . ') AND send_status = 0 AND remind_ycbg = 0');
                    }
                    $db->commit();
                    file_put_contents($create_mail_file, "number bid = " . sizeof($array_ycbg_id) . ": " . implode(',', $array_ycbg_id) . "\n", FILE_APPEND);
                    file_put_contents($create_mail_file, "END: " . $userid . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
                } catch (PDOException $e) {
                    file_put_contents($create_mail_file, 'rollBack: ' . $userid . "\n\n", FILE_APPEND);
                    $db->rollBack();
                    echo '<pre>';
                    print_r($e);
                }
            }
        }
    }

    if (file_exists(NV_ROOTDIR . '/data/create_mail_tbmt_online' . $prefix_lang . '.txt')) {
        unlink(NV_ROOTDIR . '/data/create_mail_tbmt_online' . $prefix_lang . '.txt');
    }
} catch (PDOException $e) {
    file_put_contents($create_mail_file, 'ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "ycbg_follow_online.php ERROR: " . print_r($e, true) . "\n\n");
}

// Thực hiện lưu thông báo
if (!empty($list_info)) {
    echo "Có tổng " . sizeof($list_info) . ' Thông báo Inform';
    file_put_contents(NV_ROOTDIR . '/data/inform/inform_ycbg_follow_online' . uniqid('', true) . '.txt', json_encode($list_info));
}

echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function nv_theme_bidding_mail_follow($array_follow, $userid, &$arrInform, $username)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func;

    // Phân loại báo giá
    $arr_request_quote_type = [
        'THUOC' => $lang_module['request_quote_type_THUOC'],
        'VTTT' => $lang_module['request_quote_type_VTTT'],
        'VATTU' => $lang_module['request_quote_type_VATTU'],
    ];

    $arr_request_quote_form = [
        'OFFLINE' => $lang_module['request_quote_form_OFFLINE'],
        'EMAIL' => $lang_module['request_quote_form_EMAIL'],
        'FAX' => $lang_module['request_quote_form_FAX'],
    ];
    $rq_validity_period_unit = [
        'D' => $lang_module['rq_validity_period_unit_D'],
        'M' => $lang_module['rq_validity_period_unit_M'],
        'Y' => $lang_module['rq_validity_period_unit_Y'],
    ];
    $array_codeycbg = '';
    if (!empty($array_follow)) {
        $xtpl = new XTemplate('email_follow_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        foreach ($array_follow as $arr_list) {
            if (!empty($arr_list['list_rqid'])) {
                $i_break = 0;
                // lọc bỏ tin trùng nhau
                $arr_sycbg = array();

                foreach ($arr_list['list_rqid'] as $array_data) {
                    $array_data['request_quote_code'] = explode('-', $array_data['request_quote_code']);
                    if (!isset($arr_sycbg[$array_data['request_quote_code'][0]])) {
                        $arr_sycbg[$array_data['request_quote_code'][0]] = $array_data['request_quote_code'][1];
                    } else {
                        $array_data['request_quote_code'][1] = intval($array_data['request_quote_code'][1]);
                        $old = intval($arr_sycbg[$array_data['request_quote_code'][0]]);
                        if ($old < $array_data['request_quote_code'][1]) {
                            $arr_sycbg[$array_data['request_quote_code'][0]] = '0' . $array_data['request_quote_code'][1];
                        }
                    }
                }
                // hiển thị
                foreach ($arr_list['list_rqid'] as $array_data) {
                    $request_quote_code = explode('-', $array_data['request_quote_code']);
                    $array_codeycbg = $request_quote_code[0];
                    if (isset($arr_sycbg[$request_quote_code[0]]) && intval($arr_sycbg[$request_quote_code[0]]) == intval($request_quote_code[1])) {
                        ++$i_break;
                        if ($i_break > 50) {
                            break;
                        }
                        $array_data['title'] = $array_data['request_quote_name'];
                        $array_data['title_a'] = nv_htmlspecialchars($array_data['request_quote_name']);

                        $array_data['public_date'] = nv_date('H:i d/m/y', $array_data['public_date']);
                        // $ycbg_t = getUrlByLanguage('ycbg');
                        if ($site_lang != 'vi') {
                            $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['request-quote'] . '/' . $array_data['alias'] . '-' . $array_data['request_quote_code'] . '.html';
                        } else {
                            $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['request-quote'] . '/' . $array_data['alias'] . '-' . $array_data['request_quote_code'] . '.html';
                        }

                        $check = md5($vip_id . $so_ycbg[0]);

                        // Phân loại ycbg
                        if ($array_data['request_quote_type'] != '') {
                            $array_data['request_quote_type_title'] = (in_array($array_data['request_quote_type'], array_keys($arr_request_quote_type)) ? $arr_request_quote_type[$array_data['request_quote_type']] : '');
                        }

                        // Hình thức tiếp nhận báo giá
                        if ($array_data['request_quote_form'] != '') {
                            $request_quote_form = explode(',', $array_data['request_quote_form']);
                            $array_data['request_quote_form_title'] = '';
                            $arr_request_quote_form_title = [];
                            if (!empty($request_quote_form)) {
                                foreach ($request_quote_form as $request_quote_form_value) {
                                    $arr_request_quote_form_title[] = $arr_request_quote_form[$request_quote_form_value];
                                }
                            }
                            if (!empty($arr_request_quote_form_title)) {
                                $array_data['request_quote_form_title'] = implode(', ', $arr_request_quote_form_title);
                            }

                        }

                        // Thời hạn bắt đầu tiếp nhận báo giá
                        $array_data['reception_date_from'] = !empty($array_data['reception_date_from']) ? nv_date('d/m/Y H:i', $array_data['reception_date_from']) : '';

                        // Thời hạn kết thúc tiếp nhận báo giá
                        $array_data['reception_date_to'] = !empty($array_data['reception_date_to']) ? nv_date('d/m/Y H:i', $array_data['reception_date_to']) : '';

                        $array_data['reception_date'] = '';
                        if (!empty($array_data['reception_date_from']) && !empty($array_data['reception_date_to'])) {
                            $array_data['reception_date'] = $lang_module['from_time'] . ' ' . $array_data['reception_date_from'] . ' ' . $lang_module['to_time'] . ' ' . $array_data['reception_date_to'];
                        }

                        // Thời hạn có hiệu lực của báo giá
                        $array_data['rq_validity_period_unit_title'] = (in_array($array_data['rq_validity_period_unit'], array_keys($rq_validity_period_unit)) ? $rq_validity_period_unit[$array_data['rq_validity_period_unit']] : '');
                        if (!empty($array_data['rq_validity_period_unit_title'])) {
                            $array_data['rq_validity_period_unit_title'] .= ' ' . $lang_module['from_date_rq'] . ' ' . $array_data['reception_date_to'];
                        }
                        $array_data['link_follow'] = NV_MY_DOMAIN . '/follow-rq';

                        $xtpl->assign('DATA', $array_data);
                        $arrMess = [
                            'vi' => sprintf(get_lang('vi', 'title_new_ycbg'), $array_data['title']),
                            'en' => sprintf(get_lang('en', 'title_new_ycbg'), $array_data['title'])
                        ];

                        $arrLink = [
                            'vi' => URL_RE . '?tb=' . $array_data['id'],
                            'en' => URL_RE . 'en/?tb=' . $array_data['id']
                        ];

                        // Thông báo Push Notification cho người dùng
                        // insertInform($userid, $arrMess, $array_data['link_view']);
                        $arrInform['nv_theme_bidding_mail_follow'][] = [
                            'vip_id' => $userid,
                            'mess' => $arrMess,
                            'link' => $arrLink
                        ];

                        if ($array_data['investor_name'] != '') {
                            $xtpl->parse('main.follow.content_rq.project_owner');
                        }

                        if ($array_data['p_name'] != '') {
                            $xtpl->parse('main.follow.content_rq.p_name');
                        }

                        if (intval($request_quote_code[1]) > 0) {
                            $xtpl->parse('main.follow.content_rq.notify_old');
                        }

                        $xtpl->parse('main.follow.content_rq');
                    }
                }
            }
            $xtpl->parse('main.follow');
        }
    }

    // chỉ lấy đại diện 1 tin theo dõi
    $mail_footer_follow = '';
    if (!empty($array_codeycbg)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow_rq WHERE ycbg_code = ' . $db->quote($array_codeycbg) . '');
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow_rq'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_change_mail($array_change, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func;
    if (!empty($array_change)) {

        $xtpl = new XTemplate('email_new_ycbg.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        $array_codeycbg = '';
        foreach ($array_change as $change) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg WHERE id = ' . $change['rq_id'] . '');
            if ($row = $result->fetch()) {
                $ycbg = explode('-', $row['request_quote_code']);
                $array_codeycbg = $ycbg[0];

                $row['title'] = $row['request_quote_name'];
                $row['title_a'] = nv_htmlspecialchars($row['title']);
                $row['time'] = $change['chance_time'] > 0 ? nv_date('H:i d/m/Y', $change['chance_time']) : '';
                $row['content'] = $change['notify_vip'];
                if ($site_lang != 'vi') {
                    $row['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['request-quote'] . '/' . $row['alias'] . '-' . $row['request_quote_code'] . '.html';
                } else {
                    $row['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['request-quote'] . '/' . $row['alias'] . '-' . $row['request_quote_code'] . '.html';
                }

                $row['link_follow'] = NV_MY_DOMAIN . '/follow-rq';
                $xtpl->assign('DATA', $row);
                $arrMess = [
                    'vi' => sprintf(get_lang('vi', 'title_search_by_notify'), date('d/m/Y H:i'), $row['title']),
                    'en' => sprintf(get_lang('en', 'title_search_by_notify'), date('m/d/Y H:i'), $row['title'])
                ];

                $arrLink = [
                    'vi' => URL_RE . '?tb=' . $row['id'],
                    'en' => URL_RE . 'en/?tb=' . $row['id']
                ];
                // Thông báo Push Notification cho người dùng
                // insertInform($vip_id, $arrMess, $row['link_view']);
                $arrInform['nv_theme_change_mail'][] = [
                    'vip_id' => $vip_id,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];

                $xtpl->parse('main.change.loop');
            }
        }
        $xtpl->parse('main.change');
    }

    // chỉ lấy đại diện 1 tin theo dõi
    $mail_footer_follow = '';
    if (!empty($array_codeycbg)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow_rq WHERE ycbg_code = ' . $db->quote($array_codeycbg) . '');
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow_rq'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}
