<?php

// Tool này bỏ đi, do đã chạy qua push_tbmt.php

// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
set_time_limit(0);

$filename = NV_ROOTDIR . '/aita/data/update_aita_result_id.txt';
$id_tmbt = intval(file_get_contents($filename));

// Lấy các TBMT có is_aita = 1
$arr_bidding_row = $db->query('SELECT id, so_tbmt FROM nv4_vi_bidding_row WHERE id> ' . $id_tmbt . ' AND is_aita = 1 ORDER BY id ASC LIMIT 100');
while ($_bidding_row = $arr_bidding_row->fetch()) {
    $id_tmbt = $_bidding_row['id'];
    try {
        // Lấy result_id
        $sql = 'SELECT id FROM nv4_vi_bidding_result WHERE code = ' . $db->quote($_bidding_row['so_tbmt']);
        $result_id = $db->query($sql)->fetchColumn();
        if ($result_id > 0) {
            // Cập nhật sang bảng nv4_vi_aita_result_id
            echo $id_tmbt . "  " . $_bidding_row['so_tbmt'] . " -> " . $result_id . " \n<br>";
            $db->exec('INSERT IGNORE INTO `nv4_vi_aita_result_id`(filter_id, result_id, location, send_status, addtime) VALUES (0, ' . $result_id . ' , \'update_result.php\', 0, ' . NV_CURRENTTIME . ')');
        }
    } catch (PDOException $e) {
        pr($e);
    }
}

file_put_contents($filename, $id_tmbt);

echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '');

