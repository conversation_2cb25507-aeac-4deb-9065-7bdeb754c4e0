<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

const PTS = [
    '1_mths' => 11,
    'một giai đoạn một túi hồ sơ' => 11,
    '1 giai đoạn 1 túi hồ sơ' => 11,

    '1_hths' => 12,
    'một giai đoạn hai túi hồ sơ' => 12,
    '1 giai đoạn 2 túi hồ sơ' => 12,

    '2_mths' => 21,
    'hai giai đoạn một túi hồ sơ' => 21,
    '2 giai đoạn 1 túi hồ sơ' => 21,

    '2_hths' => 22,
    'hai giai đoạn hai túi hồ sơ' => 22,
    '2 giai đoạn 2 túi hồ s<PERSON>' => 22,
];

$id = 0;
$dem = 0;
try {

    $maxid = $db->query("SELECT MAX(id) FROM `nv4_vi_bidding_plans`")->fetchColumn();

    do {
        $num_rows = 0;
        $id2 = $id + 1000;

        $query_url = $db->query("SELECT * FROM `nv4_vi_bidding_plans` WHERE id > " . $id . " AND id < " . $id2 . " ORDER BY `id` ASC LIMIT 100");
        $id = 0;
        
        $violated_ids = [];
        $params = [];
        
        while ($item = $query_url->fetch()) {
            $item['nam_dang_tai'] = getdate($item['addtime'])['year'];
        
            $is_violated = 0;
            
            // Tìm $arr_contract
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_bidding_plans_contract WHERE id_plan IN (' . $item['id'] . ')');
            $arr_contract = $result->fetchAll();
            
            // Chỉ tính những gói thầu trong phạm vi điều chỉnh của Luật Đấu thầu
            $arr_contract = array_filter($arr_contract, fn($t) => $t['process_apply'] == 'LDT');
            
            if ($item['nam_dang_tai'] >= 2022) {
                $arr_contract = array_filter($arr_contract, fn($t) => !isMixPt21Pt22PlanContract($t));
            }
            
            if ($item['nam_dang_tai'] >= 2024) {
                $arr_contract = array_filter($arr_contract, fn($t) => $t['is_domestic']);
            }
            
            // Tổng số gói thầu thực hiện đấu thầu (Ko tính chỉ định thầu và tự thực hiện)
            $arr_bidded = array_filter($arr_contract, function ($contract)
            {
                if (empty($contract['type_choose_list'])) return false;
                $type_list = is_string($contract['type_choose_list']) ? explode(',', $contract['type_choose_list']) : $contract['type_choose_list'];
                return !in_array('23', $type_list) and
                    !in_array('13', $type_list) and
                    !in_array('14', $type_list);
            });
            $count_bid = count($arr_bidded);
            $bid_value = array_sum(array_map(fn($contract) => $contract['price'], $arr_bidded));
                
            // Tổng số gói thầu thực hiện qua mạng
            $arr_internet = array_filter($arr_contract, function($contract) use ($item)
            {
                if (empty($contract['type_choose_list'])) return false;
                $type_list = is_string($contract['type_choose_list']) ? explode(',', $contract['type_choose_list']) : $contract['type_choose_list'];
                return $item['is_new_msc'] ? 
                $contract['is_internet'] :
                in_array('6', $type_list);
            });
            $count_internet = count($arr_internet);
            $internet_value = array_sum(array_map(fn($contract) => $contract['price'], $arr_internet));
                
            // Tổng số gói thầu thực hiện DTRR, CHCT
            $arr_dtrr_chct = array_filter($arr_contract, function ($contract)
            {
                if (empty($contract['type_choose_list'])) return false;
                $type_list = is_string($contract['type_choose_list']) ? explode(',', $contract['type_choose_list']) : $contract['type_choose_list'];
                return in_array('17', $type_list) or
                    in_array('11', $type_list) or
                    in_array('12', $type_list);
            });
            $count_dtrr_chct = count($arr_dtrr_chct);
            $dtrr_chct_value = array_sum(array_map(fn($contract) => $contract['price'], $arr_dtrr_chct));
                
            // Tổng số gói thầu thực hiện DTRR, DTHC, CHCT
            $arr_dtrr_dthc_chct = array_filter($arr_contract, function ($contract)
            {
                if (empty($contract['type_choose_list'])) return false;
                $type_list = is_string($contract['type_choose_list']) ? explode(',', $contract['type_choose_list']) : $contract['type_choose_list'];
                return in_array('17', $type_list) or
                    in_array('11', $type_list) or
                    in_array('16', $type_list) or
                    in_array('12', $type_list);
            });
            $count_dtrr_dthc_chct = count($arr_dtrr_dthc_chct);
            $dtrr_dthc_chct_value = array_sum(array_map(fn($contract) => $contract['price'], $arr_dtrr_dthc_chct));
            
            if ($item['nam_dang_tai'] == 2020 && $count_bid && $bid_value) {
                if ($count_internet / $count_bid * 100 < 60 or 
                    $internet_value / $bid_value * 100 < 25
                ) {
                    $is_violated = 1;
                }
            }
            
            if ($item['nam_dang_tai'] == 2021 && $count_bid && $bid_value) {
                if ($count_internet / $count_bid * 100 < 70 or 
                    $internet_value / $bid_value * 100 < 35
                ) {
                    $is_violated = 1;
                }
            }
            
            if ($item['nam_dang_tai'] == 2022 && $count_dtrr_chct && $dtrr_chct_value) {
                if ($count_internet / $count_dtrr_chct * 100 < 80 or 
                    $internet_value / $dtrr_chct_value * 100 < 70
                ) {
                    $is_violated = 1;
                }
                
                // Tất cả chi thường xuyên
                if ($item['classify'] == 'TX' and 
                    $internet_value / $dtrr_chct_value * 100 < 100
                ) {
                    $is_violated = 1;
                }
            }
            
            if ($item['nam_dang_tai'] == 2023 && $count_dtrr_dthc_chct && $dtrr_dthc_chct_value) {
                if ($count_internet / $count_dtrr_dthc_chct * 100 < 90 or 
                    $internet_value / $dtrr_dthc_chct_value * 100 < 80
                ) {
                    $is_violated = 1;
                }
                
                // Tất cả chi thường xuyên
                if ($item['classify'] == 'TX' and 
                    $internet_value / $dtrr_dthc_chct_value * 100 < 100
                ) {
                    $is_violated = 1;
                }
            }
            
            if ($item['nam_dang_tai'] >= 2024 && $count_dtrr_dthc_chct && $dtrr_dthc_chct_value) {
                if ($count_internet / $count_dtrr_dthc_chct * 100 < 95 or 
                    $internet_value / $dtrr_dthc_chct_value * 100 < 90
                ) {
                    $is_violated = 1;
                }
                
                // Tất cả chi thường xuyên
                if ($item['classify'] == 'TX' and 
                    $internet_value / $dtrr_dthc_chct_value * 100 < 100
                ) {
                    $is_violated = 1;
                }
            }
            
            if ($is_violated) {
                $violated_ids[] = $item['id'];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'dauthau_plans',
                        '_id' => $item['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 1
                    ]
                ];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'en_dauthau_plans',
                        '_id' => $item['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 1
                    ]
                ];
            } else {
                $params['body'][] = [
                    'update' => [
                        '_index' => 'dauthau_plans',
                        '_id' => $item['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 0
                    ]
                ];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'en_dauthau_plans',
                        '_id' => $item['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 0
                    ]
                ];
            }
            
            $id = $item['id'];
            $num_rows++;
            $dem++;
        }
        $query_url->closeCursor();
        
        if (!empty($violated_ids)) {
            $str_ids = implode(',', $violated_ids);
            $db->exec('UPDATE nv4_vi_bidding_plans SET is_violated = 1 WHERE id IN (' . $str_ids . ')');
            $db->exec('UPDATE nv4_en_bidding_plans SET is_violated = 1 WHERE id IN (' . $str_ids . ')');
        }
        
        if (!empty($params['body'])) {
            $hosts = array(
                $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
            );
    
            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();
            $responses = $client->bulk($params)->asArray();
            $params = null;
            if (!empty($responses['items'])) {
                foreach ($responses['items'] as $value) {
                    if ($value['update']['status'] == 404) {
                        if (str_starts_with($value['update']['_index'], 'en_')) {
                            $db->exec('UPDATE nv4_en_bidding_plans SET elasticsearch=0 WHERE id = ' . $value['update']['_id']);
                        } else {
                            $db->exec('UPDATE nv4_vi_bidding_plans SET elasticsearch=0 WHERE id = ' . $value['update']['_id']);
                        }
                    } elseif ($value['update']['status'] != 200) {
                        print_r("Xử lý id: \033[33m" . $value['update']['_id']);
                        print_r("\033[31m ERROR\033[0m\n");
                        file_put_contents(NV_ROOTDIR . '/tools/check_violate_khlcnt.log', (str_starts_with($value['update']['_index'], 'en_') ? 'en' : 'vi')  . ' ' . $value['update']['_id'] . PHP_EOL, FILE_APPEND);
                    }
                }
            }
            $responses = null;
            $client = null;
        }

        echo 'Check violate Plan id: ' . number_format($id) . '/' . number_format($maxid) . PHP_EOL;
        if ($id == 0 and $id2 < $maxid) {
            $id = $id2;
        } elseif ($id == 0 and $id2 >= $maxid) {
            break;
        }
    } while ($id < $maxid);
} catch (PDOException $e) {
    trigger_error(print_r($e, true));
    die($e->getMessage());
}
echo "Totally checked violated Plan: " . $dem . " - Kết thúc!!!\n\n";

function isMixPt21Pt22PlanContract($item)
{
    $lv = $item['linh_vuc'] ? trim(strtolower($item['linh_vuc'])) : '';
    $pt = $item['method_choose'] ? PTS[trim(strtolower($item['method_choose']))] : 0;

    return $lv == 'hỗn hợp' || $lv == 'hon_hop' || $pt == 21 || $pt == 22;
}
