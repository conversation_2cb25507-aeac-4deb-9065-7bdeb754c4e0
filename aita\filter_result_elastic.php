<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */

$waitTimeoutInSeconds = 2;
$filters_mail_result_log = NV_ROOTDIR . '/aita/data/filters_mail_result_log_' . date('Ymd') . '.txt';

if ($fp = fsockopen($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
    file_put_contents($filters_mail_result_log, "Server Elasticsearch didn't work.\n", FILE_APPEND);
    die();
}

fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
}
/*
 * Thuật toán như sau:
 * - Bảng nv4_vi_aita_result_logs có trường
 * - status: Trạng thái bằng 0 là đang chạy, nếu truy cập lại thì tiếp tục chạy.
 * - user: ID của users đã chạy đến, Mỗi lần tìm kiếm chỉ khoảng 2 tài khoản để test. (Sau đó có thể nâng lên 50 khi số lựong nhiều)
 * - Bảng nv4_vi_aita_result_logs được thêm mới khi không tìm thấy status = 0 và có thêm bài đăng.
 * - Khi tìm kiếm theo từng tài khoản, sẽ tìm lần luợt bộ lọc của từng người, nếu tìm thấy thì lưu vào bảng nv4_vi_bidding_mail để chương trình send_mail.php lấy ra để gửi.
 */

$new_loop = 0;
// Bước 1: Kiểm tra có bảng logs nào đang chạy không, nếu không thì bắt đầu chạy mới, nếu có thì tiếp tục chạy đến userid tiếp theo
$logs_info = $db->query('SELECT * FROM `nv4_vi_aita_result_logs` WHERE status=0 ORDER BY `id` DESC limit 1')->fetch();

if (!empty($logs_info)) {
    echo "\n\nLogid: " . $logs_info['id'] . " date: " . nv_date('H:i:s d/m/Y', $logs_info['from_time']) . "\n";
    $start_result_id = $logs_info['from_id'];
    $end_result_id = $logs_info['to_id'];
} else {
    // Nếu không có phần nào đang chạy thì kiểm tra xem có tin đấu thầu mới không. Nếu có tin mới thì chạy vòng lặp mới, nếu không thì kết thúc
    $last_logs = $db->query('SELECT * FROM `nv4_vi_aita_result_logs` ORDER BY id DESC limit 1')->fetch();

    $array_query_elastic = array();
    $array_query_elastic['size'] = 1;
    $array_query_elastic['sort'] = [
        [
            "id" => [
                "order" => "desc"
            ]
        ]
    ];
    $array_query_elastic['_source'] = array(
        'id'
    );
    $params = array();
    $params['index'] = 'dauthau_result';

    $params['body'] = $array_query_elastic;
    $response = $client->search($params)->asArray();
    if ($response['hits']['total']['value'] > 0) {
        $info_result['to_id'] = $response['hits']['hits'][0]['_source']['id'];
    }
    if (!isset($info_result['to_id']) or $info_result['to_id'] <= 0) {
        file_put_contents($filters_mail_result_log, "Lỗi maxid ES \n", FILE_APPEND);
        die('Lỗi');
    }
    if (empty($last_logs)) {
        $query = $db->query('SELECT min(`id`) FROM `nv4_vi_bidding_result` WHERE `finish_time`>=' . $check_time_aita);
        $start_result_id = $query->fetchColumn(); // Chạy lần đầu
        $end_result_id = $info_result['to_id'];
        $new_loop = 1;
    } else {
        if ($last_logs['to_id'] < $info_result['to_id']) {
            // Nếu có kế hoạch chọn nhà thầu mới thì lấy ra các bài mới để chạy vòng lặp mới
            $start_result_id = $last_logs['to_id']; // Lấy id result của lần cuối cùng chạy
            $end_result_id = $info_result['to_id']; // Lấy id result lớn nhất của dữ liệu hiện có
            $new_loop = 1;
        } else {
            file_put_contents($filters_mail_result_log, "Không có tin mới trong csdl. Hãy chạy tiếp khi có kế hoạch LCNT đăng mới\n\n", FILE_APPEND);
            // Nếu không có dữ liệu mới để chạy thì dừng lại ngay
            die('Không có KQLCNT mới trong csdl');
        }
    }
}

$time = time();
$start_time = $time;
if ($new_loop) {
    if ($end_result_id > $start_result_id + 5000) {
        // Nếu có quá nhiều KQLCNT thì chỉ tìm kiếm trong 5000 KQLCNT
        $end_result_id = $start_result_id + 5000;
    }

    if ($start_result_id == $end_result_id) {
        die('No Data new');
    }

    try {
        // thêm logs mới vào csdl
        $stmt = $db->prepare('INSERT INTO nv4_vi_aita_result_logs (from_id, to_id, from_time, to_time, total_time, status) VALUES ( :from_id, :to_id, :from_time, :to_time, 0, 0)');
        $stmt->bindParam(':from_id', $start_result_id, PDO::PARAM_INT);
        $stmt->bindParam(':to_id', $end_result_id, PDO::PARAM_INT);
        $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
        $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
        $exc = $stmt->execute();
        $logs_id = $db->lastInsertId();
        $run_time = 0;
    } catch (PDOException $e) {
        file_put_contents($filters_mail_result_log, "Lỗi thêm logs mới vào csdl L94\n", FILE_APPEND);
        die($e->getMessage()); // Remove this line after checks finished
    }
} else {
    $logs_id = $logs_info['id'];
    $run_time = $logs_info['total_time'];
}
$arr_filter = [];
// Lấy ra ds các bộ lọc của AITA
$query_filter = $db->query('SELECT * FROM `nv4_vi_aita_filter` WHERE status=1 ORDER BY weight ASC');
while ($result = $query_filter->fetch()) {
    $arr_filter[$result['id']] = $result;
}

$new_result_id = 0;

if (empty($arr_filter)) {
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $change = $db->query('UPDATE nv4_vi_aita_result_logs SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_result_log, "Không có bộ lọc", FILE_APPEND);
    die();
} else {
    file_put_contents($filters_mail_result_log, "ArrayID results:" . $start_result_id . "-" . $end_result_id . ";\n", FILE_APPEND);
    $db->beginTransaction();
    try {
        foreach ($arr_filter as $filter_id => $filter) {
            file_put_contents($filters_mail_result_log, "Bắt đầu quét:" . $filter_id . ";", FILE_APPEND);
            if (!empty($filter)) {
                // Từ bộ lọc chọn ra điều kiện lọc where tương ứng
                $search_elastic = array();
                $search_elastic['must'][]['range']['id'] = [
                    "gt" => $start_result_id,
                    "lte" => $end_result_id
                ];

                $search_elastic['must'][]['range']['finish_time'] = [
                    "gte" => $check_time_aita
                ];

                if (!empty($filter['key_search'])) {
                    $arr_key = explode(',', $filter['key_search']);
                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $search_elastic['should'][] = [
                            "match_phrase" => [
                                "title" => $key
                            ]
                        ];

                        $search_elastic['should'][] = [
                            "match_phrase" => [
                                "project_name " => $key
                            ]
                        ];
                    }

                    $search_elastic['minimum_should_match'] = '1';
                    $search_elastic['boost'] = '1.0';
                }

                if (!empty($filter['key_search2'])) {
                    $arr_key = explode(',', $filter['key_search2']);
                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $search_elastic['must'][] = [
                            "match_phrase" => [
                                "title" => $key
                            ]
                        ];

                        $search_elastic['must'][] = [
                            "match_phrase" => [
                                "project_name" => $key
                            ]
                        ];
                    }
                }

                if (!empty($filter['without_key'])) {
                    $arr_key = explode(',', $filter['without_key']);
                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $search_elastic['must_not'][] = [
                            "match_phrase" => [
                                "title" => $key
                            ]
                        ];

                        $search_elastic['must_not'][] = [
                            "match_phrase" => [
                                "project_name" => $key
                            ]
                        ];
                    }
                }

                $array_query_elastic = array();
                if (!empty($search_elastic)) {
                    $array_query_elastic['query']['bool'] = $search_elastic;
                }
                $array_query_elastic['size'] = 1000;
                $array_query_elastic['sort'] = [
                    [
                        "finish_time" => [
                            "order" => "desc"
                        ]
                    ]
                ];

                $array_query_elastic['_source'] = array(
                    'id',
                    'code',
                    'plan_code'
                );

                $params = array();
                $params['index'] = 'dauthau_result';
                // $params['type'] = 'nv4_vi_bidding_results';
                $params['body'] = $array_query_elastic;
                $response = $client->search($params)->asArray();
                $time = NV_CURRENTTIME;
                $location = "filter_result_elastic";

                /*
                 * TH1: kết quả thông thường: có TBMT:
                 * - TBMT không có từ khoá CNTT: Nếu KQLCNT có thì lấy luôn TBMT
                 * - TBMT có từ khóa trùng khớp: Nếu có rồi thì hệ thống tự đồng bộ với KQLCNT rồi, không cần xử lý thêm.
                 * TH2: kết quả chỉ định thầu.. không có TBMT mà có KHLCNT,
                 * - Mà KHLCNT này không có từ khóa CNTT: Nếu KQLCNT có thì lấy luôn KHLCNT
                 * - KHLCNT có từ khóa trùng khớp: -> lấy dữ liệu.
                 * - TH đã có dữ liệu trên dauthau.asia ngay lúc lọc KHLCNT: tool filter KHLCNT đã tự động đồng bộ, không cần xử lý thêm
                 * - TH chưa có dữ liệu trên dauthau.asia, sau khi lọc KHLCNT xong mới có: xử lý tại đây
                 */
                foreach ($response['hits']['hits'] as $value) {
                    if (!empty($value['_source'])) {
                        $result = $value['_source'];
                        // Kiểm tra chưa được cập nhật sang aita thì mới tiếp tục
                        $_count = $db->query("SELECT COUNT(*) FROM nv4_vi_aita_result_id WHERE result_id=" . $result['id'])->fetchColumn();
                        if ($_count == 0) {
                            // Trong mọi trường hợp thì đều insert result khi đã đến bước này
                            $stmt = $db->prepare('INSERT INTO `nv4_vi_aita_result_id`(filter_id, result_id, location, send_status, addtime) VALUES (:filter_id, :result_id, :location, 0, :addtime)');
                            $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                            $stmt->bindParam(':result_id', $result['id'], PDO::PARAM_INT);
                            $stmt->bindParam(':location', $location, PDO::PARAM_STR);
                            $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                            $exc = $stmt->execute();
                            if ($exc) {
                                ++$new_result_id;
                            }
                        }
                    }
                }
            }
        }
        $db->commit();
    } catch (PDOException $e) {
        file_put_contents($filters_mail_result_log, "Lỗi INSERT  L401:\n" . print_r($e, true) . "\n", FILE_APPEND);
        $db->rollBack();
        die(); // Remove this line after checks finished
    }

    $time = time();
    $this_time = $time - $start_time;
    $total_time = $run_time + $this_time;
    try {
        $db->query('UPDATE nv4_vi_aita_result_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ', status=1 where id =' . $logs_id);
    } catch (PDOException $e) {
        file_put_contents($filters_mail_result_log, "Lỗi update logs \n" . print_r($e, true) . "\n", FILE_APPEND);
        die();
    }

    $note = 'L420 Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
    file_put_contents($filters_mail_result_log, $note . "\n", FILE_APPEND);
    die($note . "\n new_result_id=" . $new_result_id . "\n");
}

/**
 *
 * @param array $data
 * @return string
 */
function debugArray($data)
{
    return "<pre><code>" . print_r($data, true) . "</code></pre>";
}
