<?php

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$hosts = array(
    $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
);

$client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
->setHosts($hosts)
->setRetries(0)
->build();

$array_index = [
    'dauthau_solicitor'
];

foreach ($array_index as $my_index) {
    try {
        // Get mappings
        $params = [
            'index' => $my_index
        ];
        $response = $client->indices()->getMapping($params);
        $properties = $response[$my_index]['mappings']['properties'];
        $properties['english_name']['type'] = 'text';

        $params = [
            'index' => $my_index,
            'body' => [
                'properties' => $properties
            ]
        ];
        // Update the index mapping
        $client->indices()->putMapping($params);

        // Cập nhật giá trị mặc định
        $params = [
            'index' => $my_index,
            'body' => [
                'query' => [
                    'match_all' => (object) []
                ],
                'script' => [
                    'source' =>
                    "ctx._source.english_name = '';"
                ]
            ]
        ];
        $response = $client->updateByQuery($params);
        print_r($response);
    } catch (Exception $e) {
        echo 3;
        print_r($e->getMessage());
    }
}

die('đã thực hiện xong');
