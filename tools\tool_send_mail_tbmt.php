<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

require NV_ROOTDIR . '/vendor/autoload.php';
$site_timezone = 'Asia/Saigon';
date_default_timezone_set($site_timezone);

$waitTimeoutInSeconds = 2;
if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
}
fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setHosts($hosts)
        ->setRetries(0)
        ->build();
}

$start_id = 0;
$info = $db->query('SELECT max(id) as to_id FROM `nv4_vi_bidding_row`')->fetch();
$end_id = $info['to_id']; // Lấy id mời thầu lớn nhất của dữ liệu hiện có
$time = time();
$start_time = $time;
$arr_vip = array();
$time_start_thisday = mktime(date('H'), date('i'), date('s'), date('n'), date('j'), date('Y'));
$testUserid = 13;

// Test 1 tài khoản VIP
$query_vip = $db->query('SELECT * FROM `nv4_vi_bidding_customs` WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id =' . $testUserid . ' AND (vip = 1 or vip = 19)');
while ($vip_info = $query_vip->fetch()) {
    $arr_vip[$vip_info['user_id']] = $vip_info;
}
//https://dauthau.info/detail/?is_advance=1&type_search=1&type_info=1&type_info2=1&q=v%E1%BA%ADn+h%C3%A0nh+t%C3%B2a+nh%C3%A0%2C+v%E1%BA%ADn+h%C3%A0nh+nh%C3%A0%2C+v%E1%BA%ADn+h%C3%A0nh+tr%E1%BB%A5+s%E1%BB%9F%2C+v%E1%BA%ADn+h%C3%A0nh+kh%E1%BB%91i+nh%C3%A0%2C+qu%E1%BA%A3n+l%C3%BD+t%C3%B2a+nh%C3%A0&sfrom=01%2F01%2F2019&sto=03%2F10%2F2019&q2=&without_key=mua%2C+mua+s%E1%BA%AFm%2C+nghi%C3%AAn+c%E1%BB%A9u%2C+t%E1%BB%91i+%C6%B0u%2C+b%E1%BA%A3o+hi%E1%BB%83m&cat=0&field%5B%5D=4&field%5B%5D=5&money_from=&money_to=&price_from=&price_to=&invest_from=&invest_to=&price_plan_from=&price_plan_to=&catressult=0&win_price_from=&win_price_to=

$filter = array();
$filter['type'] = 0;
$filter['time_find'] = 200;
$filter['cat'] = 0;
$filter['type_org'] = 0;
$filter['money_from'] = 0;
$filter['money_to'] = 0;
$filter['price_from'] = 0;
$filter['price_to'] = 0;
$filter['field'] = '4,5';
$filter['search_type'] = 0;
$filter['without_key'] = 'mua, mua sắm, nghiên cứu, tối ưu, bảo hiểm';
$filter['key_search2'] = '';
$filter['key_search'] = 'vận hành tòa nhà, vận hành nhà, vận hành trụ sở, vận hành khối nhà, quản lý tòa nhà';

$db->beginTransaction();
try {
    foreach ($arr_vip as $vip_id => $vip) {
        echo "Bắt đầu quét:" . $vip_id . "\n";
        //Từ bộ lọc chọn ra điều kiện lọc where tương ứng
        $search_elastic = array();
        $search_elastic['must'][]['range']['id'] = [
            "gt" => $start_id,
            "lte" => $end_id
        ];

        // tìm kiếm theo khoảng thời gian
        if ($filter['time_find'] > 0) {
            $time_find = mktime(0, 0, 0, date('n'), date('j'), date('Y')) - 86400 * $filter['time_find'];
            if ($filter['type'] == 0) {
                $search_elastic['must'][]['range']['ngay_dang_tai'] = [
                    "gte" => $time_find
                ];
            } else {
                $search_elastic['must'][]['range']['den_ngay'] = [
                    "gte" => $time_find
                ];
            }
        }
        if ($filter['cat'] > 0) {
            $search_elastic['must'][] = [
                'match' => [
                    'type_bid' => [
                        'query' => $filter['cat'] == 1 ? "1" : "0"
                    ]
                ]
            ];
        }
        if ($filter['type_org'] == 2) {
            $search_elastic['must'][] = [
                'match' => [
                    'type_org' => [
                        'query' => "1"
                    ]
                ]
            ];
        }

        if (($filter['money_from'] > 0 or $filter['money_to'] > 0) and ($filter['money_to'] > $filter['money_from'])) {
            $search_elastic['must'][]['range']['money_bid'] = [
                "gte" => $filter['money_from'],
                "lte" => $filter['money_to']
            ];
        }

        // tìm theo giá mời thầu
        if (($filter['price_from'] > 0 or $filter['price_to'] > 0) and ($filter['price_to'] > $filter['price_from'])) {
            $search_elastic['must'][]['range']['price'] = [
                "gte" => $filter['price_from'],
                "lte" => $filter['price_to']
            ];
        }

        // tim kiếm theo lĩnh vực thông báo
        if (!empty($filter['field'])) {
            $search_field = array();
            $filter['field'] = explode(',', $filter['field']);
            foreach ($filter['field'] as $key) {
                $search_field[] = [
                    'term' => [
                        'linh_vuc_thong_bao' => $key
                    ]
                ];
            }
            $search_elastic['must'][]['bool']['should'] = $search_field;
        } else {
            $search_elastic['must_not'][] = [
                'term' => [
                    'linh_vuc_thong_bao' => 20
                ]
            ];
        }

        if (!empty($filter['without_key'])) {
            $arr_key = explode(',', $filter['without_key']);
            foreach ($arr_key as $key) {
                if ($filter['search_type'] == 1) {
                    $key = trim($key);
                    $search_elastic['must_not'][] = [
                        "match_phrase" => [
                            "content_full" => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_elastic['must_not'][] = [
                        "match_phrase" => [
                            "content" => $key
                        ]
                    ];
                }
            }
        }

        if (!empty($filter['key_search2'])) {
            $arr_key = explode(',', $filter['key_search2']);
            foreach ($arr_key as $key) {
                if ($filter['search_type'] == 1) {
                    $key = trim($key);
                    $search_elastic['must'][] = [
                        "match_phrase" => [
                            "content_full" => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_elastic['must'][] = [
                        "match_phrase" => [
                            "content" => $key
                        ]
                    ];
                }
            }
        }

        if (!empty($filter['key_search'])) {
            $arr_key = explode(',', $filter['key_search']);
            foreach ($arr_key as $key) {
                if ($filter['search_type'] == 1) {
                    $key = trim($key);
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "content_full" => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "content" => $key
                        ]
                    ];
                }
            }

            $search_elastic['minimum_should_match'] = '1';
            $search_elastic['boost'] = '1.0';
        }

        $array_query_elastic = array();
        if (!empty($search_elastic)) {
            $array_query_elastic['query']['bool'] = $search_elastic;
        }
        $array_query_elastic['size'] = 500;
        $array_query_elastic['sort'] = [
            [
                "ngay_dang_tai" => [
                    "order" => "desc"
                ]
            ]
        ];

        $params = array();
        $params['index'] = 'dauthau_bidding';
        $params['type'] = 'nv4_vi_bidding_row';
        $params['body'] = $array_query_elastic;
        $response = $client->search($params)->asArray();

        $array_bidding = array();
        foreach ($response['hits']['hits'] as $value) {
            if (!empty($value['_source'])) {
                $bidding = $value['_source'];
                $array_bidding[$bidding['id']] = $bidding;
                $query = $db->query('SELECT * FROM nv4_vi_bidding_detail WHERE id = ' . $bidding['id']);
                if ($bid_row = $query->fetch()) {
                    $bidding = array_merge($bid_row, $bidding);
                }
                $array_bidding[$bidding['id']] = $bidding;
            }
        }
        if (!empty($array_bidding)) {
            $data_insert = array();
            $data_insert['title'] = 'Thông báo Có tin mới đấu thầu ngày ' . date('d/m/Y');
            $data_insert['type'] = 0;
            $data_insert['vip'] = 1;
            $data_insert['addtime'] = NV_CURRENTTIME;
            $data_insert['send_time'] = 0;
            $data_insert['status'] = 0;
            $data_insert['content'] = nv_theme_bidding_mail($filter, $array_bidding, $vip_id);
            //Nội dung htm sẽ gửi cho từng khách
            $stmt = $db->prepare("INSERT INTO nv4_vi_bidding_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '')");
            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
            $exc = $stmt->execute();
        }
    }

    $db->commit();
} catch (PDOException $e) {
    $db->rollBack();
    print_r($e);
    die(); //Remove this line after checks finished
}

$time = time();
$this_time = $time - NV_CURRENTTIME;

$note = 'L420 Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
die($note . "\n");

function nv_theme_bidding_mail($array_filter, $array_bidding, $vip_id)
{
    global $db, $lang_module;

    if (!empty($array_filter)) {
        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        include NV_ROOTDIR . '/create_mail/vi.php';
        $xtpl->assign('LANG', $lang_module);
        //Lấy tên bộ lọc
        //print($filter_name);
        $xtpl->assign('FILTER_NAME', 'Bộ lọc gửi mail test');

        if (!empty($array_bidding)) {
            if (sizeof($array_bidding) > 50) {
                $xtpl->assign('FILTER_NUMBER', number_format(sizeof($array_bidding)));
                $xtpl->parse('main.filter.number');
            }

            $i_break = 0;

            // lọc bỏ tin trùng nhau
            $arr_stbmt = array();
            foreach ($array_bidding as $array_data) {
                $array_data['so_tbmt'] = explode('-', $array_data['so_tbmt']);
                if (!isset($arr_stbmt[$array_data['so_tbmt'][0]])) {
                    $arr_stbmt[$array_data['so_tbmt'][0]] = $array_data['so_tbmt'][1];
                } else {
                    $array_data['so_tbmt'][1] = intval($array_data['so_tbmt'][1]);
                    $old = intval($arr_stbmt[$array_data['so_tbmt'][0]]);
                    if ($old < $array_data['so_tbmt'][1]) {
                        $arr_stbmt[$array_data['so_tbmt'][0]] = '0' . $array_data['so_tbmt'][1];
                    }
                }
            }
            // hiển thị
            foreach ($array_bidding as $array_data) {
                $so_tbmt = explode('-', $array_data['so_tbmt']);
                if (isset($arr_stbmt[$so_tbmt[0]]) && intval($arr_stbmt[$so_tbmt[0]]) == intval($so_tbmt[1])) {
                    ++$i_break;
                    if ($i_break > 50) {
                        break;
                    }
                    $array_data['title'] = $array_data['goi_thau'];
                    $array_data['title_a'] = nv_htmlspecialchars($array_data['goi_thau']);

                    //Bôi vàng những từ trùng với từ khóa tìm kiếm, tìm kiếm tuyệt đối
                    $arr_key = explode(',', $array_filter['key_search']);

                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $array_data['goi_thau'] = BoldKeywordInStr(strip_tags($array_data['goi_thau']), $key);
                        $array_data['ben_moi_thau'] = strip_tags(BoldKeywordInStr($array_data['ben_moi_thau'], $key));
                    }
                    $array_data['ngay_dang_tai'] = nv_date('H:i d/m/y', $array_data['ngay_dang_tai']);
                    $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/view?id=' . $array_data['id'];

                    $check = md5($vip_id . $so_tbmt[0]);
                    $array_data['link_follow'] = NV_MY_DOMAIN . '/' . $site_lang . '/bidding/follow?bid_id=' . $array_data['id'] . '&bid_code=' . $so_tbmt[0] . '&vipid=' . $vip_id . '&check=' . $check;

                    $xtpl->assign('DATA', $array_data);

                    if ($array_data['chu_dau_tu'] != '') {
                        $xtpl->parse('main.filter.content.chu_dau_tu');
                    }

                    if ($array_data['ten_du_an'] != '') {
                        $xtpl->parse('main.filter.content.ten_du_an');
                    }

                    if ($array_data['notify_chance_time'] == 1) {
                        $content_chance = $db->query('SELECT content_chance FROM nv4_vi_bidding_update WHERE bid_id=' . $array_data['id'] . ' ORDER BY addtime DESC')->fetchColumn();
                        $xtpl->assign('CONTENT_CHANCE', $content_chance);
                        $xtpl->parse('main.filter.content.notify_chance_time');
                    } else {
                        //Tạo link theo dõi tin thầu
                        $xtpl->parse('main.filter.content.follow');
                    }

                    if (intval($so_tbmt[1]) > 0) {
                        $xtpl->parse('main.filter.content.notify_old');
                    }

                    if ($array_data['note'] != '') {
                        $xtpl->parse('main.filter.content.note');
                    }

                    $xtpl->parse('main.filter.content');
                }
            }
        }
        $xtpl->parse('main.filter');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}
