<?php

function nv_theme_remind_mail($list_remind_bid, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func;


    $xtpl = new XTemplate('email_remind_nop_hsdt.tpl', NV_ROOTDIR . '/create_mail');
    $xtpl->assign('LANG', $lang_module);

    foreach ($list_remind_bid as $data) {

        $tbmt = explode('-', $data['so_tbmt']);
        $array_codetbmt = $tbmt[0];

        if (!empty($data['chu_dau_tu'])) {
            $xtpl->parse('main.bid_item.chu_dau_tu');
        }

        if (!empty($data['ten_du_an'])) {
            $xtpl->parse('main.bid_item.ten_du_an');
        }
        
        $tbmt_t = getUrlByLanguage('tbmt');
        
        if ($site_lang != 'vi') {
            $data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $data['alias'] . '-' . $data['id'] . '.html';
        } else {
            $data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $data['alias'] . '-' . $data['id'] . '.html';
        }
        
        $xtpl->assign('DATA', $data);
        $xtpl->parse('main.bid_item');
    }

    // chỉ lấy đại diện 1 tin theo dõi
    $mail_footer_follow = '';
    if (!empty($array_codetbmt)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date('d/m/Y', $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}
