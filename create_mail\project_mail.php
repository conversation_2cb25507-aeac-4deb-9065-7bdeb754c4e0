<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$create_mail_file = NV_ROOTDIR . '/data/create_mail_project_' . $prefix_lang . '_' . date('Ymd') . '.txt';
if (file_exists(NV_ROOTDIR . '/data/create_mail_project_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "project_mail.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit();
}
file_put_contents(NV_ROOTDIR . '/data/create_mail_project_' . $prefix_lang . '.txt', NV_CURRENTTIME);

try {
    // Từ bảng tmp tổng hợp lại mail gửi đi cho từng VIP
    // Lấy danh sách các mail chưa gửi trong csdl để tiến hành gửi đi gom theo userid
    $vips = array();
    $list_vipid = array();
    $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id WHERE userid IN (SELECT user_id FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE ((' . NV_CURRENTTIME . ' - last_email) >= time_send*3600) AND vip=4 AND status = 1 AND prefix_lang = ' . $prefix_lang . ' ORDER BY last_email ASC) AND send_status = 0 LIMIT 20');
    while ($row = $query->fetch()) {
        $vips[$row['userid']] = array();
        $list_vipid[$row['userid']] = $row['userid'];
    }

    if (!empty($list_vipid)) {
        $arr_vip = $db->query('SELECT id as cusid, phone, user_id as userid, email, sub_email, time_send, last_email, active_user, from_time FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id IN ( ' . implode(',', $list_vipid) . ') AND vip=4 AND prefix_lang = ' . $prefix_lang . '');
        while ($vip_data = $arr_vip->fetch()) {
            // nếu vip này bật chế độ gửi email theo tài khoản
            if ($vip_data['active_user'] == 1) {
                $arr_subemail = [];
                $query_permission = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_permission WHERE vipid=' . $vip_data['cusid'] . ' AND totime >' . NV_CURRENTTIME . ' AND send_mail = 1 AND prefix_lang = ' . $prefix_lang . '');
                while ($permission = $query_permission->fetch()) {
                    $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid = ' . $permission['userid'] . '');
                    while ($user = $arr_user->fetch()) {
                        $arr_subemail[$user['email']] = $user['email'];
                    }
                }
                $vip_data['sub_email'] = implode(',', $arr_subemail);
            }

            $vips[$vip_data['userid']] = $vip_data;
            $vips[$vip_data['userid']]['filter'] = array();
            $vips[$vip_data['userid']]['bid'] = array();
            $vips[$vip_data['userid']]['plans'] = array();
            $vips[$vip_data['userid']]['result'] = array();
            $vips[$vip_data['userid']]['cbdmda'] = array();
            $vips[$vip_data['userid']]['tbmst'] = array();
            $vips[$vip_data['userid']]['kqst'] = array();
            $vips[$vip_data['userid']]['hopdong'] = array();
        }
    }

    if (!empty($vips)) {
        // lấy username
        $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid IN ( ' . implode(',', array_keys($vips)) . ')');
        while ($user = $arr_user->fetch()) {
            $vips[$user['userid']]['username'] = $user['username'];
        }

        // Lấy ra tất cả tin đấu thầu trong danh sách gửi đi của tất cả các VIP
        // Lưu nội dung mail vào csdl
        foreach ($vips as $vip_id => $vip) {
            file_put_contents($create_mail_file, 'BEGIN: ' . $vip_id . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
            $arr_bid = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_row a INNER JOIN ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_detail b ON a.id=b.id WHERE a.id IN (SELECT bid_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id WHERE send_status = 0 AND bid_id>0 AND userid=' . $vip_id . ')');
            while ($bid_row = $query->fetch()) {
                $arr_bid[$bid_row['id']] = $bid_row;
            }
            $arr_plans = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_plans_project WHERE id IN (SELECT plan_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id WHERE send_status = 0 AND plan_id>0 AND userid=' . $vip_id . ')');
            while ($plans_row = $query->fetch()) {
                $arr_plans[$plans_row['id']] = $plans_row;
            }
            $arr_cbdmda = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_proposal WHERE id IN (SELECT cbdmda_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id WHERE send_status = 0 AND cbdmda_id>0 AND userid=' . $vip_id . ')');
            while ($cbdmda_row = $query->fetch()) {
                $arr_cbdmda[$cbdmda_row['id']] = $cbdmda_row;
            }
            $arr_kqlcndt = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result_project WHERE id IN (SELECT result_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id WHERE send_status = 0 AND result_id>0 AND userid=' . $vip_id . ')');
            while ($kqlcndt_row = $query->fetch()) {
                $arr_kqlcndt[$kqlcndt_row['id']] = $kqlcndt_row;
            }
            $arr_tbmst = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_investor_prequalification WHERE id IN (SELECT pq_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id WHERE send_status = 0 AND pq_id>0 AND userid=' . $vip_id . ')');
            while ($tbmst_row = $query->fetch()) {
                $arr_tbmst[$tbmst_row['id']] = $tbmst_row;
            }
            $arr_kqst = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqst_project WHERE id IN (SELECT kqst_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id WHERE send_status = 0 AND kqst_id>0 AND userid=' . $vip_id . ')');
            while ($kqst_row = $query->fetch()) {
                $arr_kqst[$kqst_row['id']] = $kqst_row;
            }

            // Gộp nhóm những mail chung chủ đề lại theo từng VIP
            $array_bid_id = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id WHERE send_status = 0 AND userid=' . $vip_id . ' LIMIT 50');
            while ($data = $query->fetch()) {
                $array_bid_id[$data['id']] = $data['id'];
                if ($data['filter_id'] > 0) {
                    if ($data['bid_id'] > 0) {
                        $vip['filter'][$data['filter_id']]['list_bid'][] = $arr_bid[$data['bid_id']];
                    }
                    if ($data['plan_id'] > 0) {
                        $vip['filter'][$data['filter_id']]['list_plans'][] = $arr_plans[$data['plan_id']];
                    }
                    if ($data['result_id'] > 0) {
                        $vip['filter'][$data['filter_id']]['list_result'][] = $arr_kqlcndt[$data['result_id']];
                    }
                    if ($data['cbdmda_id'] > 0) {
                        $vip['filter'][$data['filter_id']]['list_cbdmda'][] = $arr_cbdmda[$data['cbdmda_id']];
                    }
                    if ($data['pq_id'] > 0) {
                        $vip['filter'][$data['filter_id']]['list_tbmst'][] = $arr_tbmst[$data['pq_id']];
                    }
                    if ($data['kqst_id'] > 0) {
                        $vip['filter'][$data['filter_id']]['list_kqst'][] = $arr_kqst[$data['kqst_id']];
                    }
                    // $vip['filter'][$data['filter_id']]['list_hd'][] = $arr_bid[$data['hd_id']];
                    $vip['bid'][] = $data['bid_id'];
                    $vip['plans'][] = $data['plan_id'];
                    $vip['result'][] = $data['result_id'];
                    $vip['cbdmda'][] = $data['cbdmda_id'];
                    $vip['tbmst'][] = $data['pq_id'];
                    $vip['kqst'][] = $data['kqst_id'];
                    $vip['hopdong'][] = $data['hd_id'];
                }
            }

            // Cập nhật lại lần cuối cùng gửi mail cho các tài khoản VIP, Phần này cần chạy trước beg-----inTransaction để nếu có lỗi thì bỏ qua 1 tài khảo.
            if (sizeof($array_bid_id) < 50) {
                // Nếu 1 lần tổng hợp có > 50 thì không cập nhật ngay last_email để 1 phút sau sẽ chạy tiếp
                $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET last_email = ' . NV_CURRENTTIME . ' WHERE id =' . $vip['cusid'] . ' AND vip = 1');
            } else {
                file_put_contents($create_mail_file, "Con chay nua: " . sizeof($array_bid_id) . " / " . sizeof($arr_bid) . "\n", FILE_APPEND);
            }
            $db->beginTransaction();
            try {
                $data_insert = array();
                $data_insert['send_time'] = 0;
                $data_insert['status'] = 0;

                // Gửi mail thông báo mới thầu, kế hoạch chọn nhà thầu, thông báo sơ tuyển nhóm theo từng bộ lọc
                if (!empty($vip['filter'])) {
                    foreach ($vip['filter'] as $filter_id => $filter) {
                        if (!empty($filter['list_bid'])) {
                            $data_insert['title'] = sprintf($lang_module['mail_title_ndt_tbmt'], date('d/m/Y'));
                            $data_insert['content'] = nv_theme_bidding_mail($filter_id, $filter['list_bid'], $vip_id);
                        } else if (!empty($filter['list_plans'])) {
                            $data_insert['title'] = sprintf($lang_module['mail_title_ndt_khlcnt'], date('d/m/Y'));
                            $data_insert['content'] = nv_theme_mail_plans($filter_id, $filter['list_plans'], $vip_id);
                        } else if (!empty($filter['list_result'])) {
                            $data_insert['title'] = sprintf($lang_module['mail_title_ndt_kqlcnt'], date('d/m/Y'));
                            $data_insert['content'] = nv_theme_mail_result($filter_id, $filter['list_result'], $vip_id);
                        } else if (!empty($filter['list_cbdmda'])) {
                            $data_insert['title'] = sprintf($lang_module['mail_title_ndt_dmda'], date('d/m/Y'));
                            $data_insert['content'] = nv_theme_mail_cbdmda($filter_id, $filter['list_cbdmda'], $vip_id);
                        } else if (!empty($filter['list_tbmst'])) {
                            $data_insert['title'] = sprintf($lang_module['mail_title_ndt_tbmst'], date('d/m/Y'));
                            $data_insert['content'] = nv_theme_mail_tbmst($filter_id, $filter['list_tbmst'], $vip_id);
                        } else if (!empty($filter['list_kqst'])) {
                            $data_insert['title'] = sprintf($lang_module['mail_title_ndt_kqst'], date('d/m/Y'));
                            $data_insert['content'] = nv_theme_mail_kqst($filter_id, $filter['list_kqst'], $vip_id);
                        }

                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 4;
                        $data_insert['addtime'] = NV_CURRENTTIME;
                        $data_insert['content'] .= sprintf($lang_module['mail_footer_tbmt'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date('d/m/Y', $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');
                        // Nội dung htm sẽ gửi cho từng khách
                        try {
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
                            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();
                            $_mailid = $db->lastInsertId();
                        } catch (PDOException $e) {
                            print_r('Lỗi thêm mail tin mới vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "project_mail.php ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo ($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }
                }
                echo "vip_id = " . $vip_id . "\n";
                // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                if (!empty($array_bid_id)) {
                    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_bid_id) . ') AND send_status = 0');
                }
                $db->commit();
                file_put_contents($create_mail_file, "number bid = " . sizeof($array_bid_id) . ": " . implode(',', $array_bid_id) . "\n", FILE_APPEND);
                file_put_contents($create_mail_file, "END: " . $vip_id . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
            } catch (PDOException $e) {
                file_put_contents($create_mail_file, 'rollBack: ' . $vip_id . "\n\n", FILE_APPEND);
                $db->rollBack();
                echo '<pre>';
                print_r($e);
                echo '</pre>';
            }
        }
    }

    unlink(NV_ROOTDIR . '/data/create_mail_project_' . $prefix_lang . '.txt');
} catch (PDOException $e) {
    file_put_contents($create_mail_file, 'ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "project_mail.php ERROR " . print_r($e, true) . "\n\n");
}
echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function nv_theme_bidding_mail($filter_id, $array_data, $vip_id)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func;

    if (!empty($array_data)) {
        $xtpl = new XTemplate('template_email_project.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        // Lấy tên bộ lọc
        $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();
        // link sửa bộ lọc
        $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
        $from = $filter_info['time_find'] > 0 ? nv_date('d/m/Y', NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date('d/m/Y', NV_CURRENTTIME - 86400 * 14);
        $filter_info['link_search'] = '';
        $filter_info['link_search_detail'] = NV_MY_DOMAIN . '/' . $site_lang . '/detail?';
        if ($filter_info['key_search'] != '') {
            $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
        }
        if ($filter_info['key_search2'] != '') {
            $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
        }
        $filter_info['link_search'] .= '&sfrom=' . $from;
        $filter_info['link_search'] .= '&sto=' . nv_date('d/m/Y', NV_CURRENTTIME);
        $filter_info['link_search'] .= '&is_advance=1';
        $filter_info['link_search'] .= '&userid=' . $vip_id;
        $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
        $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];
        $filter_info['link_search'] .= '&search_one_key=' . $filter_info['search_one_key'];

        if ($filter_info['cat'] > 0) {
            $filter_info['link_search'] .= '&cat=' . $filter_info['cat'];
        }
        if ($filter_info['field'] != '') {
            $array_field = explode(',', $filter_info['field']);
            foreach ($array_field as $field) {
                $filter_info['link_search'] .= '&field[]=' . $field;
            }
        }
        if ($filter_info['without_key'] != '') {
            $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
        }
        if ($filter_info['type_org'] > 0) {
            $filter_info['link_search'] .= '&type_org=' . $filter_info['type_org'];
        }
        if ($filter_info['money_from'] > 0) {
            $filter_info['link_search'] .= '&money_from=' . number_format($filter_info['money_from'], 0, '.', ',');
        }
        if ($filter_info['money_to'] > 0) {
            $filter_info['link_search'] .= '&money_to=' . number_format($filter_info['money_to'], 0, '.', ',');
        }
        if ($filter_info['price_from'] > 0) {
            $filter_info['link_search'] .= '&price_from=' . number_format($filter_info['price_from'], 0, '.', ',');
        }
        if ($filter_info['price_to'] > 0) {
            $filter_info['link_search'] .= '&price_to=' . number_format($filter_info['price_to'], 0, '.', ',');
        }
        if ($filter_info['vip_use'] == 4) {
            if ($filter_info['vip_use2'] == 2) {
                $filter_info['link_search_detail'] .= '&type_search=2&type_info2=2';
            }
        }
        $filter_info['link_search_detail'] = $filter_info['link_search_detail'] . $filter_info['link_search'];
        $xtpl->assign('LINK_FILTER', $filter_info['link_search_detail']);
        // ghi log bộ lọc
        $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);
        $xtpl->assign('FILTER_NAME', $filter_info['title']);

        if (sizeof($array_data) > 50) {
            $xtpl->assign('FILTER_NUMBER', sprintf($lang_module['limit_filter_bid'], number_format(sizeof($array_data))));
            $xtpl->parse('main.filter.number');
        }

        file_put_contents($create_mail_file, 'list_bid: ' . sizeof($array_data) . "\n", FILE_APPEND);
        $i_break = 0;
        foreach ($array_data as $_data) {
            ++$i_break;
            if ($i_break > 50) {
                break;
            }
            $_data['title'] = $_data['goi_thau'];
            $_data['title_a'] = nv_htmlspecialchars($_data['goi_thau']);

            // Bôi vàng những từ trùng với từ khóa tìm kiếm, tìm kiếm tuyệt đối
            $arr_key = explode(',', $filter_info['key_search']);

            foreach ($arr_key as $key) {
                $key = trim($key);
                $_data['goi_thau'] = BoldKeywordInStr(strip_tags($_data['goi_thau']), $key);
                $_data['ben_moi_thau'] = strip_tags(BoldKeywordInStr($_data['ben_moi_thau'], $key));
            }
            $_data['ngay_dang_tai'] = nv_date('H:i d/m/y', $_data['ngay_dang_tai']);
            $tbmt_t = getUrlByLanguage('tbmt');
            if ($site_lang != 'vi') {
                $_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . change_alias($_data['title_a']) . '-' . $_data['id'] . '.html';
            } else {
                $_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . change_alias($_data['title_a']) . '-' . $_data['id'] . '.html';
            }

            $xtpl->assign('DATA', $_data);

            $xtpl->parse('main.filter.content');
        }
        $xtpl->parse('main.filter');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_mail_plans($filter_id, $array_data, $vip_id)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func;

    if (!empty($array_data)) {
        $xtpl = new XTemplate('template_email_project.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);

        // Lấy tên bộ lọc
        $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();
        // link sửa bộ lọc
        $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
        $from = $filter_info['time_find'] > 0 ? nv_date('d/m/Y', NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date('d/m/Y', NV_CURRENTTIME - 86400 * 14);
        $filter_info['link_search'] = '';
        $filter_info['link_search_plans'] = NV_MY_DOMAIN . '/' . $site_lang . '/listplan?type_info=2';
        if ($filter_info['key_search'] != '') {
            $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
        }
        if ($filter_info['key_search2'] != '') {
            $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
        }
        $filter_info['link_search'] .= '&sfrom=' . $from;
        $filter_info['link_search'] .= '&sto=' . nv_date('d/m/Y', NV_CURRENTTIME);
        $filter_info['link_search'] .= '&is_advance=1';
        $filter_info['link_search'] .= '&userid=' . $vip_id;
        $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
        $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];
        $filter_info['link_search'] .= '&search_one_key=' . $filter_info['search_one_key'];

        if ($filter_info['cat'] > 0) {
            $filter_info['link_search'] .= '&cat=' . $filter_info['cat'];
        }
        if ($filter_info['field'] != '') {
            $array_field = explode(',', $filter_info['field']);
            foreach ($array_field as $field) {
                $filter_info['link_search'] .= '&field[]=' . $field;
            }
        }
        if ($filter_info['without_key'] != '') {
            $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
        }
        if ($filter_info['type_org'] > 0) {
            $filter_info['link_search'] .= '&type_org=' . $filter_info['type_org'];
        }

        $filter_info['link_search_plans'] .= $filter_info['link_search'];
        $filter_info['link_search_plan'] = '';
        if ($filter_info['invest_from'] > 0) {
            $filter_info['link_search_plan'] .= '&invest_from=' . number_format($filter_info['invest_from'], 0, '.', ',');
        }
        if ($filter_info['invest_to'] > 0) {
            $filter_info['link_search_plan'] .= '&invest_to=' . number_format($filter_info['invest_to'], 0, '.', ',');
        }
        if ($filter_info['price_plan_from'] > 0) {
            $filter_info['link_search_plan'] .= '&price_plan_from=' . number_format($filter_info['price_plan_from'], 0, '.', ',');
        }
        if ($filter_info['price_plan_to'] > 0) {
            $filter_info['link_search_plan'] .= '&price_plan_to=' . number_format($filter_info['price_plan_to'], 0, '.', ',');
        }
        if ($filter_info['vip_use'] == 4) {
            if ($filter_info['vip_use2'] == 4) {
                $filter_info['link_search_plans'] .= '&type_search=2&type_info2=4';
            }
        }
        $filter_info['link_search_plans'] = $filter_info['link_search_plans'] . $filter_info['link_search_plan'];
        $xtpl->assign('LINK_FILTER', $filter_info['link_search_plans']);

        // ghi log bộ lọc
        $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);
        $xtpl->assign('FILTER_NAME', $filter_info['title']);

        file_put_contents($create_mail_file, 'list_plan: ' . sizeof($array_data) . "\n", FILE_APPEND);
        $i_break = 0;
        foreach ($array_data as $_data) {
            ++$i_break;
            if ($i_break > 100) {
                break;
            }
            $_data['title_a'] = nv_htmlspecialchars($_data['title']);
            // Bôi vàng những từ trùng với từ khóa tìm kiếm
            if ($filter_info['search_type'] == 1) {
                // Nếu tìm kiếm tuyệt đối
                $arr_key = explode(',', $filter_info['key_search']);
            } else {
                // Nếu tìm kiếm tương đối
                $filter_info['key_search'] = str_replace(',', ' ', $filter_info['key_search']);
                $arr_key = explode(' ', trim($filter_info['key_search']));
                $arr_key = array_unique($arr_key);
            }

            foreach ($arr_key as $key) {
                $key = trim($key);
                $_data['title'] = BoldKeywordInStr(strip_tags($_data['title']), $key);
                $_data['investor'] = strip_tags(BoldKeywordInStr($_data['investor'], $key));
            }
            $_data['addtime'] = $_data['addtime'] > 0 ? nv_date('H:i d/m/Y', $_data['addtime']) : '';
            $khlcnt_t = getUrlByLanguage('khlcnt');
            if ($site_lang != 'vi') {
                $_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['plans'] . '/' . $khlcnt_t . '/' . change_alias($_data['title_a']) . '-' . $_data['id'] . '.html';
            } else {
                $_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['plans'] . '/' . $khlcnt_t . '/' . change_alias($_data['title_a']) . '-' . $_data['id'] . '.html';
            }

            $xtpl->assign('DATA', $_data);
            $xtpl->parse('main.filter.content_plan');
        }
        $xtpl->parse('main.filter');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_mail_result($filter_id, $array_data, $vip_id)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func;

    if (!empty($array_data)) {
        $xtpl = new XTemplate('template_email_project.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        // Lấy tên bộ lọc
        $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();
        // link sửa bộ lọc
        $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
        $from = $filter_info['time_find'] > 0 ? nv_date('d/m/Y', NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date('d/m/Y', NV_CURRENTTIME - 86400 * 14);
        $filter_info['link_search'] = '';
        $filter_info['link_search_project_result'] = NV_MY_DOMAIN . '/' . $site_lang . '/listresult?type_info2=5&type_search=2';
        if ($filter_info['key_search'] != '') {
            $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
        }
        if ($filter_info['key_search2'] != '') {
            $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
        }
        $filter_info['link_search'] .= '&sfrom=' . $from;
        $filter_info['link_search'] .= '&sto=' . nv_date('d/m/Y', NV_CURRENTTIME);
        $filter_info['link_search'] .= '&is_advance=1';
        $filter_info['link_search'] .= '&userid=' . $vip_id;
        $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
        $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];
        $filter_info['link_search'] .= '&search_one_key=' . $filter_info['search_one_key'];

        if ($filter_info['cat'] > 0) {
            $filter_info['link_search'] .= '&cat=' . $filter_info['cat'];
        }
        if ($filter_info['field'] != '') {
            $array_field = explode(',', $filter_info['field']);
            foreach ($array_field as $field) {
                $filter_info['link_search'] .= '&field[]=' . $field;
            }
        }
        if ($filter_info['without_key'] != '') {
            $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
        }
        if ($filter_info['type_org'] > 0) {
            $filter_info['link_search'] .= '&type_org=' . $filter_info['type_org'];
        }
        if ($filter_info['money_from'] > 0) {
            $filter_info['link_search'] .= '&money_from=' . number_format($filter_info['money_from'], 0, '.', ',');
        }
        if ($filter_info['money_to'] > 0) {
            $filter_info['link_search'] .= '&money_to=' . number_format($filter_info['money_to'], 0, '.', ',');
        }
        if ($filter_info['price_from'] > 0) {
            $filter_info['link_search'] .= '&price_from=' . number_format($filter_info['price_from'], 0, '.', ',');
        }
        if ($filter_info['price_to'] > 0) {
            $filter_info['link_search'] .= '&price_to=' . number_format($filter_info['price_to'], 0, '.', ',');
        }

        $filter_info['link_search_project_result'] = $filter_info['link_search_project_result'] . $filter_info['link_search'];
        $xtpl->assign('LINK_FILTER', $filter_info['link_search_project_result']);

        // ghi log bộ lọc
        $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);
        $xtpl->assign('FILTER_NAME', $filter_info['title']);

        if (sizeof($array_data) > 50) {
            $xtpl->assign('FILTER_NUMBER', sprintf($lang_module['limit_filter_bid'], number_format(sizeof($array_data))));
            $xtpl->parse('main.filter.number');
        }

        file_put_contents($create_mail_file, 'list_result: ' . sizeof($array_data) . "\n", FILE_APPEND);
        $i_break = 0;
        foreach ($array_data as $_data) {
            ++$i_break;
            if ($i_break > 50) {
                break;
            }
            $_data['title_a'] = nv_htmlspecialchars($_data['title']);

            // Bôi vàng những từ trùng với từ khóa tìm kiếm, tìm kiếm tuyệt đối
            $arr_key = explode(',', $filter_info['key_search']);

            foreach ($arr_key as $key) {
                $key = trim($key);
                $_data['title'] = BoldKeywordInStr(strip_tags($_data['title']), $key);
                $_data['ben_moi_thau'] = strip_tags(BoldKeywordInStr($_data['soclictor'], $key));
            }
            $_data['post_time'] = nv_date('H:i d/m/y', $_data['post_time']);
            $luachon_t = getUrlByLanguage('kqlcnt');
            if ($site_lang != 'vi') {
                $_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['result'] . '/' . $luachon_t . '/' . change_alias($_data['title_a']) . '-' . $_data['id'] . '.html';
            } else {
                $_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['result'] . '/' . $luachon_t . '/' . change_alias($_data['title_a']) . '-' . $_data['id'] . '.html';
            }

            $xtpl->assign('DATA', $_data);

            $xtpl->parse('main.filter.result');
        }
        $xtpl->parse('main.filter');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_mail_cbdmda($filter_id, $array_data, $vip_id)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func;

    if (!empty($array_data)) {
        $xtpl = new XTemplate('template_email_project.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        // Lấy tên bộ lọc
        $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();
        // link sửa bộ lọc
        $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
        $from = $filter_info['time_find'] > 0 ? nv_date('d/m/Y', NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date('d/m/Y', NV_CURRENTTIME - 86400 * 14);
        $filter_info['link_search'] = '';
        $filter_info['link_search_project'] = NV_MY_DOMAIN . '/' . $site_lang . '/project?type_info2=1&type_search=2';
        if ($filter_info['key_search'] != '') {
            $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
        }
        if ($filter_info['key_search2'] != '') {
            $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
        }
        $filter_info['link_search'] .= '&sfrom=' . $from;
        $filter_info['link_search'] .= '&sto=' . nv_date('d/m/Y', NV_CURRENTTIME);
        $filter_info['link_search'] .= '&is_advance=1';
        $filter_info['link_search'] .= '&userid=' . $vip_id;
        $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
        $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];
        $filter_info['link_search'] .= '&search_one_key=' . $filter_info['search_one_key'];

        if ($filter_info['cat'] > 0) {
            $filter_info['link_search'] .= '&cat=' . $filter_info['cat'];
        }
        if ($filter_info['field'] != '') {
            $array_field = explode(',', $filter_info['field']);
            foreach ($array_field as $field) {
                $filter_info['link_search'] .= '&field[]=' . $field;
            }
        }
        if ($filter_info['without_key'] != '') {
            $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
        }
        if ($filter_info['type_org'] > 0) {
            $filter_info['link_search'] .= '&type_org=' . $filter_info['type_org'];
        }
        if ($filter_info['money_from'] > 0) {
            $filter_info['link_search'] .= '&money_from=' . number_format($filter_info['money_from'], 0, '.', ',');
        }
        if ($filter_info['money_to'] > 0) {
            $filter_info['link_search'] .= '&money_to=' . number_format($filter_info['money_to'], 0, '.', ',');
        }
        if ($filter_info['price_from'] > 0) {
            $filter_info['link_search'] .= '&price_from=' . number_format($filter_info['price_from'], 0, '.', ',');
        }
        if ($filter_info['price_to'] > 0) {
            $filter_info['link_search'] .= '&price_to=' . number_format($filter_info['price_to'], 0, '.', ',');
        }

        $filter_info['link_search_project'] = $filter_info['link_search_project'] . $filter_info['link_search'];
        $xtpl->assign('LINK_FILTER', $filter_info['link_search_project']);

        // ghi log bộ lọc
        $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);
        $xtpl->assign('FILTER_NAME', $filter_info['title']);

        if (sizeof($array_data) > 50) {
            $xtpl->assign('FILTER_NUMBER', sprintf($lang_module['limit_filter_bid'], number_format(sizeof($array_data))));
            $xtpl->parse('main.filter.number');
        }

        file_put_contents($create_mail_file, 'list_project: ' . sizeof($array_data) . "\n", FILE_APPEND);
        $i_break = 0;
        foreach ($array_data as $_data) {
            ++$i_break;
            if ($i_break > 50) {
                break;
            }
            $_data['title_a'] = nv_htmlspecialchars($_data['ten_du_an']);

            // Bôi vàng những từ trùng với từ khóa tìm kiếm, tìm kiếm tuyệt đối
            $arr_key = explode(',', $filter_info['key_search']);

            foreach ($arr_key as $key) {
                $key = trim($key);
                $_data['title'] = BoldKeywordInStr(strip_tags($_data['ten_du_an']), $key);
                $_data['ben_moi_thau'] = strip_tags(BoldKeywordInStr($_data['don_vi'], $key));
            }
            $_data['time_post'] = nv_date('H:i d/m/y', $_data['time_post']);
            if ($site_lang != 'vi') {
                $_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['projectdetail'] . '/' . change_alias($_data['title_a']) . '-' . $_data['code'] . '.html';
            } else {
                $_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['projectdetail'] . '/' . change_alias($_data['title_a']) . '-' . $_data['code'] . '.html';
            }

            $xtpl->assign('DATA', $_data);

            $xtpl->parse('main.filter.project');
        }
        $xtpl->parse('main.filter');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_mail_tbmst($filter_id, $array_data, $vip_id)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func;

    if (!empty($array_data)) {
        $xtpl = new XTemplate('template_email_project.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        // Lấy tên bộ lọc
        $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();
        // link sửa bộ lọc
        $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
        $from = $filter_info['time_find'] > 0 ? nv_date('d/m/Y', NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date('d/m/Y', NV_CURRENTTIME - 86400 * 14);
        $filter_info['link_search'] = '';
        $filter_info['link_search_project_tbmst'] = NV_MY_DOMAIN . '/' . $site_lang . '/listprequalification?type_info2=3&type_search=2';
        if ($filter_info['key_search'] != '') {
            $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
        }
        if ($filter_info['key_search2'] != '') {
            $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
        }
        $filter_info['link_search'] .= '&sfrom=' . $from;
        $filter_info['link_search'] .= '&sto=' . nv_date('d/m/Y', NV_CURRENTTIME);
        $filter_info['link_search'] .= '&is_advance=1';
        $filter_info['link_search'] .= '&userid=' . $vip_id;
        $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
        $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];
        $filter_info['link_search'] .= '&search_one_key=' . $filter_info['search_one_key'];

        if ($filter_info['cat'] > 0) {
            $filter_info['link_search'] .= '&cat=' . $filter_info['cat'];
        }
        if ($filter_info['field'] != '') {
            $array_field = explode(',', $filter_info['field']);
            foreach ($array_field as $field) {
                $filter_info['link_search'] .= '&field[]=' . $field;
            }
        }
        if ($filter_info['without_key'] != '') {
            $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
        }
        if ($filter_info['type_org'] > 0) {
            $filter_info['link_search'] .= '&type_org=' . $filter_info['type_org'];
        }
        if ($filter_info['money_from'] > 0) {
            $filter_info['link_search'] .= '&money_from=' . number_format($filter_info['money_from'], 0, '.', ',');
        }
        if ($filter_info['money_to'] > 0) {
            $filter_info['link_search'] .= '&money_to=' . number_format($filter_info['money_to'], 0, '.', ',');
        }
        if ($filter_info['price_from'] > 0) {
            $filter_info['link_search'] .= '&price_from=' . number_format($filter_info['price_from'], 0, '.', ',');
        }
        if ($filter_info['price_to'] > 0) {
            $filter_info['link_search'] .= '&price_to=' . number_format($filter_info['price_to'], 0, '.', ',');
        }

        $filter_info['link_search_project_tbmst'] = $filter_info['link_search_project_tbmst'] . $filter_info['link_search'];
        $xtpl->assign('LINK_FILTER', $filter_info['link_search_project_tbmst']);

        // ghi log bộ lọc
        $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);
        $xtpl->assign('FILTER_NAME', $filter_info['title']);

        if (sizeof($array_data) > 50) {
            $xtpl->assign('FILTER_NUMBER', sprintf($lang_module['limit_filter_bid'], number_format(sizeof($array_data))));
            $xtpl->parse('main.filter.number');
        }

        file_put_contents($create_mail_file, 'list_tbmst: ' . sizeof($array_data) . "\n", FILE_APPEND);
        $i_break = 0;
        foreach ($array_data as $_data) {
            ++$i_break;
            if ($i_break > 50) {
                break;
            }
            $_data['title_a'] = nv_htmlspecialchars($_data['ten_du_an']);

            // Bôi vàng những từ trùng với từ khóa tìm kiếm, tìm kiếm tuyệt đối
            $arr_key = explode(',', $filter_info['key_search']);

            foreach ($arr_key as $key) {
                $key = trim($key);
                $_data['title'] = BoldKeywordInStr(strip_tags($_data['ten_du_an']), $key);
                $_data['ben_moi_thau'] = strip_tags(BoldKeywordInStr($_data['ben_moi_thau'], $key));
            }
            $_data['ngay_dang_tai'] = nv_date('H:i d/m/y', $_data['ngay_dang_tai']);
            $nt_t = getUrlByLanguage('nt');
            if ($site_lang != 'vi') {
                $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['prequalification'] . '/' . $nt_t . '/' . change_alias($_data['title_a']) . '-' . $_data['id'] . '.html';
            } else {
                $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['prequalification'] . '/' . $nt_t . '/' . change_alias($_data['title_a']) . '-' . $_data['id'] . '.html';
            }

            $xtpl->assign('DATA', $_data);

            $xtpl->parse('main.filter.content_pq');
        }
        $xtpl->parse('main.filter');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_mail_kqst($filter_id, $array_data, $vip_id)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func;

    if (!empty($array_data)) {
        $xtpl = new XTemplate('template_email_project.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        // Lấy tên bộ lọc
        $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();
        // link sửa bộ lọc
        $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
        $from = $filter_info['time_find'] > 0 ? nv_date('d/m/Y', NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date('d/m/Y', NV_CURRENTTIME - 86400 * 14);
        $filter_info['link_search'] = '';
        $filter_info['link_search_project_kqst'] = NV_MY_DOMAIN . '/' . $site_lang . '/listresultpq?type_info2=6&type_search=2';
        if ($filter_info['key_search'] != '') {
            $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
        }
        if ($filter_info['key_search2'] != '') {
            $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
        }
        $filter_info['link_search'] .= '&sfrom=' . $from;
        $filter_info['link_search'] .= '&sto=' . nv_date('d/m/Y', NV_CURRENTTIME);
        $filter_info['link_search'] .= '&is_advance=1';
        $filter_info['link_search'] .= '&userid=' . $vip_id;
        $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
        $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];
        $filter_info['link_search'] .= '&search_one_key=' . $filter_info['search_one_key'];

        if ($filter_info['cat'] > 0) {
            $filter_info['link_search'] .= '&cat=' . $filter_info['cat'];
        }
        if ($filter_info['field'] != '') {
            $array_field = explode(',', $filter_info['field']);
            foreach ($array_field as $field) {
                $filter_info['link_search'] .= '&field[]=' . $field;
            }
        }
        if ($filter_info['without_key'] != '') {
            $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
        }
        if ($filter_info['type_org'] > 0) {
            $filter_info['link_search'] .= '&type_org=' . $filter_info['type_org'];
        }
        if ($filter_info['money_from'] > 0) {
            $filter_info['link_search'] .= '&money_from=' . number_format($filter_info['money_from'], 0, '.', ',');
        }
        if ($filter_info['money_to'] > 0) {
            $filter_info['link_search'] .= '&money_to=' . number_format($filter_info['money_to'], 0, '.', ',');
        }
        if ($filter_info['price_from'] > 0) {
            $filter_info['link_search'] .= '&price_from=' . number_format($filter_info['price_from'], 0, '.', ',');
        }
        if ($filter_info['price_to'] > 0) {
            $filter_info['link_search'] .= '&price_to=' . number_format($filter_info['price_to'], 0, '.', ',');
        }

        $filter_info['link_search_project_kqst'] = $filter_info['link_search_project_kqst'] . $filter_info['link_search'];
        $xtpl->assign('LINK_FILTER', $filter_info['link_search_project_kqst']);

        // ghi log bộ lọc
        $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);
        $xtpl->assign('FILTER_NAME', $filter_info['title']);

        if (sizeof($array_data) > 50) {
            $xtpl->assign('FILTER_NUMBER', sprintf($lang_module['limit_filter_bid'], number_format(sizeof($array_data))));
            $xtpl->parse('main.filter.number');
        }

        file_put_contents($create_mail_file, 'list_kqst: ' . sizeof($array_data) . "\n", FILE_APPEND);
        $i_break = 0;
        foreach ($array_data as $_data) {
            ++$i_break;
            if ($i_break > 50) {
                break;
            }
            $_data['title_a'] = nv_htmlspecialchars($_data['title']);

            // Bôi vàng những từ trùng với từ khóa tìm kiếm, tìm kiếm tuyệt đối
            $arr_key = explode(',', $filter_info['key_search']);

            foreach ($arr_key as $key) {
                $key = trim($key);
                $_data['title'] = BoldKeywordInStr(strip_tags($_data['title']), $key);
                $_data['title_solicitor'] = strip_tags(BoldKeywordInStr($_data['title_solicitor'], $key));
            }
            $_data['post_time'] = nv_date('H:i d/m/y', $_data['post_time']);
            $_data['date_results'] = nv_date('H:i d/m/y', $_data['date_results']);
            $_data['reception_date'] = nv_date('H:i d/m/y', $_data['reception_date']);
            $nt_t = getUrlByLanguage('nt');
            if ($site_lang != 'vi') {
                $_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['resultpq'] . '/' . $nt_t . '/' . change_alias($_data['title_a']) . '-' . $_data['id'] . '.html';
            } else {
                $_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['resultpq'] . '/' . $nt_t . '/' . change_alias($_data['title_a']) . '-' . $_data['id'] . '.html';
            }

            $xtpl->assign('DATA', $_data);

            $xtpl->parse('main.filter.kqst');
        }
        $xtpl->parse('main.filter');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}
