<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$hosts = array(
    $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
);

$client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
    ->setHosts($hosts)
    ->setRetries(0)
    ->build();

$file_name = NV_ROOTDIR . '/tools/update_province_plans.txt';
if (file_exists($file_name)) {
    $id = file_get_contents($file_name);
    $id = intval($id);
} else {
    $id = 0;
}

/**
 * Tool xử lý update lại dữ liệu các tỉnh thành từ bảng plan_contract sang bảng plan
*/
try {
    // Lấy danh sách nhà thầu phân lô trúng thầu
    $sqlPL = $db->query("SELECT id FROM nv4_vi_bidding_plans WHERE id > " . $id . " ORDER BY id LIMIT 100");
    while ($row = $sqlPL->fetch()) {
        $arr[] = $row['id'];
        $newid = $row['id'];
    }
    $sqlPL->closeCursor();

    $i = 0;
    if (!empty($arr)) {
        // Lấy danh sách nhà thầu phân lô trúng thầu
        $sqlPL = $db->query("SELECT id, id_plan, province_id FROM nv4_vi_bidding_plans_contract WHERE id_plan IN (" . implode(',', $arr) . ") and province_id > 0");
        while ($row = $sqlPL->fetch()) {
            $arrPlanContract[$row['id_plan']][$row['province_id']] = $row['province_id'];
        }
        $sqlPL->closeCursor();

        if (!empty($arrPlanContract)) {
            $sqlPlan = [];
            foreach ($arrPlanContract as $k => $v) {
                $strProvince = implode(',', $v);

                $sqlPlan[] = "UPDATE nv4_vi_bidding_plans SET list_id_province = " . $db->quote($strProvince) . " WHERE id = " . $k;
                $sqlPlan[] = "UPDATE nv4_en_bidding_plans SET list_id_province = " . $db->quote($strProvince) . " WHERE id = " . $k;

                $params['body'][] = [
                    'update' => [
                        '_index' => NV_LANG_ELASTIC . 'dauthau_plans',
                        '_id' => $k
                    ]
                ];

                $params['body'][] = [
                    'doc' => [
                        'list_id_province' => implode(', ', $v)
                    ]
                ];
                $i++;
                echo "\n>>ID_Plan: " . $k;
            }

            if (!empty($params['body'])) {
                $db->exec(implode(';', $sqlPlan));
                echo ">>\033[92mĐã cập nhật lại tỉnh thành bên MYSQL size: " . (sizeof($sqlPlan)/2) .  "\033[0m";

                // Thực hiện cập nhật
                $response = $client->bulk($params);
                echo "\n>>\033[92mĐã cập nhập tỉnh thành vào trong bảng plans trên ES thành công, size: " . (sizeof($params['body'])/2) .  "\033[0m";
            }
        }
    }

    echo ("\nnewid:" . $newid);
    echo ("\nid: " . $id);
    if ($newid > $id) {
        file_put_contents($file_name, $newid);
    } else {
        exit(1);
    }

    echo "\nThoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
    echo "\n> Đã chạy xong: " . $i;
    echo "\n-------------------------------------------\n";
} catch (Exception $e) {
    echo ("\n>>> Lỗi rồi");
    pr($e);
}
