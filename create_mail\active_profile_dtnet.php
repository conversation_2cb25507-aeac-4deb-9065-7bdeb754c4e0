<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 * Gửi mail M<PERSON><PERSON> xác thực quyền quản lý hồ sơ doanh nghiệp trên <PERSON>.Net, chạy 1 ngày 1 lần
 * L<PERSON><PERSON> thông tin vào bảng nv4_vi_active_profile_dtnet_mkt để marketing xử lý
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);
// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';

$log_file = NV_ROOTDIR . '/data/active_profile_dtnet_' . date('Ymd') . '.log';
if (file_exists(NV_ROOTDIR . '/data/active_profile_dtnet.log')) {
    file_put_contents($log_file, "=================================Chay gui mail moi xác thuc quyen quan ly ho so doanh nghiep dauthau.net luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    exit();
}

if (file_exists(NV_ROOTDIR . '/data/active_profile_dtnet.log')) {
    file_put_contents(NV_ROOTDIR . '/data/active_profile_dtnet.log', NV_CURRENTTIME);
}

// Dọn dẹp dữ liệu cũ trong bảng active_profile_dtnet_mkt (lưu trữ 30 ngày)
$clean_time = NV_CURRENTTIME - (30 * 86400); // 30 ngày

// Kiểm tra và cập nhật trạng thái các bản ghi cũ trước khi xóa
$count_old_data = $db->query("SELECT COUNT(*) FROM " . $config['prefix'] . "_vi_active_profile_dtnet_mkt WHERE add_time < " . $clean_time)->fetchColumn();
if ($count_old_data > 0) {
    $sql_check = "SELECT contact_id, verified FROM " . $config['prefix'] . "_vi_active_profile_dtnet_mkt WHERE add_time < " . $clean_time;
    $query_check = $db->query($sql_check);
    while ($row_check = $query_check->fetch()) {
        if ($row_check['verified'] == 1) {
            $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -20 WHERE business_id IN (SELECT id FROM " . BUSINESS_PREFIX_GLOBAL . "_info WHERE code = " . $db->quote($row_check['contact_id']) . ")");
        }
    }

    // Xóa dữ liệu cũ
    $db->query("DELETE FROM " . $config['prefix'] . "_vi_active_profile_dtnet_mkt WHERE add_time < " . $clean_time);
    echo "- Đã dọn dẹp " . number_format($count_old_data) . " bản ghi cũ trong bảng active_profile_dtnet_mkt (dữ liệu cũ hơn 30 ngày)\n";
    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Đã dọn dẹp " . number_format($count_old_data) . " bản ghi cũ trong bảng active_profile_dtnet_mkt (dữ liệu cũ hơn 30 ngày)\n", FILE_APPEND);
}

/* kiểm tra trong đợt gửi mail này gửi bao nhiêu lần cho 1 nhà thầu, nếu nhiều hơn 1 lần thì đánh dấu các row đó lại = -2 */
$check_turns_send_mail_active = $db->query("SELECT business_id, count(id) as num FROM " . $config['prefix'] . "_vi_active_dtnet_mail WHERE bidding_mail_id = 0  GROUP BY business_id HAVING num > 1 ORDER BY business_id ASC");
while ($result_turn = $check_turns_send_mail_active->fetch()) {
    $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -2 WHERE business_id = " . $result_turn['business_id'] . " AND bidding_mail_id = 0");
    echo '- Đánh dấu nhà thầu bị trùng nhiều lần gửi mail để xử lý sau: business_id: ' . $result_turn['business_id'] . ', số lần trùng: ' . $result_turn['num'] . "\n";
}
$check_turns_send_mail_active->closeCursor();

$last_id_log_file = NV_ROOTDIR . '/data/last_mail_active_dtnet_id.log';

$last_mail_active_dtnet_id = 0;
if (file_exists($last_id_log_file)) {
    $last_mail_active_dtnet_id = (int)file_get_contents($last_id_log_file);
}
$limit_mail = 20;
$num_row = 0;

try {
    //code test
    $query_mail_active_dtnet = $db->query("SELECT * FROM " . $config['prefix'] . "_vi_active_dtnet_mail WHERE id > " . $last_mail_active_dtnet_id . " AND bidding_mail_id = 0 ORDER BY id ASC LIMIT " . $limit_mail);

    while ($row_mail_active_dtnet = $query_mail_active_dtnet->fetch()) {
        $num_row++;
        $last_mail_active_dtnet_id = $row_mail_active_dtnet['id'];
        file_put_contents($last_id_log_file, $last_mail_active_dtnet_id);
        $send_mail = true;
        echo "\n" . "------ Bắt đầu kiểm tra mail Mời xác thực quyền quản lý hồ sơ doanh nghiệp trên DauThau.Net, id: " . $row_mail_active_dtnet['id'] . ", business_id: " . $row_mail_active_dtnet['business_id'] . ' -----' . "\n";
        // Đánh dấu đã chạy
        $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -1 WHERE id = " . $row_mail_active_dtnet['id']);
        // lấy thông tin bussiness
        $business_info = $db->query("SELECT * FROM " . BUSINESS_PREFIX_GLOBAL . "_info as BINFO INNER JOIN " . BUSINESS_PREFIX_GLOBAL . "_addinfo AS BADDINFO ON BINFO.id = BADDINFO.id WHERE BINFO.id = " . $row_mail_active_dtnet['business_id'] . ' LIMIT 1')->fetch();

        if (!empty($business_info)) {
            if (!empty($business_info['tax_nation']) && $business_info['tax_nation'] != 'VN') {
                $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -17 WHERE id = " . $row_mail_active_dtnet['id']);
                echo "- Không xử lý doanh nghiệp nước ngoài (tax_nation: " . $business_info['tax_nation'] . "), active_dtnet_mail id: " . $row_mail_active_dtnet['id'] . ', business_id: ' . $row_mail_active_dtnet['business_id'] . "\n";
                file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Không xử lý doanh nghiệp nước ngoài (tax_nation: " . $business_info['tax_nation'] . "), active_dtnet_mail id: " . $row_mail_active_dtnet['id'] . ', business_id: ' . $row_mail_active_dtnet['business_id'] . "\n", FILE_APPEND);
                continue;
            }

            // kiểm tra invoice_email
            if (empty($business_info['invoice_email'])) {
                // Nếu không có mail nhận hd điện tử -> cập nhật trạng thái = -5 -> sang row tiếp theo
                $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -5 WHERE id = " . $row_mail_active_dtnet['id']);
                echo "- Lỗi không gửi được email do không tìm thấy email nhận hoá đơn điện tử, active_dtnet_mail id: " . $row_mail_active_dtnet['id'] . ', business_id: ' . $row_mail_active_dtnet['business_id'] . "\n";
                continue;
            } else {
                // Kiểm tra xem có email nhận hoá đơn điện tử nào bị trùng trên hệ thống không
                $check_duplicate_invoice_email = $db->query("SELECT count(*) FROM " . BUSINESS_PREFIX_GLOBAL . "_addinfo WHERE invoice_email = " . $db->quote($business_info['invoice_email']))->fetchColumn();
                // nếu có -> cập nhật trạng thái = -6 -> sang row tiếp theo
                if ($check_duplicate_invoice_email > 1) {
                    $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -6 WHERE id = " . $row_mail_active_dtnet['id']);
                    echo "- Lỗi không gửi được email do phát hiện bị trùng thông tin email nhận hoá đơn điện tử với nhà thầu khác trên hệ thống, active_dtnet_mail id: " . $row_mail_active_dtnet['id'] . ', business_id: ' . $row_mail_active_dtnet['business_id'] . ', invoice_email: ' . $business_info['invoice_email'] . "\n";
                    continue;
                }

                // Kiểm tra định dạng email
                $email_validation_regex = "/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z-]+(\.[a-z-]+)*(\.[a-z]{2,3})$/i";
                if (!filter_var($business_info['invoice_email'], FILTER_VALIDATE_EMAIL) || !preg_match($email_validation_regex, $business_info['invoice_email'])) {
                    $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -7 WHERE id = " . $row_mail_active_dtnet['id']);
                    echo "- Lỗi không thể xử lý do email nhận hoá đơn điện tử không đúng định dạng, active_dtnet_mail id: " . $row_mail_active_dtnet['id'] . ', business_id: ' . $row_mail_active_dtnet['business_id'] . ', invoice_email: ' . $business_info['invoice_email'] . "\n";
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Lỗi không thể xử lý do email nhận hoá đơn điện tử không đúng định dạng, active_dtnet_mail id: " . $row_mail_active_dtnet['id'] . ', business_id: ' . $row_mail_active_dtnet['business_id'] . ', invoice_email: ' . $business_info['invoice_email'] . "\n", FILE_APPEND);
                    continue;
                }
            }

            if (!empty($business_info['code'])) {
                $check_exists = $db->query("SELECT * FROM " . $config['prefix'] . "_vi_active_profile_dtnet_mkt WHERE contact_id = " . $db->quote($business_info['code']))->fetch();
                if (!empty($check_exists)) {
                    echo "- Đã tồn tại trong bảng active_profile_dtnet_mkt: " . $business_info['code'] . "\n";
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Đã tồn tại trong bảng active_profile_dtnet_mkt: " . $business_info['code'] . "\n", FILE_APPEND);

                    // Kiểm tra đã xác thực chưa
                    if ($check_exists['verified'] == 1) {
                        $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -20 WHERE id = " . $row_mail_active_dtnet['id']);
                        $db->query("UPDATE " . $config['prefix'] . "_vi_active_profile_dtnet_mkt SET updatetime = " . NV_CURRENTTIME . " WHERE id = " . $check_exists['id']);
                        echo "- Doanh nghiệp đã xác thực hồ sơ, không cần gửi email nhắc nhở, active_dtnet_mail id: " . $row_mail_active_dtnet['id'] . "\n";
                        file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Doanh nghiệp đã xác thực hồ sơ, không cần gửi email nhắc nhở, active_dtnet_mail id: " . $row_mail_active_dtnet['id'] . "\n", FILE_APPEND);
                        continue;
                    }

                    // Nếu chưa xác thực, cập nhật bidding_mail_id và chuyển sang xử lý tiếp theo
                    $db->query('UPDATE ' . $config['prefix'] . '_vi_active_dtnet_mail SET bidding_mail_id = ' . $check_exists['id'] . ' WHERE id=' . $row_mail_active_dtnet['id']);
                }
            }

            // kiểm tra các dn trên dauthau.net -> gọi api GetBidsProfile
            echo '- Kiểm tra đã có thông tin doanh nghiệp đăng ký trên dauthau.net chưa: company_name: ' . $business_info['companyname'] . ', tax_code: ' . $business_info['code'] . "\n";
            $has_profile = false;
            $is_verified = false;
            $prof_code = !empty($business_info['code']) ? $business_info['code'] : '';
            $profile_info = [];
            if (!empty($prof_code)) {
                $params = [
                    'prof_code' => $business_info['code'],
                ];
                $connect_api = [
                    'api_url' => API_DAUTHAUNET_URL,
                    'api_key' => API_DAUTHAUNET_KEY,
                    'api_secret' => API_DAUTHAUNET_SECRET,
                ];
                $respon = call_api($connect_api, '', 'GetBidsProfile', $params);

                if (!empty($respon['profile_info']['profile'])) {
                    $profile_info = $respon['profile_info']['profile'];
                    $has_profile = true;
                    echo '-- Tìm thấy thông tin doanh nghiệp trên dauthau.net: id: ' . $profile_info['id'] . "\n";
                    if ($profile_info['status'] == 1 || $profile_info['userid'] > 0) {
                        $send_mail = false;
                        $is_verified = true;
                        echo '-- Không lưu bảng mkt do Đã có account xác thực chủ sở hữu hồ sơ doanh nghiệp trên DauThau.Net' . "\n";
                        file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Không lưu bảng mkt do Đã có account xác thực chủ sở hữu hồ sơ doanh nghiệp trên DauThau.Net: " . $business_info['code'] . "\n", FILE_APPEND);
                    }
                } else {
                    echo '-- Không Tìm thấy thông tin doanh nghiệp trên dauthau.net' . "\n";
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Không Tìm thấy thông tin doanh nghiệp trên dauthau.net: " . $business_info['code'] . "\n", FILE_APPEND);
                }
            } else {
                $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -14 WHERE id = " . $row_mail_active_dtnet['id']);
                echo "-- Không thể kiểm tra thông tin trên dauthau.net do không có mã số thuế\n";
                file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Không thể kiểm tra thông tin trên dauthau.net do không có mã số thuế\n", FILE_APPEND);
                continue;
            }

            // Kiểm tra tài khoản đã đăng ký chưa
            $check_register = false;
            $main_user = [];
            $username = '';
            if (!empty($business_info['invoice_email']) && $send_mail) {
                $connect_api = [
                    'api_url' => API_CRM_URL,
                    'api_key' => API_CRM_KEY,
                    'api_secret' => API_CRM_SECRET
                ];

                // Gọi API kiểm tra email đã đăng ký
                $where = [];
                $where['OR'][] = [
                    '=' => [
                        'email' => $business_info['invoice_email'],
                    ],
                ];
                $order = [];
                $order['userid'] = 'ASC';

                $params_check_user = [
                    'page' => 1,
                    'perpage' => 1,
                    'where' => $where,
                    'order' => $order
                ];

                $respon_check_user = call_api($connect_api, 'users', 'ListUser', $params_check_user);
                echo "\n" . '- Kiểm tra email đã đăng ký trên hstdt chưa: ' . "\n";
                if ($respon_check_user['status'] == 'success') {
                    if (!empty($respon_check_user['data'])) {
                        $check_register = true;
                        $main_user = current($respon_check_user['data']);
                        if (!empty($main_user['username'])) {
                            $username = $main_user['username'];
                            echo "-- Email đã đăng ký tài khoản: " . $username . "\n";
                            file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Email đã đăng ký tài khoản: " . $username . "\n", FILE_APPEND);
                        }
                        $template_id = 2;
                    } else {
                        $check_register = false;
                        $template_id = 1;
                        echo "-- Email chưa đăng ký tài khoản\n";
                        file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Email chưa đăng ký tài khoản\n", FILE_APPEND);
                    }
                } else {
                    $check_register = null;
                    echo "-- Lỗi khi kiểm tra tài khoản: " . (isset($respon_check_user['message']) ? $respon_check_user['message'] : 'Unknown error') . "\n";
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Lỗi khi kiểm tra tài khoản: " . (isset($respon_check_user['message']) ? $respon_check_user['message'] : 'Unknown error') . "\n", FILE_APPEND);
                    $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -15 WHERE id = " . $row_mail_active_dtnet['id']);
                    continue;
                }

                if ($check_register === null) {
                    continue;
                }
            }

            $data_insert = [];
            $data_insert['contact_id'] = $business_info['code'];
            $data_insert['email'] = $business_info['invoice_email'];

            // Link hồ sơ từ dauthau.net
            $link_bids_profile = 'https://dauthau.net/vi/dn/content/';
            if (!empty($profile_info['prof_alias'])) {
                $link_bids_profile = 'https://dauthau.net/vi/dn/' . $profile_info['prof_alias'];
            }

            // Xác định loại liên hệ
            if ($check_register) {
                $data_insert['contact_type'] = 2; // Đã đăng ký tài khoản
            } else {
                $data_insert['contact_type'] = 1; // Chưa đăng ký tài khoản
            }

            $data_insert['company_name'] = $business_info['companyname'];
            $data_insert['link_bids_profile'] = $link_bids_profile;
            $data_insert['rep_name'] = $business_info['rep_name'];
            $data_insert['add_time'] = NV_CURRENTTIME;
            $data_insert['updatetime'] = NV_CURRENTTIME;
            $data_insert['verified'] = $is_verified ? 1 : 0; // Cập nhật trạng thái đã xác thực

            // Thêm kiểm tra trước khi thêm mới
            if (empty($check_exists)) {
                // Kiểm tra trong bảng active_dtnet_mail xem doanh nghiệp này đã từng được xử lý với bidding_mail_id = -20 (đã xác thực) chưa
                $check_verified = $db->query("SELECT id FROM " . $config['prefix'] . "_vi_active_dtnet_mail WHERE business_id = " . $row_mail_active_dtnet['business_id'] . " AND bidding_mail_id = -20 LIMIT 1")->fetchColumn();

                if ($check_verified) {
                    echo "- Doanh nghiệp đã xác thực hồ sơ trước đó (theo active_dtnet_mail), không cần gửi email nhắc nhở\n";
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Doanh nghiệp đã xác thực hồ sơ trước đó (theo active_dtnet_mail), không cần gửi email nhắc nhở: " . $business_info['code'] . "\n", FILE_APPEND);
                    continue;
                }

                $sql = "INSERT INTO " . $config['prefix'] . "_vi_active_profile_dtnet_mkt
                    (contact_id, email, contact_type, company_name, link_bids_profile, rep_name, add_time, updatetime, verified)
                    VALUES
                    (:contact_id, :email, :contact_type, :company_name, :link_bids_profile, :rep_name, :add_time, :updatetime, :verified)";

                $stmt = $db->prepare($sql);
                foreach ($data_insert as $key => $value) {
                    $stmt->bindParam(':' . $key, $data_insert[$key]);
                }
                $exc = $stmt->execute();
                if ($exc) {
                    $_mkt_id = $db->lastInsertId();
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Đã lưu vào bảng active_profile_dtnet_mkt: " . $_mkt_id . "\n", FILE_APPEND);

                    // Cập nhật mkt_id vào bảng active_dtnet_mail thay vì mail_id
                    $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = " . $_mkt_id . ", template_id = " . $template_id . " WHERE id = " . $row_mail_active_dtnet['id']);
                    echo "-- Đã lưu vào bảng active_profile_dtnet_mkt, ID: " . $_mkt_id . "\n";

                    // Tính thời gian gửi lần tiếp theo
                    $next_time_send_mail_active_profile = 0;
                    if ($business_info['turn_send_mail_active_profile'] == 1) {
                        // Lần 2 gửi cách lần 1 1 tháng
                        $next_time_send_mail_active_profile = NV_CURRENTTIME + 30 * 24 * 60 * 60;
                    } elseif ($business_info['turn_send_mail_active_profile'] == 2) {
                        // Lần 3 gửi cách lần 2 3 tháng
                        $next_time_send_mail_active_profile = NV_CURRENTTIME + 90 * 24 * 60 * 60;
                    }

                    // Update bảng nv4_vi_businesslistings_addinfo
                    $db->query("UPDATE " . BUSINESS_PREFIX_GLOBAL . "_addinfo SET next_time_send_mail_active_profile = " . $next_time_send_mail_active_profile . ", id_active_dtnet_mail = 0 WHERE id = " . $business_info['id']);
                }
            } else {
                $update_fields = [];

                // Cập nhật trạng thái verified nếu đã xác thực
                if ($is_verified && $check_exists['verified'] != 1) {
                    $update_fields[] = "verified = 1";
                    echo "-- Cập nhật trạng thái đã xác thực cho doanh nghiệp\n";
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Cập nhật trạng thái đã xác thực cho doanh nghiệp: " . $business_info['code'] . "\n", FILE_APPEND);
                }

                // Cập nhật contact_type nếu đã đăng ký tài khoản
                if ($check_register && $check_exists['contact_type'] == 1) {
                    $update_fields[] = "contact_type = 2";
                    echo "-- Cập nhật contact_type từ 1 thành 2 do doanh nghiệp đã đăng ký tài khoản\n";
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Cập nhật contact_type từ 1 thành 2 do doanh nghiệp đã đăng ký tài khoản: " . $business_info['code'] . "\n", FILE_APPEND);
                }

                // Thực hiện cập nhật nếu có thay đổi
                if (!empty($update_fields)) {
                    $update_fields[] = "updatetime = " . NV_CURRENTTIME;
                    $sql_update = "UPDATE " . $config['prefix'] . "_vi_active_profile_dtnet_mkt SET " . implode(", ", $update_fields) . " WHERE id = " . $check_exists['id'];
                    $db->query($sql_update);
                    echo "-- Đã cập nhật thông tin trong bảng active_profile_dtnet_mkt với ID: " . $check_exists['id'] . "\n";
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Đã cập nhật thông tin trong bảng active_profile_dtnet_mkt với ID: " . $check_exists['id'] . "\n", FILE_APPEND);
                }
            }
        } else {
            echo "- Không tìm thấy thông tin doanh nghiệp trong bảng business_info, business_id: " . $row_mail_active_dtnet['business_id'] . "\n";
            file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Không tìm thấy thông tin doanh nghiệp trong bảng business_info, business_id: " . $row_mail_active_dtnet['business_id'] . "\n", FILE_APPEND);
        }
    }
    $query_mail_active_dtnet->closeCursor();
} catch (PDOException $e) {
    trigger_error($e->getMessage());
    if (file_exists($log_file)) {
        file_put_contents($log_file, '[' . date('d/m/Y H:i:s') . '] ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
    }
}

if ($num_row == 0) {
    echo "Không có dữ liệu để xử lý\n";
    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Không có dữ liệu để xử lý\n", FILE_APPEND);
    exit(1);
}

if (file_exists(NV_ROOTDIR . '/data/active_profile_dtnet.log')) {
    unlink(NV_ROOTDIR . '/data/active_profile_dtnet.log');
}

echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
