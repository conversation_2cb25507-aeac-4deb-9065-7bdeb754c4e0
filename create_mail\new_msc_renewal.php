<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 * Gửi mail nhắc gia hạn trên msc mới, chạy 1 ngày 1 lần https://vinades.org/dauthau/dauthau.info/-/issues/1356
 * Hàng ngày quét các row có bidding_mail_id = 0 (chưa gửi) trong bảng nv4_vi_business_renewal_mail để gửi mail nhắc gia hạn
 * Thay đổi cách gửi mail nhắc gia hạn MSC: lưu vào bảng nv4_vi_new_msc_renewal_mkt thay vì gửi mail trực tiếp
 */
if (isset($_GET['response_headers_detect'])) {
    exit(1);
}

define('NV_SYSTEM', true);
// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';
include NV_ROOTDIR . '/create_mail/vi.php';

$log_file = NV_ROOTDIR . '/data/new_msc_renewal_mkt_' . date('Ymd') . '.log';
file_put_contents($log_file, "=================================Chạy lưu thông tin mail nhắc gia hạn MSC vào bảng marketing lúc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
$clean_time = NV_CURRENTTIME - (30 * 86400); // 30 ngày
$count_old_data = $db->query("SELECT COUNT(*) FROM " . $config['prefix'] . "_vi_new_msc_renewal_mkt WHERE add_time < " . $clean_time)->fetchColumn();
if ($count_old_data > 0) {
    $db->query("DELETE FROM " . $config['prefix'] . "_vi_new_msc_renewal_mkt WHERE add_time < " . $clean_time);
    echo "- Đã dọn dẹp " . number_format($count_old_data) . " bản ghi cũ trong bảng new_msc_renewal_mkt (dữ liệu cũ hơn 30 ngày)\n";
    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Đã dọn dẹp " . number_format($count_old_data) . " bản ghi cũ trong bảng new_msc_renewal_mkt (dữ liệu cũ hơn 30 ngày)\n", FILE_APPEND);
}

$create_mail_file = NV_ROOTDIR . '/data/create_mail_msc_renewal_' . date('Ymd') . '.log';
if (file_exists(NV_ROOTDIR . '/data/create_mail_msc_renewal.log')) {
    file_put_contents($create_mail_file, "=================================Chay gui mail nhac gia han msc luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    exit(1);
}

if (file_exists(NV_ROOTDIR . '/data/create_mail_msc_renewal.log')) {
    file_put_contents(NV_ROOTDIR . '/data/create_mail_msc_renewal.log', NV_CURRENTTIME);
}

/* kiểm tra trong đợt gửi mail này gửi bao nhiêu lần cho 1 nhà thầu, nếu nhiều hơn 1 lần thì đánh dấu các row đó lại = -2 */
// $check_turns_send_mail_renewal = $db->query("SELECT business_id, count(id) as num FROM " . $config['prefix'] . "_business_renewal_mail WHERE bidding_mail_id = 0  GROUP BY business_id HAVING num > 1 ORDER BY business_id ASC");
// while ($result_turn = $check_turns_send_mail_renewal->fetch()) {
//     $last_check_business_id = $result_turn['business_id'];
//     $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -2 WHERE business_id = " . $result_turn['business_id'] . " AND bidding_mail_id = 0");

//     echo '- Đánh dấu nhà thầu bị trùng nhiều lần gửi mail để xử lý sau: business_id: ' . $result_turn['business_id'] . ', số lần trùng: ' . $result_turn['num'] . "\n";
// }
// $check_turns_send_mail_renewal->closeCursor();

$duplicate_business_ids = [];
$result = $db->query("SELECT business_id FROM " . $config['prefix'] . "_business_renewal_mail WHERE bidding_mail_id = 0 GROUP BY business_id HAVING COUNT(*) > 1");

while ($row = $result->fetch()) {
    $duplicate_business_ids[] = $row['business_id'];
}

if (!empty($duplicate_business_ids)) {
    $business_ids_str = implode(',', $duplicate_business_ids);
    $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -2 WHERE bidding_mail_id = 0 AND business_id IN (" . $business_ids_str . ")");
}

/* End kiểm tra */
$last_check_mail_renewal_id = 0;
$last_id_log_file = NV_ROOTDIR . '/data/last_check_mail_renewal_id.log';
if (file_exists($last_id_log_file)) {
    $last_mail_active_dtnet_id = (int)file_get_contents($last_id_log_file);
}
$limit_mail_renewal = 10;
$num_row = 0;

// run
try {
    //code test
    // $query_mail_renewal = $db->query("SELECT * FROM " . $config['prefix'] . "vi_business_renewal_mail WHERE bidding_mail_id = 0 LIMIT 100");
    // $query_mail_renewal = $db->query("SELECT * FROM " . $config['prefix'] . "_business_renewal_mail WHERE business_id IN (150546,150547,150560,150563,150565,150567,150569,150571,150573,150574) AND bidding_mail_id = 0");
    $query_mail_renewal = $db->query("SELECT * FROM " . $config['prefix'] . "_business_renewal_mail WHERE id > " . $last_check_mail_renewal_id . " AND bidding_mail_id = 0 ORDER BY id ASC LIMIT " . $limit_mail_renewal);

    $last_check_mail_renewal_id = 0;
    while ($row_mail_renewal = $query_mail_renewal->fetch()) {
        $num_row++;
        $last_check_mail_renewal_id = $row_mail_renewal['id'];
        file_put_contents($last_id_log_file, $last_check_mail_renewal_id);
        echo "\n" . "------ Bắt đầu kiểm tra mail nhắc gia hạn, id: " . $row_mail_renewal['id'] . ', business_id: ' . $row_mail_renewal['business_id'] . ' -----' . "\n";
        // Đánh dấu đã chạy
        $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -1 WHERE id = " . $row_mail_renewal['id']);
        // lấy thông tin bussiness
        $business_info = $db->query("SELECT * FROM " . BUSINESS_PREFIX_GLOBAL . "_info as BINFO LEFT JOIN " . BUSINESS_PREFIX_GLOBAL . "_addinfo AS BADDINFO ON BINFO.id = BADDINFO.id WHERE BINFO.id = " . $row_mail_renewal['business_id'])->fetch();

        if (!empty($business_info)) {
            if (!empty($business_info['email_type']) && $business_info['email_type'] == 3) {
                $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -16 WHERE id = " . $row_mail_renewal['id']);
                echo "- Không lưu vào bảng marketing do doanh nghiệp đang có hiệu lực (email_type = 3), renewal_mail id: " . $row_mail_renewal['id'] . ', business_id: ' . $row_mail_renewal['business_id'] . "\n";
                file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Không lưu vào bảng marketing do doanh nghiệp đang có hiệu lực (email_type = 3), business_id: " . $row_mail_renewal['business_id'] . "\n", FILE_APPEND);
                continue;
            }

            // kiểm tra lại thời gian hết hạn gia hạn tên trên msc của nhà thầu trước khi gửi
            // (nếu không tìm thấy thời gian hết hạn -> cập nhật trạng thái = -8 -> sang row tiếp theo)
            if (empty($business_info['expiry_time'])) {
                $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -8 WHERE id = " . $row_mail_renewal['id']);
                echo "- Không gửi được email do không tìm thấy thông tin thời gian hết hạn, renewal_mail id: " . $row_mail_renewal['id'] . ', business_id: ' . $row_mail_renewal['business_id'] . "\n";
                continue;

                // (nếu nhà thầu đã gia hạn rồi -> cập nhật trạng thái = -11 -> sang row tiếp theo)
            } elseif ($business_info['expiry_time'] > $row_mail_renewal['add_time']) {
                $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -11 WHERE id = " . $row_mail_renewal['id']);
                echo "- Không gửi email do nhà thầu đã gia hạn tên rồi, renewal_mail id: " . $row_mail_renewal['id'] . ', business_id: ' . $row_mail_renewal['business_id'] . "\n";
                continue;
            }

            // kiểm tra invoice_email
            if (empty($business_info['invoice_email'])) {
                // Nếu không có mail nhận hd điện tử -> cập nhật trạng thái = -5 -> sang row tiếp theo
                $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -5 WHERE id = " . $row_mail_renewal['id']);
                echo "- Lỗi không gửi được email do không tìm thấy email nhận hoá đơn điện tử, renewal_mail id: " . $row_mail_renewal['id'] . ', business_id: ' . $row_mail_renewal['business_id'] . "\n";
                continue;
            } else {
                // Kiểm tra validate email (nếu sai -> cập nhật trạng thái = -7 -> sang row tiếp theo)
                $email_validation_regex = "/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z-]+(\.[a-z-]+)*(\.[a-z]{2,3})$/i";
                if (!filter_var($business_info['invoice_email'], FILTER_VALIDATE_EMAIL) || !preg_match($email_validation_regex, $business_info['invoice_email'])) {
                    $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -7 WHERE id = " . $row_mail_renewal['id']);
                    echo "- Lỗi không gửi được email do email nhận hoá đơn điện tử không đúng định dạng, renewal_mail id: " . $row_mail_renewal['id'] . ', business_id: ' . $row_mail_renewal['business_id'] . ', invoice_email: ' . $business_info['invoice_email'] . "\n";
                    continue;
                }

                // Kiểm tra xem có email nhận hoá đơn điện tử nào bị trùng trên hệ thống không
                $check_duplicate_invoice_email = $db->query("SELECT count(*) FROM " . BUSINESS_PREFIX_GLOBAL . "_addinfo WHERE invoice_email = " . $db->quote($business_info['invoice_email']))->fetchColumn();
                // nếu có -> cập nhật trạng thái = -6 -> sang row tiếp theo
                if ($check_duplicate_invoice_email > 1) {
                    $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -6 WHERE id = " . $row_mail_renewal['id']);
                    echo "- Lỗi không gửi được email do phát hiện bị trùng thông tin email nhận hoá đơn điện tử với nhà thầu khác trên hệ thống, renewal_mail id: " . $row_mail_renewal['id'] . ', business_id: ' . $row_mail_renewal['business_id'] . ', invoice_email: ' . $business_info['invoice_email'] . "\n";                        continue;
                }
            }

            // Kiểm tra đã tồn tại trong bảng marketing chưa
            if (!empty($business_info['code'])) {
                $check_exists = $db->query("SELECT * FROM " . $config['prefix'] . "_vi_new_msc_renewal_mkt WHERE contact_id = " . $db->quote($business_info['code']))->fetch();
                if (!empty($check_exists)) {
                    echo "- Đã tồn tại trong bảng new_msc_renewal_mkt: " . $business_info['code'] . "\n";
                    file_put_contents($create_mail_file, "[" . date('d/m/Y H:i:s') . "] Đã tồn tại trong bảng new_msc_renewal_mkt: " . $business_info['code'] . "\n", FILE_APPEND);
                    $update_fields = [];

                    // Lấy thông tin dauthau.net
                    $respon = $respon_permission = $arr_cc_mail = $arr_cc_userid = $respon_user = $profile_info = $arr_cc_user = [];
                    $prof_code = '';
                    $prof_code = !empty($business_info['code']) ? $business_info['code'] : '';
                    $check_profile_status = 0;

                    if (!empty($prof_code)) {
                        $connect_api = [
                            'api_url' => API_DAUTHAUNET_URL,
                            'api_key' => API_DAUTHAUNET_KEY,
                            'api_secret' => API_DAUTHAUNET_SECRET
                        ];
                        $params = [
                            'prof_code' => $prof_code
                        ];

                        $respon = call_api($connect_api, '', 'GetBidsProfile', $params);
                        echo '- Kiểm tra đã có thông tin doanh nghiệp đăng ký trên dauthau.net chưa: ' . "\n";
                        if ($respon['status'] == 'success') {
                            if (!empty($respon['profile_info']['profile'])) {
                                $profile_info = $respon['profile_info']['profile'];
                                echo '-- Tìm thấy thông tin doanh nghiệp trên dauthau.net: id: ' . $profile_info['id'] . "\n";
                                if (!empty($profile_info['userid']) && $profile_info['status'] == 1) {
                                    $check_profile_status = 1;
                                    // Lấy thông tin các user có quyền quản lý hồ sơ
                                    $respon_permission = call_api($connect_api, '', 'GetProfilePermisson', $params);

                                    if ($respon_permission['status'] == 'success') {
                                        if(!empty($respon_permission['data'])) {
                                            $mail_alert_enabled = false;
                                            foreach ($respon_permission['data'] as $profile_permission) {
                                                if (!empty($profile_permission['mail_alert_msc_renewal'])) {
                                                    $mail_alert_enabled = true;
                                                    break;
                                                }
                                            }

                                            // Nếu không có người dùng nào bật nhận email
                                            if (!$mail_alert_enabled && !empty($respon_permission['data'])) {
                                                $db->query("DELETE FROM " . $config['prefix'] . "_vi_new_msc_renewal_mkt WHERE id = " . $check_exists['id']);
                                                echo "-- Xóa khỏi bảng marketing do doanh nghiệp đã tắt nhận thông báo\n";
                                                file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Xóa khỏi bảng marketing do doanh nghiệp đã tắt nhận thông báo\n", FILE_APPEND);
                                                continue;
                                            }
                                        }
                                    }
                                }
                            } else {
                                echo "- Không tìm thấy thông tin doanh nghiệp trên dauthau.net: " . $business_info['code'] . "\n";
                                file_put_contents($create_mail_file, "[" . date('d/m/Y H:i:s') . "] Không tìm thấy thông tin doanh nghiệp trên dauthau.net: " . $business_info['code'] . "\n", FILE_APPEND);
                                $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -14 WHERE id = " . $row_mail_renewal['id']);
                                continue;
                            }
                        } else {
                            echo "- Lỗi khi gọi API GetBidsProfile: " . (isset($respon['message']) ? $respon['message'] : 'Unknown error') . "\n";
                            file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Lỗi khi gọi API GetBidsProfile: " . (isset($respon['message']) ? $respon['message'] : 'Unknown error') . "\n", FILE_APPEND);
                            $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -13 WHERE id = " . $row_mail_renewal['id']);
                            continue;
                        }
                    }

                    // Kiểm tra tài khoản đã đăng ký chưa
                    $check_register = false;
                    $main_user = [];
                    $username = '';

                    try {
                        if (!empty($business_info['invoice_email'])) {
                            $connect_api = [
                                'api_url' => API_CRM_URL,
                                'api_key' => API_CRM_KEY,
                                'api_secret' => API_CRM_SECRET
                            ];
                            // Gọi api lấy thông tin email của user
                            $where = [];
                            $where['OR'][] = [
                                '=' => [
                                    'email' => $business_info['invoice_email'],
                                ],
                            ];
                            $order = [];
                            $order['userid'] = 'ASC';

                            $params_check_user = [
                                'page' => 1,
                                'perpage' => 1,
                                'where' => $where,
                                'order' => $order
                            ];

                            $respon_check_user = call_api($connect_api, 'users', 'ListUser', $params_check_user);
                            echo "\n" . '- Kiểm tra email đã đăng ký trên hstdt chưa: ' . "\n";

                            if ($respon_check_user['status'] != 'success') {
                                echo "- Lỗi khi gọi API ListUser: " . (isset($respon_check_user['message']) ? $respon_check_user['message'] : 'Unknown error') . "\n";
                                file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Lỗi khi gọi API ListUser: " . (isset($respon_check_user['message']) ? $respon_check_user['message'] : 'Unknown error') . "\n", FILE_APPEND);
                                $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -15 WHERE id = " . $row_mail_renewal['id']);
                                continue;
                            }

                            if (!empty($respon_check_user['data'])) {
                                $check_register = true;
                                $main_user = current($respon_check_user['data']);
                                if (!empty($main_user['username'])) {
                                    $username = $main_user['username'];

                                    // Nếu ban đầu chưa đăng ký (contact_type = 1), sau đó đã đăng ký
                                    if ($check_exists['contact_type'] == 1) {
                                        $update_fields[] = "contact_type = 2";
                                        $update_fields[] = "username = " . $db->quote($username);
                                        $update_fields[] = "update_time = " . NV_CURRENTTIME;
                                        echo "-- Cập nhật contact_type từ 1 thành 2 do doanh nghiệp đã đăng ký tài khoản\n";
                                        file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Cập nhật contact_type từ 1 thành 2 do doanh nghiệp đã đăng ký tài khoản\n", FILE_APPEND);
                                    }
                                }
                            }
                        }
                    } catch (Exception $e) {
                        echo "- Lỗi khi kiểm tra tài khoản: " . $e->getMessage() . "\n";
                        file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Lỗi khi kiểm tra tài khoản: " . $e->getMessage() . "\n", FILE_APPEND);
                        $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -15 WHERE id = " . $row_mail_renewal['id']);
                        trigger_error($e->getMessage());
                        continue;
                    }

                    // Trường hợp 2: Cập nhật thời gian hết hạn nếu đã thay đổi
                    if (!empty($business_info['expiry_time']) && $business_info['expiry_time'] != $check_exists['expiry_time_timestamp']) {
                        $update_fields[] = "expiry_time = 'ngày " . date('d', $business_info['expiry_time']) . " tháng " . date('m', $business_info['expiry_time']) . " năm " . date('Y', $business_info['expiry_time']) . "'";
                        $update_fields[] = "expiry_time_timestamp = " . $business_info['expiry_time'];
                        $update_fields[] = "renewed = 1";
                        $update_fields[] = "update_time = " . NV_CURRENTTIME;
                        echo "-- Cập nhật expiry_time do doanh nghiệp đã gia hạn MSC, thời hạn mới: " . date('d/m/Y', $business_info['expiry_time']) . "\n";
                        file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Cập nhật expiry_time do doanh nghiệp đã gia hạn MSC, thời hạn mới: " . date('d/m/Y', $business_info['expiry_time']) . "\n", FILE_APPEND);
                    }

                    // Thực hiện cập nhật nếu có thay đổi
                    if (!empty($update_fields)) {
                        $sql_update = "UPDATE " . $config['prefix'] . "_vi_new_msc_renewal_mkt SET " . implode(", ", $update_fields) . " WHERE id = " . $check_exists['id'];
                        $db->query($sql_update);
                        echo "-- Đã cập nhật thông tin trong bảng marketing với ID: " . $check_exists['id'] . "\n";
                        file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Đã cập nhật thông tin trong bảng marketing với ID: " . $check_exists['id'] . "\n", FILE_APPEND);
                    }

                    $db->query('UPDATE ' . $config['prefix'] . '_business_renewal_mail SET bidding_mail_id = ' . $check_exists['id'] . ' WHERE id=' . $row_mail_renewal['id']);
                    continue;
                }
            }

            // Lấy thông tin dauthau.net
            $respon = $respon_permission = $arr_cc_mail = $arr_cc_userid = $respon_user = $profile_info = $arr_cc_user = [];
            $prof_code = '';
            $prof_code = !empty($business_info['code']) ? $business_info['code'] : '';
            $check_profile_status = 0;
            $mail_alert_enabled = true;

            if (!empty($prof_code)) {
                $connect_api = [
                    'api_url' => API_DAUTHAUNET_URL,
                    'api_key' => API_DAUTHAUNET_KEY,
                    'api_secret' => API_DAUTHAUNET_SECRET
                ];
                $params = [
                    'prof_code' => $prof_code
                ];

                $respon = call_api($connect_api, '', 'GetBidsProfile', $params);
                echo '- Kiểm tra đã có thông tin doanh nghiệp đăng ký trên dauthau.net chưa: ' . "\n";

                if ($respon['status'] != 'success') {
                    // Nếu API lỗi -> cập nhật trạng thái = -13 -> dừng xử lý
                    $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -13 WHERE id = " . $row_mail_renewal['id']);
                    echo "-- Lỗi khi gọi API GetBidsProfile: " . (isset($respon['message']) ? $respon['message'] : 'Unknown error') . "\n";
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Lỗi khi gọi API GetBidsProfile: " . (isset($respon['message']) ? $respon['message'] : 'Unknown error') . "\n", FILE_APPEND);
                    continue;
                }

                if (!empty($respon['profile_info']['profile'])) {
                    $profile_info = $respon['profile_info']['profile'];
                    echo '-- Tìm thấy thông tin doanh nghiệp trên dauthau.net: id: ' . $profile_info['id'] . "\n";
                    if (!empty($profile_info['userid']) && $profile_info['status'] == 1) {
                        $check_profile_status = 1;
                        // Lấy thông tin các user có quyền quản lý hồ sơ
                        $respon_permission = call_api($connect_api, '', 'GetProfilePermisson', $params);

                        if ($respon_permission['status'] == 'success') {
                            if(!empty($respon_permission['data'])) {
                                $mail_alert_enabled = false;
                                foreach ($respon_permission['data'] as $profile_permission) {
                                    if (!empty($profile_permission['mail_alert_msc_renewal'])) {
                                        $mail_alert_enabled = true;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Kiểm tra tài khoản đã đăng ký chưa
            $check_register = false;
            $main_user = [];
            $username = '';

            if (!empty($business_info['invoice_email'])) {
                $connect_api = [
                    'api_url' => API_CRM_URL,
                    'api_key' => API_CRM_KEY,
                    'api_secret' => API_CRM_SECRET
                ];
                // Gọi api lấy thông tin email của user
                $where = [];
                $where['OR'][] = [
                    '=' => [
                        'email' => $business_info['invoice_email'],
                    ],
                ];
                $order = [];
                $order['userid'] = 'ASC';

                $params_check_user = [
                    'page' => 1,
                    'perpage' => 1,
                    'where' => $where,
                    'order' => $order
                ];

                $respon_check_user = call_api($connect_api, 'users', 'ListUser', $params_check_user);
                echo "\n" . '- Kiểm tra email đã đăng ký trên hstdt chưa: ' . "\n";

                if ($respon_check_user['status'] == 'success') {
                    if (!empty($respon_check_user['data'])) {
                        $check_register = true;
                        $main_user = current($respon_check_user['data']);
                        if (!empty($main_user['username'])) {
                            $username = $main_user['username'];
                        }
                    }
                } else {
                    echo "- Lỗi khi gọi API ListUser: " . (isset($respon_check_user['message']) ? $respon_check_user['message'] : 'Unknown error') . "\n";
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Lỗi khi gọi API ListUser: " . (isset($respon_check_user['message']) ? $respon_check_user['message'] : 'Unknown error') . "\n", FILE_APPEND);
                    $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -15 WHERE id = " . $row_mail_renewal['id']);
                    continue;
                }
            }

            // Nếu đã tắt nhận email, không lưu vào bảng marketing
            if (!$mail_alert_enabled) {
                $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -12 WHERE id = " . $row_mail_renewal['id']);
                echo "-- Bỏ qua do doanh nghiệp đã tắt nhận email\n";
                file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Bỏ qua do doanh nghiệp đã tắt nhận email\n", FILE_APPEND);
                continue;
            }

            // Chuẩn bị thông tin phí
            $total_money = 0;
            $table_html = '';
            if (!empty($business_info['short_fee_info'])) {
                $short_fee_info = json_decode($business_info['short_fee_info'], true);
                if (!empty($short_fee_info)) {
                    $table_html = '<table class="table table-bidding table-bordered table-hover" style="width: 100%; border: 1px solid #F5F5F5;">
                            <thead>
                                <tr>
                                    <th><b>' . $lang_module['number'] . '</b></th>
                                    <th><b>' . $lang_module['fee_type_name'] . '</b></th>
                                    <th><b>' . $lang_module['contractor_role'] . '</b></th>
                                    <th><b>' . $lang_module['maintain_year'] . '</b></th>
                                    <th><b>' . $lang_module['payment_exp_date'] . '</b></th>
                                    <th><b>' . $lang_module['fee_charge'] . '</b></th>
                                    <th><b>' . $lang_module['fee_vat'] . '</b></th>
                                    <th><b>' . $lang_module['fee_total'] . '</b></th>
                                </tr>
                            </thead>
                            <tbody>';

                    $stt = 0;
                    foreach ($short_fee_info as $value) {
                        $stt++;
                        $total_money += isset($value['feeTotal']) ? $value['feeTotal'] : 0;

                        $fee_type_name = '';
                        if (!empty($value['feeTypeCode'])) {
                            $fee_type_name = $lang_module['fee_type_name_' . $value['feeTypeCode']];
                        }

                        $table_html .= '<tr>
                                <td>' . $stt . '</td>
                                <td>' . $fee_type_name . '</td>
                                <td>' . $value['contractorRole'] . '</td>
                                <td>' . $value['maintainYear'] . '</td>
                                <td>' . (!empty($value['paymentExpDate']) ? date('d/m/Y', strtotime($value['paymentExpDate'])) : '') . '</td>
                                <td>' . number_format($value['feeCharge'], 0, '', '.') . '</td>
                                <td>' . number_format($value['feeVat'], 0, '', '.') . '</td>
                                <td>' . number_format($value['feeTotal'], 0, '', '.') . '</td>
                            </tr>';
                    }

                    $table_html .= '</tbody></table>';
                }
            }

            $data_insert = [];
            $data_insert['contact_id'] = $business_info['code'];
            $data_insert['email'] = $business_info['invoice_email'];

            // Link hồ sơ từ dauthau.net
            $link_bids_profile = 'https://dauthau.net/vi/dn/content/';
            $link_permission = '';

            if (!empty($profile_info['prof_alias'])) {
                $link_bids_profile = 'https://dauthau.net/vi/dn/' . $profile_info['prof_alias'];
            }

            // Link permission nếu có profile_id
            if (!empty($profile_info['id'])) {
                $link_permission = 'https://dauthau.net/vi/dn/permission/?profile_id=' . $profile_info['id'];
            }

            // Xác định loại liên hệ
            if ($check_register) {
                $data_insert['contact_type'] = 2;
            } else {
                $data_insert['contact_type'] = 1;
            }

            $data_insert['add_time'] = NV_CURRENTTIME;
            $data_insert['update_time'] = NV_CURRENTTIME;
            $data_insert['company_name'] = $business_info['companyname'];
            $data_insert['rep_name'] = $business_info['rep_name'];

            // Định dạng thời gian hết hạn
            if (!empty($business_info['expiry_time'])) {
                $expiry_day = date('d', $business_info['expiry_time']);
                $expiry_month = date('m', $business_info['expiry_time']);
                $expiry_year = date('Y', $business_info['expiry_time']);
                $data_insert['expiry_time'] = 'ngày ' . $expiry_day . ' tháng ' . $expiry_month . ' năm ' . $expiry_year;
                $data_insert['expiry_time_timestamp'] = $business_info['expiry_time'];
            } else {
                $data_insert['expiry_time'] = '';
                $data_insert['expiry_time_timestamp'] = 0;
            }

            $data_insert['short_fee_info'] = $table_html;
            $data_insert['link_bids_profile'] = $link_bids_profile;
            $data_insert['link_permission'] = $link_permission;
            $data_insert['username'] = $username;
            $data_insert['total_money'] = number_format($total_money, 0, '', '.');
            $data_insert['renewed'] = 0;

            // Kiểm tra đã gia hạn MSC chưa
            if (!empty($business_info['expiry_time']) && $business_info['expiry_time'] > NV_CURRENTTIME) {
                $data_insert['renewed'] = 1;
            }

            // Lưu vào bảng marketing
            try {
                $sql = "INSERT INTO " . $config['prefix'] . "_vi_new_msc_renewal_mkt
                       (contact_id, email, contact_type, add_time, update_time, company_name, rep_name,
                       expiry_time, expiry_time_timestamp, short_fee_info, total_money, username, link_bids_profile, link_permission, renewed)
                       VALUES
                       (:contact_id, :email, :contact_type, :add_time, :update_time, :company_name, :rep_name,
                       :expiry_time, :expiry_time_timestamp, :short_fee_info, :total_money, :username, :link_bids_profile, :link_permission, :renewed)";

                $stmt = $db->prepare($sql);
                foreach ($data_insert as $key => $value) {
                    $stmt->bindParam(':' . $key, $data_insert[$key]);
                }
                $exc = $stmt->execute();

                // Đánh dấu là đã lưu
                if ($exc) {
                    $_mkt_id = $db->lastInsertId();
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] MarketingID: " . $_mkt_id . ";\n", FILE_APPEND);
                    $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = " . $_mkt_id . " WHERE id = " . $row_mail_renewal['id']);
                    echo '-- email: ' . $business_info['invoice_email'] . "\n";
                    echo '-- Đã lưu vào bảng ' . $config['prefix'] . '_vi_new_msc_renewal_mkt, ID: ' . $_mkt_id . "\n";
                }
            } catch (PDOException $e) {
                if ($e->errorInfo[1] == 1062) { // Duplicate entry
                    echo "- Lỗi: Doanh nghiệp đã tồn tại trong bảng marketing\n";
                    $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -10 WHERE id = " . $row_mail_renewal['id']);
                    continue;
                } else {
                    print_r('Lỗi thêm thông tin vào bảng marketing: ' . $e->getMessage() . "\n");
                    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] ERROR INSERT INTO marketing: mail_renewal_id: " . $row_mail_renewal['id'] . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                    $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -9 WHERE id = " . $row_mail_renewal['id']);
                    trigger_error($e->getMessage());
                    continue;
                }
            }
        } else {
            echo "- Lỗi không lưu được thông tin do không tìm thấy được thông tin nhà thầu\n";
            // Cập nhật lại trạng thái
            $db->query("UPDATE " . $config['prefix'] . "_business_renewal_mail SET bidding_mail_id = -4 WHERE id = " . $row_mail_renewal['id']);
        }
    }
    $query_mail_renewal->closeCursor();
} catch (PDOException $e) {
    trigger_error($e->getMessage());
    if (file_exists($log_file)) {
        file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] ERROR: " . print_r($e, true) . "\n\n", FILE_APPEND);
    }
}

if ($num_row == 0) {
    echo "Không có dữ liệu để xử lý\n";
    file_put_contents($log_file, "[" . date('d/m/Y H:i:s') . "] Không có dữ liệu để xử lý\n", FILE_APPEND);
    exit(1);
}

// Kết thúc
if (file_exists(NV_ROOTDIR . '/data/create_mail_msc_renewal.log')) {
    unlink(NV_ROOTDIR . '/data/create_mail_msc_renewal.log');
}

echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
