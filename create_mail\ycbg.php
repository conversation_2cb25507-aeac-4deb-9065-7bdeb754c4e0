<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> - VINADES.,JSC <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 28/11/2024, 17:00
 */

// Gửi mail VIP8

if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$date_format = ($prefix_lang == 1) ? 'm/d/Y' : 'd/m/Y';
$datetime_format = ($prefix_lang == 1) ? 'm/d/Y H:i' : 'd/m/Y H:i';
$create_mail_file = NV_ROOTDIR . '/data/create_mail_ycbg_' . $prefix_lang . '_' . date('Ymd') . '.txt';

register_shutdown_function("fatal_handler");

function fatal_handler()
{
    global $create_mail_file;
    $error = error_get_last();
    if ($error !== NULL) {
        echo ('<pre><code>' . print_r($error, true) . '</code></pre>');
        file_put_contents($create_mail_file, '<pre><code>' . print_r($error, true) . '</code></pre>', FILE_APPEND);
    }
}

if (file_exists(NV_ROOTDIR . '/data/create_mail_ycbg_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "ycbg.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit("Chay trung nhau");
}

file_put_contents(NV_ROOTDIR . '/data/create_mail_ycbg_' . $prefix_lang . '.txt', NV_CURRENTTIME);
file_put_contents($create_mail_file, "======== Bắt đầu chạy: " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);

// Khởi tạo biến lưu thông tin thông báo
$arrInform = $list_info = [];
try {

    // Từ bảng tmp tổng hợp lại mail gửi đi cho từng VIP
    // Lấy danh sách các mail chưa gửi trong csdl để tiến hành gửi đi gom theo userid
    $vips = [];
    $list_vipid = [];

    if (file_exists(NV_ROOTDIR . '/data/create_mail_ycbg_' . $prefix_lang . '_log.txt')) {
        $log_customsid = file_get_contents(NV_ROOTDIR . '/data/create_mail_ycbg_' . $prefix_lang . '_log.txt');
        $log_customsid = explode('_', $log_customsid);
        $last_customsid = intval($log_customsid[0]);
        $last_customspoint = intval($log_customsid[1]);
    } else {
        $last_customsid = $last_customspoint = 0;
    }

    // các userid vip đến hạn gửi email
    $array_vip_userid = [];
    $query = $db->query('SELECT user_id FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE ((' . NV_CURRENTTIME . ' - last_email) >= time_send*3600) AND vip=8 AND status = 1 AND prefix_lang = ' . $prefix_lang . ' AND user_id > ' . $last_customsid . ' ORDER BY user_id ASC LIMIT 50');
    while ($row = $query->fetch()) {
        $array_vip_userid[$row['user_id']] = $row['user_id'];
        $last_customsid = $row['user_id'];
    }

    if (!empty($array_vip_userid)) {
        $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg_id WHERE userid IN (' . implode(',', $array_vip_userid) . ') AND send_status = 0 LIMIT 20');
        while ($row = $query->fetch()) {
            $vips[$row['userid']] = array();
            $list_vipid[$row['userid']] = $row['userid'];
        }
    } else {
        $last_customsid = 0;
    }

    $list_vipid_all = [];
    // lấy các tài khoản mua bằng điểm, chu kỳ mail 6h
    $config_bidding['use_reward_points'] = 1;
    if ($config_bidding['use_reward_points']) {
        $arr_customs_points_email = [];
        $query = $db->query('SELECT userid FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE ((' . NV_CURRENTTIME . ' - lastmail) >= (6*3600)) AND vip=8 AND prefix_lang = ' . $prefix_lang . ' AND userid > ' . $last_customspoint . ' ORDER BY userid ASC LIMIT 50');
        while ($row = $query->fetch()) {
            $arr_customs_points_email[$row['userid']] = $row['userid'];
            $last_customspoint = $row['userid'];
        }

        $array_id_user = [];
        if (!empty($arr_customs_points_email)) {
            $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg_id WHERE userid IN (' . implode(',', $arr_customs_points_email) . ') AND send_status = 0 LIMIT 10');
            while ($row = $query->fetch()) {
                $vips[$row['userid']] = [];
                $array_id_user[$row['userid']] = $row['userid'];
            }
        } else {
            $last_customspoint = 0;
        }

        ksort($vips);
        $list_vipid_all = array_merge($list_vipid, $array_id_user);
    } else {
        $list_vipid_all = $list_vipid;
    }
    file_put_contents(NV_ROOTDIR . '/data/create_mail_ycbg_' . $prefix_lang . '_log.txt', $last_customsid . '_' . $last_customspoint);

    $list_vipid_all = implode(',', $list_vipid_all);
    if (!empty($list_vipid_all)) {
        $arr_vip = $db->query('SELECT id as cusid, phone, user_id as userid, email, sub_email, time_send, split_email_filters, last_email, active_user, from_time FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id IN ( ' . $list_vipid_all . ') AND vip=8 AND status = 1 AND prefix_lang = ' . $prefix_lang . '');

        while ($vip_data = $arr_vip->fetch()) {
            // nếu vip này bật chế độ gửi email theo tài khoản
            if ($vip_data['active_user'] == 1) {
                $arr_subemail = [];
                $query_permission = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_permission WHERE vipid=' . $vip_data['cusid'] . ' AND totime >' . NV_CURRENTTIME . ' AND send_mail = 1 AND prefix_lang = ' . $prefix_lang . '');
                while ($permission = $query_permission->fetch()) {
                    $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid = ' . $permission['userid'] . '');
                    while ($user = $arr_user->fetch()) {
                        $arr_subemail[$user['email']] = $user['email'];
                    }
                }
                $vip_data['sub_email'] = implode(',', $arr_subemail);
            }

            $vips[$vip_data['userid']] = $vip_data;
            $vips[$vip_data['userid']]['filter'] = array();
            $vips[$vip_data['userid']]['notify'] = array();
            $vips[$vip_data['userid']]['rqid'] = array();
            $vips[$vip_data['userid']]['result'] = array();
            $vips[$vip_data['userid']]['result_open'] = array();
            $vips[$vip_data['userid']]['is_vip'] = 1;
        }

        if ($config_bidding['use_reward_points'] and !empty($array_id_user)) {
            $arr_user = $db->query('SELECT t1.userid, t1.email, t2.id as cusid, t2.lastmail, t2.datecreate as from_time FROM nv4_users as t1 INNER JOIN ' . BID_PREFIX_GLOBAL . '_customs_points_email as t2 ON t1.userid= t2.userid WHERE t1.userid IN ( ' . implode(',', $array_id_user) . ') AND t2.prefix_lang = ' . $prefix_lang . '');
            while ($user_data = $arr_user->fetch()) {
                if (!isset($vips[$user_data['userid']]['email'])) {
                    $vips[$user_data['userid']] = $user_data;
                    $vips[$user_data['userid']]['filter'] = [];
                    $vips[$user_data['userid']]['notify'] = '';
                    $vips[$user_data['userid']]['plan'] = [];
                    $vips[$user_data['userid']]['time_send'] = 6;
                    $vips[$user_data['userid']]['sub_email'] = '';
                    $vips[$user_data['userid']]['is_vip'] = 0;
                }
            }
        }
    }

    // Nếu có dữ liệu danh sách user thì vào đây
    if (!empty($vips)) {
        // lấy username
        $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid IN ( ' . implode(',', array_keys($vips)) . ')');
        while ($user = $arr_user->fetch()) {
            $vips[$user['userid']]['username'] = $user['username'];
        }

        // Lấy ra tất cả tin đấu thầu trong danh sách gửi đi của tất cả các VIP
        // Lưu nội dung mail vào csdl
        foreach ($vips as $vip_id => $vip) {
            file_put_contents($create_mail_file, 'BEGIN: ' . $vip_id . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
            $arr_ycbg = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg WHERE id IN (SELECT rq_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg_id WHERE send_status = 0 AND rq_id>0 AND userid=' . $vip_id . ')');
            while ($ycbg_row = $query->fetch()) {
                $arr_ycbg[$ycbg_row['id']] = $ycbg_row;
            }

            // Gộp nhóm những mail chung chủ đề lại theo từng VIP
            $array_ycbg_id = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg_id WHERE send_status = 0 AND userid=' . $vip_id . ' LIMIT 50');
            while ($rqid_data = $query->fetch()) {
                $array_ycbg_id[$rqid_data['id']] = $rqid_data['id'];
                if ($rqid_data['filter_id'] > 0) {
                    $vip['filter'][$rqid_data['filter_id']]['list_ycbg'][] = $arr_ycbg[$rqid_data['rq_id']];
                    $vip['rqid'][] = $rqid_data['rq_id'];
                }
            }
            // Cập nhật lại lần cuối cùng gửi mail cho các tài khoản VIP, Phần này cần chạy trước beg-----inTransaction để nếu có lỗi thì bỏ qua 1 tài khảo.
            if (sizeof($array_ycbg_id) < 50) {
                // Nếu 1 lần tổng hợp có > 50 thì không cập nhật ngay last_email để 1 phút sau sẽ chạy tiếp

                if ($vip['is_vip']) {
                    $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET last_email = ' . NV_CURRENTTIME . ' WHERE id =' . $vip['cusid'] . ' AND vip = 8');
                } else {
                    // do mua theo bộ lọc nên 1 tài khoản có nhiều row, dẫn tới cần ghi lastmail theo userid
                    $stmt = $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs_points_email SET lastmail=' . NV_CURRENTTIME . ' WHERE userid =' . $vip_id . '');
                }
            } else {
                file_put_contents($create_mail_file, "Con chay nua: " . sizeof($array_ycbg_id) . " / " . sizeof($arr_ycbg) . "\n", FILE_APPEND);
            }

            $db->beginTransaction();

            try {
                $data_insert = array();
                $data_insert['addtime'] = NV_CURRENTTIME;
                $data_insert['send_time'] = 0;
                $data_insert['status'] = 0;
                // Gửi mail thông báo mới thầu, kế hoạch chọn nhà thầu, thông báo sơ tuyển nhóm theo từng bộ lọc
                if (!empty($vip['filter']) and !empty($vip['email'])) {
                    $data_insert['title'] = sprintf($lang_module['mail_title_ycbg'], date($date_format));
                    $data_insert['type'] = 0;
                    $data_insert['vip'] = 8;

                    file_put_contents($create_mail_file, "Begin nv_theme_bidding_mail\n", FILE_APPEND);

                    if ($vip['split_email_filters'] == 1) {
                        // Phân chia email thông báo mời thầu riêng biệt cho từng bộ lọc
                        foreach ($vip['filter'] as $filterId => $filter) {
                            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filterId)->fetch();
                            $title = sprintf($lang_module['mail_title_ycbg_split'], date($date_format)) . sprintf($lang_module['with_filter'], $filter_info['title']);
                            $data_insert['content'] = nv_theme_bidding_mail([
                                $filterId => $filter
                            ], $vip_id, $arrInform);
                            $data_insert['content'] .= sprintf($lang_module['mail_footer_ycbg'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date($date_format, $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');

                            file_put_contents($create_mail_file, "END nv_theme_bidding_mail filter number $count\n", FILE_APPEND);
                            // Nội dung htm sẽ gửi cho từng khách
                            try {
                                file_put_contents($create_mail_file, "BIGIN INSERT INTO nv4_vi_bidding_mail\n", FILE_APPEND);
                                $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                                $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
                                $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                                $stmt->bindParam(':title', $title, PDO::PARAM_STR);
                                $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                                $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                                $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                                $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                                $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                                $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                                $_mailid = $db->lastInsertId();
                                file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            } catch (Exception $e) {
                                print_r('Lỗi thêm mail tin mới vào csdl');
                                file_put_contents($create_mail_file, "ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                                notify_to_slack($create_mail_file, "ycbg.php ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                                trigger_error($e->getMessage());
                                echo ($e->getMessage()); // Remove this line after checks finished
                                break;
                            }
                        }
                    } else {
                        $data_insert['content'] = nv_theme_bidding_mail($vip['filter'], $vip_id, $arrInform);
                        $data_insert['content'] .= sprintf($lang_module['mail_footer_ycbg'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date($date_format, $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');
                        file_put_contents($create_mail_file, "END nv_theme_bidding_mail\n", FILE_APPEND);
                        // Nội dung htm sẽ gửi cho từng khách
                        try {
                            file_put_contents($create_mail_file, "BEGIN INSERT INTO nv4_vi_bidding_mail\n", FILE_APPEND);
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
                            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();
                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (Exception $e) {
                            print_r('Lỗi thêm mail tin mới vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "ycbg.php ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo ($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }
                }

                echo "vip_id = " . $vip_id . "\n";
                // Thực hiện lưu thông báo vào biến tạm
                foreach ($arrInform as $k => $v) {
                    foreach ($v as $v1) {
                        $list_info[] = $v1;
                    }
                }

                // Reset lại biến này về rỗng để tiến hành lưu mảng dữ liệu tiếp theo
                $arrInform = [];

                // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                if (!empty($array_ycbg_id) and !empty($_mailid)) {
                    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_ycbg_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_ycbg_id) . ') AND send_status = 0');
                }
                $db->commit();

                file_put_contents($create_mail_file, "number ycbg = " . sizeof($array_ycbg_id) . ": " . implode(',', $array_ycbg_id) . "\n", FILE_APPEND);
                file_put_contents($create_mail_file, "END: " . $vip_id . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
                // print_r($array_ycbg_id);
            } catch (Exception $e) {
                file_put_contents($create_mail_file, 'rollBack ' . $vip_id . "\n\n", FILE_APPEND);
                file_put_contents($create_mail_file, 'ERROR 390: ' . print_r($e, true) . "\n\n", FILE_APPEND);
                $db->rollBack();
                echo '<pre>';
                print_r($e);
            }
        }

    } else {
        echo "khong co vip \n";
    }

    unlink(NV_ROOTDIR . '/data/create_mail_ycbg_' . $prefix_lang . '.txt');
} catch (Exception $e) {
    file_put_contents($create_mail_file, 'ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "ycbg.php ERROR: " . print_r($e, true) . "\n\n");
}

// Thực hiện lưu thông báo
if (!empty($list_info)) {
    echo "Có tổng " . sizeof($list_info) . ' Thông báo Inform';
    file_put_contents(NV_ROOTDIR . '/data/inform/inform_ycbg' . uniqid('', true) . '.txt', json_encode($list_info));
}

echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
file_put_contents($create_mail_file, "Chạy xong: " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);

function nv_theme_bidding_mail($array_filter, $vip_id, &$arrInform)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func, $prefix_lang, $date_format, $datetime_format;

    // Phân loại báo giá
    $arr_request_quote_type = [
        'THUOC' => $lang_module['request_quote_type_THUOC'],
        'VTTT' => $lang_module['request_quote_type_VTTT'],
        'VATTU' => $lang_module['request_quote_type_VATTU'],
    ];

    $arr_request_quote_form = [
        'OFFLINE' => $lang_module['request_quote_form_OFFLINE'],
        'EMAIL' => $lang_module['request_quote_form_EMAIL'],
        'FAX' => $lang_module['request_quote_form_FAX'],
    ];
    $rq_validity_period_unit = [
        'D' => $lang_module['rq_validity_period_unit_D'],
        'M' => $lang_module['rq_validity_period_unit_M'],
        'Y' => $lang_module['rq_validity_period_unit_Y'],
    ];

    if (!empty($array_filter)) {
        $xtpl = new XTemplate('email_new_ycbg.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        foreach ($array_filter as $filter_id => $arr_list) {
            // Lấy tên bộ lọc
            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();

            // link sửa bộ lọc
            $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
            $from = $filter_info['time_find'] > 0 ? nv_date($date_format, NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date($date_format, NV_CURRENTTIME - 86400 * 14);
            $filter_info['link_search'] = '';
            $filter_info['link_search_detail'] = NV_MY_DOMAIN . '/' . $site_lang . '/request-quote?';
            if ($filter_info['key_search'] != '') {
                $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
            }
            if ($filter_info['key_search2'] != '') {
                $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
            }
            $filter_info['link_search'] .= '&sfrom=' . $from;
            $filter_info['link_search'] .= '&sto=' . nv_date('d/m/Y', NV_CURRENTTIME);
            $filter_info['link_search'] .= '&is_advance=1';
            $filter_info['link_search'] .= '&userid=' . $vip_id;
            $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
            $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];

            if ($filter_info['without_key'] != '') {
                $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
            }

            if ($filter_info['par_search']) {
                $filter_info['link_search'] .= '&par_search=' . $filter_info['par_search'];
            }

            if ($filter_info['searchkind']) {
                $filter_info['link_search'] .= '&searchkind=' . $filter_info['searchkind'];
            }

            if (!empty($filter_info['rq_form_value'])) {
                $filter_info['link_search'] .= '&rq_form_value=' . $filter_info['rq_form_value'];
            }

            if (!empty($filter_info['rq_investor'])) {
                $rq_investor = explode(',', $filter_info['rq_investor']);
                foreach ($rq_investor as $value) {
                    $filter_info['link_search'] .= "&rq_investor[]=" . $value;
                }
            }

            $filter_info['link_search_detail'] = $filter_info['link_search_detail'] . $filter_info['link_search'];
            $xtpl->assign('LINK_FILTER', $filter_info['link_search_detail']);

            // lt
            // ghi log bộ lọc
            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);

            $xtpl->assign('FILTER_NAME', $filter_info['title']);

            if (!empty($arr_list['list_ycbg'])) {
                if (sizeof($arr_list['list_ycbg']) > 50) {
                    $xtpl->assign('FILTER_NUMBER', sprintf($lang_module['limit_filter_bid'], number_format(sizeof($arr_list['list_ycbg']))));
                    $xtpl->parse('main.filter.number');
                }

                file_put_contents($create_mail_file, 'list_ycbg_id: ' . sizeof($arr_list['list_ycbg']) . "\n", FILE_APPEND);
                $i_break = 0;

                // lọc bỏ tin trùng nhau
                $arr_sycbg = array();
                foreach ($arr_list['list_ycbg'] as $array_data) {
                    $array_data['so_ycbg'] = explode('-', $array_data['request_quote_code']);
                    if (!isset($arr_sycbg[$array_data['so_ycbg'][0]])) {
                        $arr_sycbg[$array_data['so_ycbg'][0]] = $array_data['so_ycbg'][1];
                    } else {
                        $array_data['so_ycbg'][1] = intval($array_data['so_ycbg'][1]);
                        $old = intval($arr_sycbg[$array_data['so_ycbg'][0]]);
                        if ($old < $array_data['so_ycbg'][1]) {
                            $arr_sycbg[$array_data['so_ycbg'][0]] = '0' . $array_data['so_ycbg'][1];
                        }
                    }
                }
                // hiển thị
                foreach ($arr_list['list_ycbg'] as $array_data) {
                    $so_ycbg = explode('-', $array_data['request_quote_code']);
                    if (isset($arr_sycbg[$so_ycbg[0]]) && intval($arr_sycbg[$so_ycbg[0]]) == intval($so_ycbg[1])) {
                        ++$i_break;
                        if ($i_break > 50) {
                            break;
                        }
                        $array_data['title'] = $array_data['request_quote_name'];
                        $array_data['title_a'] = nv_htmlspecialchars($array_data['request_quote_name']);

                        // Bôi vàng những từ trùng với từ khóa tìm kiếm, tìm kiếm tuyệt đối
                        $arr_key = explode(',', $filter_info['key_search']);

                        foreach ($arr_key as $key) {
                            $key = trim($key);
                            $array_data['request_quote_name'] = BoldKeywordInStr(strip_tags($array_data['request_quote_name']), $key);
                            $array_data['investor_name'] = strip_tags(BoldKeywordInStr($array_data['investor_name'], $key));
                        }
                        $array_data['public_date'] = nv_date(($prefix_lang == 1 ? 'H:i m/d/y' : 'H:i d/m/y'), $array_data['public_date']);
                        // $ycbg_t = getUrlByLanguage('ycbg');
                        if ($site_lang != 'vi') {
                            $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['request-quote'] . '/' . $array_data['alias'] . '-' . $array_data['request_quote_code'] . '.html';
                        } else {
                            $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['request-quote'] . '/' . $array_data['alias'] . '-' . $array_data['request_quote_code'] . '.html';
                        }

                        $check = md5($vip_id . $so_ycbg[0]);
                        // $array_data['link_follow'] = NV_MY_DOMAIN . '/' . $site_lang . '/bidding/follow?bid_id=' . $array_data['id'] . '&bid_code=' . $so_ycbg[0] . '&vipid=' . $vip_id . '&check=' . $check;

                        // Phân loại ycbg
                        if ($array_data['request_quote_type'] != '') {
                            $array_data['request_quote_type_title'] = (in_array($array_data['request_quote_type'], array_keys($arr_request_quote_type)) ? $arr_request_quote_type[$array_data['request_quote_type']] : '');
                        }

                        // Hình thức tiếp nhận báo giá
                        if ($array_data['request_quote_form'] != '') {
                            $request_quote_form = explode(',', $array_data['request_quote_form']);
                            $array_data['request_quote_form_title'] = '';
                            $arr_request_quote_form_title = [];
                            if (!empty($request_quote_form)) {
                                foreach ($request_quote_form as $request_quote_form_value) {
                                    $arr_request_quote_form_title[] = $arr_request_quote_form[$request_quote_form_value];
                                }
                            }
                            if (!empty($arr_request_quote_form_title)) {
                                $array_data['request_quote_form_title'] = implode(', ', $arr_request_quote_form_title);
                            }

                        }

                        // Thời hạn bắt đầu tiếp nhận báo giá
                        $array_data['reception_date_from'] = !empty($array_data['reception_date_from']) ? nv_date($datetime_format, $array_data['reception_date_from']) : '';

                        // Thời hạn kết thúc tiếp nhận báo giá
                        $array_data['reception_date_to'] = !empty($array_data['reception_date_to']) ? nv_date($datetime_format, $array_data['reception_date_to']) : '';

                        $array_data['reception_date'] = '';
                        if (!empty($array_data['reception_date_from']) && !empty($array_data['reception_date_to'])) {
                            $array_data['reception_date'] = $lang_module['from_time'] . ' ' . $array_data['reception_date_from'] . ' ' . $lang_module['to_time'] . ' ' . $array_data['reception_date_to'];
                        }

                        // Thời hạn có hiệu lực của báo giá
                        $array_data['rq_validity_period_unit_title'] = (in_array($array_data['rq_validity_period_unit'], array_keys($rq_validity_period_unit)) ? $rq_validity_period_unit[$array_data['rq_validity_period_unit']] : '');
                        if (!empty($array_data['rq_validity_period_unit_title'])) {
                            $array_data['rq_validity_period_unit_title'] .= ' ' . $lang_module['from_date_rq'] . ' ' . $array_data['reception_date_to'];
                        }

                        $xtpl->assign('DATA', $array_data);
                        $arrMess = [
                            'vi' => sprintf(get_lang('vi', 'title_search_by_filter'), $array_data['title']),
                            'en' => sprintf(get_lang('en', 'title_search_by_filter'), $array_data['title'])
                        ];

                        $arrLink = [
                            'vi' => URL_RE . '?tb=' . $array_data['id'],
                            'en' => URL_RE . 'en/?tb=' . $array_data['id']
                        ];

                        // Thông báo Push Notification cho người dùng
                        // $arrInform['nv_theme_bidding_mail'][] = [
                        //     'vip_id' => $vip_id,
                        //     'mess' => $arrMess,
                        //     'link' => $arrLink
                        // ];
                        // insertInform($vip_id, $arrMess, $array_data['link_view']);

                        if ($array_data['investor_name'] != '') {
                            $xtpl->parse('main.filter.content_ycbg.project_owner');
                        }

                        if ($array_data['p_name'] != '') {
                            $xtpl->parse('main.filter.content_ycbg.p_name');
                        }

                        if (intval($so_ycbg[1]) > 0) {
                            $xtpl->parse('main.filter.content_ycbg.notify_old');
                        }

                        $xtpl->parse('main.filter.content_ycbg');
                    }
                }
            }
            $xtpl->parse('main.filter');
        }
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}


