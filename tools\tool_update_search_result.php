<?php

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME));
define('NV_SYSTEM', true);
require NV_ROOTDIR . '/mainfile.php';

$location = 'update_search.php';

if (!is_dir(NV_ROOTDIR . '/logs/update_search_result')) {
    mkdir(NV_ROOTDIR . '/logs/update_search_result');
}

$exception_file = NV_ROOTDIR . '/logs/update_search_result/update_search_result_' . date('Ymd') . '.log';
try {
    $waitTimeoutInSeconds = 2;
    if ($fp = fsockopen($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
        // It worked
        $elastic_online = 1;
    } else {
        // It didn't work
        $elastic_online = 0;
        echo "Server Elasticsearch didn't work: ";
        echo "ERROR: $errCode - $errStr<br />\n";
    }
    fclose($fp);

    if ($elastic_online) {
        $hosts = array(
            $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
        );

        // Kết quả lựa chọn nhà thầu.
        echo "Begin bidding_result \n";
        $params = [
            'body' => []
        ];
        $array_id = $arr_no_business_licence = [];
        $array_field = [
            'id',
            'code',
            'title',
            'open_time',
            'finish_time',
            'post_time',
            'date_approval',
            'solicitor_id',
            'type_bid_id',
            'type_choose_id',
            'type_choose',
            'type_bid',
            'price_estimate',
            'bid_price',
            'win_price_number',
            'bid_price_number',
            'no_business_licence',
            'project_name',
            'type_data',
            'get_time',
            'time_cancel',
            'reason',
            'step_cancel',
            'investor',
            'investor_id',
            'tender_price',
            'bidder_name',
            'win_price',
            'cat',
            'id_province',
            'plan_code',
            'is_aita',
            'content',
            'content_full',
            'time_open_static',
            'document_approval',
            'reason',
            'num_goods'
        ];

        $query_url = $db->query('SELECT ' . implode(',', $array_field) . ' FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result WHERE elasticsearch=11 ORDER BY elasticsearch ASC, id ASC LIMIT 500');
        while ($row = $query_url->fetch()) {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result SET elasticsearch=11 WHERE id = ' . $row['id']);

            $params['body'][] = [
                'index' => [
                    '_index' => NV_LANG_ELASTIC . 'dauthau_result',
                    '_id' => $row['id']
                ]
            ];
            $params['body'][] = $row;
            $array_id[] = $row['id'];
            $arr_no_business_licence[] = $db->quote($row['no_business_licence']);
        }

        if (!empty($params['body'])) {
            $client_result = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();

            $responses = $client_result->bulk($params)->asArray();
            if (empty($responses['errors'])) {
                $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result SET elasticsearch=' . NV_CURRENTTIME . '  WHERE id IN (' . implode(',', $array_id) . ')');
                unset($responses['items']);

                // update lại bảng nv4_businesslistings_info update_data = -1
                if (empty($arr_no_business_licence)) {
                    $db->query('UPDATE nv4_businesslistings_info SET update_data=-1 WHERE code IN (\'' . implode(',', $arr_no_business_licence) . '\')');
                }
            } else {
                file_put_contents($exception_file, "[" . date('d/m/Y H:i:s') . "]\n" . print_r($responses, true), FILE_APPEND);
            }
            echo '<pre>';
            print_r($responses);
            echo '</pre>';
        } else {
            echo 'Ko co du du lieu';
        }
    }
} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
    file_put_contents($exception_file, "[" . date('d/m/Y H:i:s') . "]\n" . print_r($e, true), FILE_APPEND);
}

$time_run = number_format((microtime(true) - NV_START_TIME), 2, '.', '');
die("xong: time_run: " . $time_run . "\n");
