<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
$waitTimeoutInSeconds = 2;
$filters_mail_tbmt_log = NV_ROOTDIR . '/aita/data/filters_mail_tbmt_log_aita_' . date('Ymd') . '.txt';

if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
    file_put_contents($filters_mail_tbmt_log, "Server Elasticsearch didn't work.\n", FILE_APPEND);
    die();
}
fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
}
/*
 * Thuật toán như sau:
 * - Bảng nv4_vi_aita_bid_logs có trường
 * - status: Trạng thái bằng 0 là đang chạy, nếu truy cập lại thì tiếp tục chạy.
 * - user: ID của users đã chạy đến, Mỗi lần tìm kiếm chỉ khoảng 2 tài khoản để test. (Sau đó có thể nâng lên 50 khi số lựong nhiều)
 * - Bảng nv4_vi_aita_bid_logs được thêm mới khi không tìm thấy status = 0 và có thêm bài đăng.
 * - Khi tìm kiếm theo từng tài khỏan, sẽ tìm lần luợt bộ lọc của từng người, nếu tìm thấy thì lưu vào bảng nv4_vi_bidding_mail để chương trình send_mail.php lấy ra để gửi.
 */

$new_loop = 0;
// Bước 1: Kiểm tra có bảng logs nào đang chạy không, nếu không thì bắt đầu chạy mới, nếu có thì tiếp tục chạy đến userid tiếp theo
$logs_info = $db->query('SELECT * FROM `nv4_vi_aita_bid_logs` WHERE status=0 ORDER BY `id` DESC limit 1')->fetch();

if (!empty($logs_info)) {
    echo "\n\nLogid: " . $logs_info['id'] . " date: " . nv_date('H:i:s d/m/Y', $logs_info['from_time']) . "\n";
    $start_id = $logs_info['from_id'];
    $end_id = $logs_info['to_id'];
} else {
    // Nếu không có phần nào đang chạy thì kiểm tra xem có tin đấu thầu mới không. Nếu có tin mới thì chạy vòng lặp mới, nếu không thì kết thúc
    $last_logs = $db->query('SELECT * FROM `nv4_vi_aita_bid_logs` ORDER BY id DESC limit 1')->fetch();

    $array_query_elastic = array();
    $array_query_elastic['size'] = 1;
    $array_query_elastic['sort'] = [
        [
            "id" => [
                "order" => "desc"
            ]
        ]
    ];
    $array_query_elastic['_source'] = array(
        'id'
    );
    $params = array();
    $params['index'] = 'dauthau_bidding';
    // $params['type'] = 'nv4_vi_bidding_row';
    $params['body'] = $array_query_elastic;
    $response = $client->search($params)->asArray();
    if ($response['hits']['total']['value'] > 0) {
        $info['to_id'] = $response['hits']['hits'][0]['_source']['id'];
    }
    if (!isset($info['to_id']) or $info['to_id'] <= 0) {
        file_put_contents($filters_mail_tbmt_log, "Lỗi maxid ES \n", FILE_APPEND);
        die('Lỗi');
    }
    if (empty($last_logs)) {
        $query = $db->query('SELECT min(`id`) FROM `nv4_vi_bidding_row` WHERE `ngay_dang_tai`>=' . $check_time_aita);
        $start_id = $query->fetchColumn(); // Chạy lần đầu
        $end_id = $info['to_id'];
        $new_loop = 1;
    } else {
        if ($last_logs['to_id'] < $info['to_id']) {
            // Nếu có tin thầu mới thì lấy ra các bài mới để chạy vòng lặp mới
            $start_id = $last_logs['to_id']; // Lấy id tin mời thầu của lần cuối cùng chạy
            $end_id = $info['to_id']; // Lấy id mời thầu lớn nhất của dữ liệu hiện có
            $new_loop = 1;
        } else {
            // Nếu không có dữ liệu mới để chạy thì dừng lại ngay
            file_put_contents($filters_mail_tbmt_log, "Lỗi select logs L71: Không có tin mới trong csdl. Hãy chạy tiếp khi có bài đăng mới\n", FILE_APPEND);
            die("Không có TBMT");
        }
    }
}

$time = time();
$start_time = $time;
if ($new_loop) {
    if ($end_id > $start_id + 5000) {
        // Nếu có quá nhiều TBMT thì chỉ tìm kiếm trong 1000 TBMT
        $end_id = $start_id + 5000;
    }
    try {
        // thêm logs mới vào csdl
        $stmt = $db->prepare('INSERT INTO nv4_vi_aita_bid_logs (from_id, to_id, from_time, to_time, total_time, status) VALUES (:from_id, :to_id, :from_time, :to_time, 0, 0)');
        $stmt->bindParam(':from_id', $start_id, PDO::PARAM_INT);
        $stmt->bindParam(':to_id', $end_id, PDO::PARAM_INT);
        $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
        $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
        $exc = $stmt->execute();
        $logs_id = $db->lastInsertId();
        $run_time = 0;
    } catch (PDOException $e) {
        file_put_contents($filters_mail_tbmt_log, "Lỗi thêm logs mới vào csdl L92: " . print_r($e, true) . "\n", FILE_APPEND);
        die('Lỗi thêm logs mới');
    }
} else {
    $logs_id = $logs_info['id'];
    $run_time = $logs_info['total_time'];
}
$arr_filter = [];
// Lấy ra ds các bộ lọc của AITA
$query_filter = $db->query('SELECT * FROM `nv4_vi_aita_filter` WHERE status=1 ORDER BY weight ASC');
while ($result = $query_filter->fetch()) {
    $arr_filter[$result['id']] = $result;
}

$new_bid_id = 0;
$date_post = '';
// Lấy ra các tin trong ds tin mới mà thỏa mãn bộ lọc tương ứng
if (empty($arr_filter)) {
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $change = $db->query('UPDATE nv4_vi_aita_bid_logs SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_tbmt_log, "Không có bộ lọc", FILE_APPEND);
    die();
} else {
    file_put_contents($filters_mail_tbmt_log, "ArrayID TBMT:" . $start_id . "-" . $end_id . ";\n", FILE_APPEND);
    $db->beginTransaction();
    try {
        foreach ($arr_filter as $filter_id => $filter) {
            file_put_contents($filters_mail_tbmt_log, "Bắt đầu quét:" . $filter_id . "; ", FILE_APPEND);
            if (!empty($filter)) {
                // Từ bộ lọc chọn ra điều kiện lọc where tương ứng
                $search_elastic = array();
                $search_elastic['must'][]['range']['id'] = [
                    "gt" => $start_id,
                    "lte" => $end_id
                ];

                $search_elastic['must'][]['range']['ngay_dang_tai'] = [
                    "gt" => $check_time_aita
                ];

                if (!empty($filter['key_search'])) {
                    $arr_key = explode(',', $filter['key_search']);
                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $search_elastic['should'][] = [
                            "match_phrase" => [
                                "goi_thau" => $key
                            ]
                        ];

                        $search_elastic['should'][] = [
                            "match_phrase" => [
                                "ten_du_an" => $key
                            ]
                        ];
                    }

                    $search_elastic['minimum_should_match'] = '1';
                    $search_elastic['boost'] = '1.0';
                }

                if (!empty($filter['key_search2'])) {
                    $arr_key = explode(',', $filter['key_search2']);
                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $search_elastic['must'][] = [
                            "match_phrase" => [
                                "goi_thau" => $key
                            ]
                        ];

                        $search_elastic['must'][] = [
                            "match_phrase" => [
                                "ten_du_an" => $key
                            ]
                        ];
                    }
                }

                if (!empty($filter['without_key'])) {
                    $arr_key = explode(',', $filter['without_key']);
                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $search_elastic['must_not'][] = [
                            "match_phrase" => [
                                "goi_thau" => $key
                            ]
                        ];

                        $search_elastic['must_not'][] = [
                            "match_phrase" => [
                                "ten_du_an" => $key
                            ]
                        ];
                    }
                }

                $array_query_elastic = array();
                if (!empty($search_elastic)) {
                    $array_query_elastic['query']['bool'] = $search_elastic;
                }
                $array_query_elastic['size'] = 1000;
                $array_query_elastic['sort'] = [
                    [
                        "ngay_dang_tai" => [
                            "order" => "desc"
                        ]
                    ]
                ];

                $array_query_elastic['_source'] = array(
                    'id',
                    'ngay_dang_tai'
                );

                $params = array();
                $params['index'] = 'dauthau_bidding';
                // $params['type'] = 'nv4_vi_bidding_row';
                $params['body'] = $array_query_elastic;
                $response = $client->search($params)->asArray();
                $time = NV_CURRENTTIME;
                foreach ($response['hits']['hits'] as $value) {
                    if (!empty($value['_source'])) {
                        $bidding = $value['_source'];

                        /*
                         * Thuật toán xử lý:
                         * - Xem TBMT có code KHLCNT
                         * - nếu không có: lấy luôn TBMT
                         * - nếu có: Kiểm tra bảng aita_plan_logs, xem hệ thống quét qua KHLCNT này chưa?
                         * _____ - Quét qua rồi: Kiểm tra xem KHLCNT đã có trong aita_plan_id chưa? Nếu không có thì bỏ qua. Nếu có thì đã tự động lấy KHLCNT rồi.
                         * _____ - Chưa quét qua: Bỏ qua, để tools lấy KHLCNT tự xử lý (mình không cần làm gì).
                         * Tóm lại: nếu có KHLCNT thì không cần xử lý, do tool KHLCNT đã xử lý
                         */

                        $_count = $db->query("SELECT COUNT(*) FROM nv4_vi_aita_bid_id WHERE bid_id=" . $bidding['id'])->fetchColumn();
                        if (empty($_count)) {
                            $date_post = $bidding['ngay_dang_tai'];

                            $khlcnt_code = $db->query("SELECT khlcnt_code FROM nv4_vi_bidding_detail WHERE id =" . $bidding['id'])->fetchColumn();
                            if (empty($khlcnt_code)) {
                                $location = 'filter_tbmt_elastic_nokhlcnt';
                            } else {
                                $location = 'filter_tbmt_elastic';
                            }

                            $stmt = $db->prepare('INSERT INTO nv4_vi_aita_bid_id (filter_id, bid_id, location, send_status, addtime) VALUES (:filter_id, :bid_id, :location, 0, :addtime)');
                            $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                            $stmt->bindParam(':bid_id', $bidding['id'], PDO::PARAM_INT);
                            $stmt->bindParam(':location', $location, PDO::PARAM_STR);
                            $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                            $exc = $stmt->execute();
                            if ($exc) {
                                ++ $new_bid_id;
                            }
                        }
                    }
                }
            }
        }
        $db->commit();
    } catch (PDOException $e) {
        file_put_contents($filters_mail_tbmt_log, "Lỗi INSERT  L401:\n" . print_r($e, true) . "\n", FILE_APPEND);
        $db->rollBack();
        die(); // Remove this line after checks finished
    }

    $time = time();
    $this_time = $time - NV_CURRENTTIME;
    $total_time = intval($run_time + $this_time);
    try {
        $db->query('UPDATE nv4_vi_aita_bid_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ', status=1, new_bid_id=new_bid_id+' . $new_bid_id . ' where id =' . $logs_id);
    } catch (PDOException $e) {
        file_put_contents($filters_mail_tbmt_log, "Lỗi update logs \n" . print_r($e, true) . "\n", FILE_APPEND);
        die();
    }
    $note = number_format($start_id) . ' -> ' . number_format($end_id) . '-> Thời gian:' . nv_date('H:i:s d/m/Y', $date_post) . ', runtime: ' . $this_time;
    file_put_contents($filters_mail_tbmt_log, $note . "\n", FILE_APPEND);
    die($note . "\n new_bid=" . $new_bid_id . "\n");
}

/**
 *
 * @param array $data
 * @return string
 */
function debugArray($data)
{
    return "<pre><code>" . print_r($data, true) . "</code></pre>";
}
