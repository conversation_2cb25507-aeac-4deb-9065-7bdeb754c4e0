<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';

$minid = 0;
if (file_exists(NV_ROOTDIR . '/tools/update_drive_msc_cu.txt')) {
    $minid = file_get_contents(NV_ROOTDIR . '/tools/update_drive_msc_cu.txt');
    $minid = intval($minid);
}
$newid = $minid;
try {
    $sql = "SELECT id, google_drive, filesize_detail FROM nv4_bidding_drive WHERE id > " . $minid . " ORDER BY id ASC LIMIT 100";
    $query = $db->query($sql);
    if ($query->rowCount() == 0) {
        echo "Đã chạy hết!!";
        exit(1);
    }
    $arr_sql = $arr_bid_id = [];
    while ($drive = $query->fetch()) {
        $newid = $drive['id'];
        $google_drive = json_decode($drive['google_drive'], true);
        $filesize_detail = json_decode($drive['filesize_detail'], true);
        foreach ($google_drive as $title => $url) {
            $arr_bid_id[] = $drive['id'];
            $arr_sql[] = '(' . $db->quote($drive['id']) . ',' . $db->quote($title) . ',' . $db->quote($url) . ',' . $db->quote($filesize_detail[$title]) . ')';
        }
    }

    if (!empty($arr_sql)) {
        $db->exec('DELETE FROM nv4_bidding_drive_tbmt WHERE bid_id IN(' . implode(',', $arr_bid_id) . ')');
        $_sql = "INSERT IGNORE INTO `nv4_bidding_drive_tbmt`(`bid_id`, `title`, `url`, `filesize`) VALUES " . implode(',', $arr_sql);
        $db->exec($_sql);

        $db->exec('UPDATE nv4_vi_bidding_detail SET sync_drive = ' . NV_CURRENTTIME . ' WHERE id IN(' . implode(',', $arr_bid_id) . ')');
        $db->exec('UPDATE nv4_bidding_drive SET sync_drive = ' . NV_CURRENTTIME . ' WHERE id IN(' . implode(',', $arr_bid_id) . ')');
    }
} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
}
if ($newid > $minid) {
    file_put_contents(NV_ROOTDIR . '/tools/update_drive_msc_cu.txt', $newid);
}
echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

