<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$date_format = ($prefix_lang == 1) ? 'm/d/Y' : 'd/m/Y';
$datetime_format = ($prefix_lang == 1) ? 'm/d/Y H:i' : 'd/m/Y H:i';
$create_mail_file = NV_ROOTDIR . '/data/create_mail_khlcntqt_' . $prefix_lang . '_' . date('Ymd') . '.txt';
if (file_exists(NV_ROOTDIR . '/data/create_mail_khlcntqt_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "khlcntqt.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit();
}
file_put_contents(NV_ROOTDIR . '/data/create_mail_khlcntqt_' . $prefix_lang . '.txt', NV_CURRENTTIME);
// Khởi tạo biến lưu thông tin thông báo
$arrInform = $list_info = [];

// Từ bảng tmp tổng hợp lại mail gửi đi cho từng VIP
// Lấy danh sách các mail chưa gửi trong csdl để tiến hành gửi đi gom theo userid
$time_start_thisday = mktime(0, 0, 0, date('n'), date('j'), date('Y'));
$vips = [];
$list_vipid = [];
$query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_planqt_id WHERE userid IN (SELECT user_id FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE ((' . NV_CURRENTTIME . ' - last_email) >= time_send*3600) AND vip=21 AND status = 1 AND prefix_lang = ' . $prefix_lang . ' ORDER BY last_email ASC) AND send_status = 0 LIMIT 10');
while ($row = $query->fetch()) {
    $vips[$row['userid']] = [];
    $list_vipid[] = $row['userid'];
}

$list_vipid_all = array();
// lấy các tài khoản mua bằng điểm, chu kỳ mail 6h
if ($config_bidding['use_reward_points']) {
    $array_id_user = array();
    $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_planqt_id WHERE userid IN (SELECT userid FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE ((' . NV_CURRENTTIME . ' - lastmail) >= (6*3600)) AND vip = 21 AND prefix_lang = ' . $prefix_lang . ' ORDER BY lastmail ASC) AND send_status = 0 LIMIT 10');
    while ($row = $query->fetch()) {
        $vips[$row['userid']] = [];
        $array_id_user[$row['userid']] = $row['userid'];
    }
    ksort($vips);
    $list_vipid_all = array_merge($list_vipid, $array_id_user);
} else {
    $list_vipid_all = $list_vipid;
}

$list_vipid_all = implode(',', $list_vipid_all);
if (!empty($list_vipid_all)) {
    $arr_vip = $db->query('SELECT id as cusid, phone, user_id as userid, email, sub_email, time_send, split_email_filters, last_email, active_user, from_time FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id IN ( ' . $list_vipid_all . ') AND vip=21 AND status = 1 AND prefix_lang = ' . $prefix_lang . '');

    while ($vip_data = $arr_vip->fetch()) {
        // nếu vip này bật chế độ gửi email theo tài khoản
        if ($vip_data['active_user'] == 1) {
            $arr_subemail = [];
            $query_permission = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_permission WHERE vipid=' . $vip_data['cusid'] . ' AND totime >' . NV_CURRENTTIME . ' AND send_mail = 1 AND prefix_lang = ' . $prefix_lang . '');
            while ($permission = $query_permission->fetch()) {
                $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid = ' . $permission['userid'] . '');
                while ($user = $arr_user->fetch()) {
                    $arr_subemail[$user['email']] = $user['email'];
                }
            }
            $vip_data['sub_email'] = implode(',', $arr_subemail);
        }

        $vips[$vip_data['userid']] = $vip_data;
        $vips[$vip_data['userid']]['filter'] = [];
        $vips[$vip_data['userid']]['plan'] = [];
        $vips[$vip_data['userid']]['is_vip'] = 1;
    }

    if ($config_bidding['use_reward_points'] and !empty($array_id_user)) {
        $arr_user = $db->query('SELECT t1.userid, t1.email, t2.id as cusid, t2.lastmail, t2.datecreate as from_time FROM nv4_users as t1 INNER JOIN ' . BID_PREFIX_GLOBAL . '_customs_points_email as t2 ON t1.userid= t2.userid WHERE t1.userid IN ( ' . implode(',', $array_id_user) . ') AND t2.prefix_lang = ' . $prefix_lang . '');
        while ($user_data = $arr_user->fetch()) {
            if (!isset($vips[$user_data['userid']]['email'])) {
                $vips[$user_data['userid']] = $user_data;
                $vips[$user_data['userid']]['filter'] = [];
                $vips[$user_data['userid']]['notify'] = '';
                $vips[$user_data['userid']]['plan'] = [];
                $vips[$user_data['userid']]['time_send'] = 6;
                $vips[$user_data['userid']]['sub_email'] = '';
                $vips[$user_data['userid']]['is_vip'] = 0;
            }
        }
    }
}

if (!empty($vips)) {
    // lấy username
    $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid IN ( ' . implode(',', array_keys($vips)) . ')');
    while ($user = $arr_user->fetch()) {
        $vips[$user['userid']]['username'] = $user['username'];
    }

    // Lấy tất cả KHLCNT trong danh sách cần gửi đi
    $arr_plan = [];

    $solicitor_id = [];

    $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_plans WHERE id IN (SELECT plan_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_planqt_id WHERE send_status = 0)');
    while ($plan = $query->fetch()) {
        $solicitor_id[$plan['solicitor_id']] = $plan['solicitor_id'];
        $arr_plan[$plan['id']] = $plan;
    }
    if (!empty($solicitor_id)) {
        $query_st = $db->query('SELECT id, title as solicitor_title FROM ' . BID_PREFIX_GLOBAL . '_solicitor WHERE id IN (' . implode(',', $solicitor_id) . ')');
        while ($solicitor = $query_st->fetch()) {
            $arr_solicitor[$solicitor['id']] = $solicitor;
        }
    }
    // Gộp nhóm những mail chung chủ đề lại theo từng VIP
    $array_plan_id = [];
    $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_planqt_id WHERE send_status = 0 AND userid IN (' . implode(',', array_keys($vips)) . ')');
    while ($bid_data = $query->fetch()) {
        if ($bid_data['filter_id'] >= 0) {
            $array_plan_id[] = $bid_data['id'];
            $_solicitor_id = $arr_plan[$bid_data['plan_id']]['solicitor_id'];
            $solicitor_title = '';
            if (!empty($arr_solicitor[$_solicitor_id])) {
                $solicitor_title = $arr_solicitor[$_solicitor_id]['solicitor_title'];
            }
            $arr_plan[$bid_data['plan_id']]['solicitor_title'] = $solicitor_title;
            $vips[$bid_data['userid']]['filter'][$bid_data['filter_id']]['list_plan'][] = $arr_plan[$bid_data['plan_id']];
            $vips[$bid_data['userid']]['plan'][] = $bid_data['plan_id'];
        }
    }

    // Lưu nội dung mail vào csdl
    foreach ($vips as $vip_id => $vip) {
        // Cập nhật lại lần cuối cùng gửi mail cho các tài khoản VIP, Phần này cần chạy trước beginTransaction để nếu có lỗi thì bỏ qua 1 tài khảo.
        if ($vip['is_vip']) {
            $stmt = $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET last_email=' . NV_CURRENTTIME . ' WHERE id=' . $vip['cusid'] . ' AND vip=21');
        } else {
            // do mua theo bộ lọc nên 1 tài khoản có nhiều row, dẫn tới cần ghi lastmail theo userid
            $stmt = $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs_points_email SET lastmail=' . NV_CURRENTTIME . ' WHERE userid =' . $vip_id . '');
        }
        $db->beginTransaction();
        file_put_contents($create_mail_file, 'BEGIN: ' . $vip_id . "\n", FILE_APPEND);

        try {
            $data_insert['addtime'] = NV_CURRENTTIME;
            $data_insert['send_time'] = 0;
            $data_insert['status'] = 0;

            // Gửi mail thông báo mới thầu, kế hoạch chọn nhà thầu, thông báo sơ tuyển nhóm theo từng bộ lọc
            if (!empty($vip['filter'])) {
                $data_insert['title'] = sprintf($lang_module['mail_title_khlcntqt'], date($date_format));
                $data_insert['type'] = 0;
                $data_insert['vip'] = 21;

                if ($vip['split_email_filters'] == 1) {
                    //Phân chia email thông báo mời thầu riêng biệt cho từng bộ lọc
                    foreach ($vip['filter'] as $filterId => $filter) {
                        $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filterId)->fetch();
                        $title = sprintf($lang_module['mail_title_khlcntqt_split'], date($date_format)) . (!empty($filterId) ? sprintf($lang_module['with_filter'], $filter_info['title']) : '');
                        $data_insert['content'] = nv_theme_bidding_mail([$filterId => $filter], $vip_id, $arrInform);
                        $data_insert['content'] .= sprintf($lang_module['mail_footer_tbmt'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date($date_format, $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');

                        // print_r($data_insert['content']);die; //Nội dung htm sẽ gửi cho từng khách
                        try {
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
                            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $title, PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (PDOException $e) {
                            print_r('Lỗi thêm mail tin KHLCNT mới vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "khlcntqt.php ERROR INSERT INTO mail: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo '<pre>';
                            print_r($e);
                            break;
                        }
                    }
                } else {
                    $data_insert['content'] = nv_theme_bidding_mail($vip['filter'], $vip_id, $arrInform);
                    $data_insert['content'] .= sprintf($lang_module['mail_footer_tbmt'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date($date_format, $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');

                    // print_r($data_insert['content']);die; //Nội dung htm sẽ gửi cho từng khách
                    try {
                        $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                        $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                        $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                        $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
                        $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                        $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                        $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                        $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                        $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                        $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                        $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                        $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                        $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                        $exc = $stmt->execute();

                        $_mailid = $db->lastInsertId();
                        file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                    } catch (PDOException $e) {
                        print_r('Lỗi thêm mail tin KHLCNT mới vào csdl');
                        file_put_contents($create_mail_file, "ERROR INSERT INTO mail: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                        notify_to_slack($create_mail_file, "khlcntqt.php ERROR INSERT INTO mail: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                        trigger_error($e->getMessage());
                        echo '<pre>';
                        print_r($e);
                        break;
                    }
                }
            }

            // Lưu tạm data thông báo
            foreach ($arrInform as $k => $v) {
                foreach ($v as $v1) {
                    $list_info[] = $v1;
                }
            }

            // Reset lại biến này về rỗng để tiến hành lưu mảng dữ liệu tiếp theo
            $arrInform = [];

            // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
            if (!empty($array_plan_id)) {
                $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_planqt_id SET send_status = 1 WHERE id IN (' . implode(',', $array_plan_id) . ') AND send_status = 0');
            }
            file_put_contents($create_mail_file, 'END: ' . $vip_id . "\n\n", FILE_APPEND);
            echo "vip_id = " . $vip_id . "\n";
            // print_r($array_plan_id);

            $db->commit();
        } catch (PDOException $e) {
            file_put_contents($create_mail_file, 'rollBack: ' . $vip_id . "\n\n", FILE_APPEND);
            $db->rollBack();
            print_r($e);
            notify_to_slack($create_mail_file, "khlcntqt.php rollBack: " . $vip_id . "; " . print_r($e, true) . "\n\n");
            break;
        }
    }
}

// Thực hiện lưu thông báo
if (!empty($list_info)) {
    echo "Có tổng " . sizeof($list_info) . ' Thông báo Inform';
    file_put_contents(NV_ROOTDIR . '/data/inform/inform_khlcntqt' . uniqid('', true) . '.txt', json_encode($list_info));
}

unlink(NV_ROOTDIR . '/data/create_mail_khlcntqt_' . $prefix_lang . '.txt');
echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function nv_theme_bidding_mail($array_filter, $vip_id, &$arrInform)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func, $prefix_lang, $date_format, $datetime_format;

    $arr_field = array(
        1 => 'Hàng hóa',
        2 => 'Xây lắp',
        3 => 'Tư vấn',
        4 => 'Phi tư vấn',
        5 => 'Hỗn hợp'
    );
    $bid_notify_status = [];
    $bid_notify_status['01'] = $lang_module['bid_notify_status_1'];
    $bid_notify_status['02'] = $lang_module['bid_notify_status_2'];
    $bid_notify_status['03'] = $lang_module['bid_notify_status_3'];
    $bid_notify_status['04'] = $lang_module['bid_notify_status_4'];
    $dm_khlcnt = [];
    $dm_khlcnt['DTPT'] = $lang_module['dm_khlcnt_1'];
    $dm_khlcnt['TX'] = $lang_module['dm_khlcnt_2'];
    $dm_khlcnt['KHAC'] = $lang_module['dm_khlcnt_3'];
    if (!empty($array_filter)) {
        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        foreach ($array_filter as $filter_id => $arr_list) {
            // Lấy tên bộ lọc
            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();
            // link sửa bộ lọc
            $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
            $from = $filter_info['time_find'] > 0 ? nv_date($date_format, NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date($date_format, NV_CURRENTTIME - 86400 * 14);
            $filter_info['link_search'] = '';
            $filter_info['link_search_plans'] = NV_MY_DOMAIN . '/' . $site_lang . '/listplan?type_info=2';
            if ($filter_info['key_search'] != '') {
                $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
            }
            if ($filter_info['key_search2'] != '') {
                $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
            }
            $filter_info['link_search'] .= '&sfrom=' . $from;
            $filter_info['link_search'] .= '&sto=' . nv_date($date_format, NV_CURRENTTIME);
            $filter_info['link_search'] .= '&is_advance=1';
            $filter_info['link_search'] .= '&userid=' . $vip_id;
            $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
            $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];
            $filter_info['link_search'] .= '&search_one_key=' . $filter_info['search_one_key'];

            if ($filter_info['cat'] > 0) {
                $filter_info['link_search'] .= '&cat=' . $filter_info['cat'];
            }
            if ($filter_info['field'] != '') {
                $array_field = explode(',', $filter_info['field']);
                foreach ($array_field as $field) {
                    $filter_info['link_search'] .= '&field[]=' . $field;
                }
            }
            if ($filter_info['without_key'] != '') {
                $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
            }
            if ($filter_info['type_org'] > 0) {
                $filter_info['link_search'] .= '&type_org=' . $filter_info['type_org'];
            }

            $filter_info['link_search_plans'] .= $filter_info['link_search'];
            $filter_info['link_search_plan'] = '';
            if ($filter_info['invest_from'] > 0) {
                $filter_info['link_search_plan'] .= '&invest_from=' . number_format($filter_info['invest_from'], 0, '.', ',');
            }
            if ($filter_info['invest_to'] > 0) {
                $filter_info['link_search_plan'] .= '&invest_to=' . number_format($filter_info['invest_to'], 0, '.', ',');
            }
            if ($filter_info['price_plan_from'] > 0) {
                $filter_info['link_search_plan'] .= '&price_plan_from=' . number_format($filter_info['price_plan_from'], 0, '.', ',');
            }
            if ($filter_info['price_plan_to'] > 0) {
                $filter_info['link_search_plan'] .= '&price_plan_to=' . number_format($filter_info['price_plan_to'], 0, '.', ',');
            }
            if ($filter_info['vip_use'] == 21) {
                $filter_info['link_search_plan'] = $filter_info['link_search_plans'] . '&id_hinhthucluachon=2';
            }
            $filter_info['link_search_plans'] = $filter_info['link_search_plans'] . $filter_info['link_search_plan'];
            $xtpl->assign('LINK_FILTER', $filter_info['link_search_plans']);

            // ghi log bộ lọc
            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);
            $xtpl->assign('FILTER_NAME', $filter_info['title']);

            if (!empty($arr_list['list_plan'])) {
                file_put_contents($create_mail_file, 'list_plan: ' . sizeof($arr_list['list_plan']) . "\n", FILE_APPEND);
                $i_break = 0;
                foreach ($arr_list['list_plan'] as $array_data) {
                    ++$i_break;
                    if ($i_break > 100) {
                        break;
                    }
                    $array_data['title_a'] = nv_htmlspecialchars($array_data['title']);
                    $array_data['type_notify'] = isset($bid_notify_status[$array_data['type_notify']]) ? $bid_notify_status[$array_data['type_notify']] : '';
                    $array_data['classify'] = isset($dm_khlcnt[$array_data['classify']]) ? $dm_khlcnt[$array_data['classify']] : '';

                    $arrMess = [
                        'vi' => sprintf(get_lang('vi', 'title_new_khlcnt_qt'), date(($prefix_lang == 1 ? 'm/d/Y' : 'd/m/Y')), $array_data['title']),
                        'en' => sprintf(get_lang('en', 'title_new_khlcnt_qt'), date(($prefix_lang == 1 ? 'm/d/Y' : 'd/m/Y')), $array_data['title']),
                    ];

                    // Bôi vàng những từ trùng với từ khóa tìm kiếm
                    if ($filter_info['search_type'] == 1) {
                        // Nếu tìm kiếm tuyệt đối
                        $arr_key = explode(',', $filter_info['key_search']);
                    } else {
                        // Nếu tìm kiếm tương đối
                        $filter_info['key_search'] = str_replace(',', ' ', $filter_info['key_search']);
                        $arr_key = explode(' ', trim($filter_info['key_search']));
                        $arr_key = array_unique($arr_key);
                    }

                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $array_data['title'] = BoldKeywordInStr(strip_tags($array_data['title']), $key);
                        $array_data['investor'] = strip_tags(BoldKeywordInStr($array_data['investor'], $key));
                    }
                    $array_data['addtime'] = $array_data['addtime'] > 0 ? nv_date(($prefix_lang == 1 ? 'H:i m/d/Y' : 'H:i d/m/Y'), $array_data['addtime']) : '';
                    $array_data['approval_date'] = nv_date($date_format, $array_data['approval_date']);
                    $khlcnt_t = getUrlByLanguage('khlcnt');
                    if ($site_lang != 'vi') {
                        $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['plans'] . '/' . $khlcnt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                    } else {
                        $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['plans'] . '/' . $khlcnt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                    }

                    $so_tbmt = explode('-', $array_data['code']);
                    $check = md5($vip_id . $so_tbmt[0]);
                    $array_data['link_follow'] = NV_MY_DOMAIN . '/' . $site_lang . '/bidding/followplans?plans_id=' . $array_data['id'] . '&plans_code=' . $so_tbmt[0] . '&vipid=' . $vip_id . '&check=' . $check;

                    $xtpl->assign('DATA', $array_data);

                    $arrLink = [
                        'vi' => URL_RE . '?kh=' .  $array_data['id'],
                        'en' => URL_RE . 'en/?kh=' .  $array_data['id']
                    ];
                    // Thông báo Push Notification cho người dùng
                    // insertInform($vip_id, $arrMess, $array_data['link_view']);
                    $arrInform['nv_theme_bidding_mail'][] = [
                        'vip_id' => $vip_id,
                        'mess' => $arrMess,
                        'link' => $arrLink
                    ];
                    $xtpl->parse('main.filter.content_plan');
                }
            }
            $xtpl->parse('main.filter');
        }
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}
