<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
set_time_limit(0);

define('NV_PREFIXLANG', 'nv4_vi');
$elastic_config = [];
$elastic_config['host'] = '************';
// $elastic_config['host'] = '**********';
$elastic_config['port'] = '9200';

$waitTimeoutInSeconds = 2;
if ($fp = fsockopen($elastic_config['host'], $elastic_config['port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
    die();
}
fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $elastic_config['host'] . ':' . $elastic_config['port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setHosts($hosts)
        ->setRetries(0)
        ->build();

    // nạp id xóa từ file
    require_once NV_ROOTDIR . '/tools/dsxoa.php';
    // xóa các file trùng
    try {
        foreach ($ids as $k => $id) {
            // Xóa trên sql
            $db->exec("DELETE FROM nv4_vi_bidding_row WHERE id = " . $id);
            $db->exec("DELETE FROM nv4_vi_bidding_detail WHERE id = " . $id);
            $db->exec("DELETE FROM `nv4_vi_bidding_follow` WHERE `bid_id` = " . $id);

            // xóa trên ES
            $params_delete = array();
            $params_delete['index'] = 'dauthau_bidding';
            $params_delete['type'] = 'nv4_vi_bidding_row';
            $params_delete['id'] = $id;
            $response = $client->delete($params_delete);
        }
    } catch (PDOException $e) {
        print_r($e);
        die($e->getMessage());
    }
}
echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '');

