#Th<PERSON> mục này dành riêng cho việc xử lý dữ liệu của site aita.dauthau.asia

Từ khóa: https://docs.google.com/spreadsheets/d/1DTboI4kCVTYIt6kQ3Ha8K4moOEMFp5P0WF5Qv4h5S58/edit#gid=0

#Xóa dữ liệu đi chạy lại từ đầu, HẾT SỨC CÂN THẬN
```
#Trên db dauthau.info
UPDATE dauthau_2018.nv4_vi_project_investment SET `is_aita`=0;
UPDATE dauthau_2018.nv4_vi_bidding_plans SET `is_aita`=0;
UPDATE dauthau_2018.nv4_vi_bidding_plans_contract SET `is_aita`=0;
UPDATE dauthau_2018.nv4_vi_bidding_result SET `is_aita`=0;
UPDATE dauthau_2018.nv4_vi_bidding_row SET `is_aita`=0;
UPDATE dauthau_2018.nv4_vi_bidding_solicitor SET `update_aita`=0;

TRUNCATE dauthau_2018.nv4_vi_aita_devproject_logs; 
TRUNCATE dauthau_2018.nv4_vi_aita_devproject_id;
TRUNCATE dauthau_2018.nv4_vi_aita_plan_logs; 
TRUNCATE dauthau_2018.nv4_vi_aita_plan_id;
TRUNCATE dauthau_2018.nv4_vi_aita_bid_id; 
TRUNCATE dauthau_2018.nv4_vi_aita_bid_logs;
TRUNCATE dauthau_2018.nv4_vi_aita_result_id; 


#Trên db aita
TRUNCATE dautucntt.nv4_vi_bidding_static;
TRUNCATE dautucntt.nv4_vi_project_investment;

TRUNCATE dautucntt.nv4_vi_bidding_plans;
TRUNCATE dautucntt.nv4_vi_bidding_plans_contract;
TRUNCATE dautucntt.nv4_vi_bidding_plans_project;

TRUNCATE dautucntt.nv4_vi_bidding_row;
TRUNCATE dautucntt.nv4_vi_bidding_detail;
TRUNCATE dautucntt.nv4_vi_bidding_goods;
TRUNCATE dautucntt.nv4_vi_bidding_row_reason;
TRUNCATE dautucntt.nv4_vi_bidding_update;

TRUNCATE dautucntt.nv4_vi_bidding_result;
TRUNCATE dautucntt.nv4_vi_bidding_result_goods;
TRUNCATE dautucntt.nv4_vi_bidding_result_business;
TRUNCATE dautucntt.nv4_vi_bidding_win;

TRUNCATE dautucntt.nv4_vi_businesslistings_addinfo;
TRUNCATE dautucntt.nv4_vi_businesslistings_info;

# Không xóa dữ liệu các bản solicitor
TRUNCATE dautucntt.nv4_vi_bidding_solicitor;
TRUNCATE dautucntt.nv4_vi_bidding_solicitor_detail;
```

Thay thế bảng nv4_vi_bidding_agency từ dauthau.info sang
Xóa CSDL Elatics của nv4_vi_bidding_result_goods
Thực hiện đồng bộ Phân mục chuyên ngành CNTT


UPDATE `nv4_vi_aita_devproject_id` SET  `send_status`=0;
UPDATE `nv4_vi_aita_plan_id` SET  `send_status`=0;
UPDATE `nv4_vi_aita_bid_id` SET  `send_status`=0;
UPDATE `nv4_vi_aita_result_id` SET  `send_status`=0;

TRUNCATE dautucntt.nv4_vi_bidding_open;
TRUNCATE dautucntt.nv4_vi_bidding_open_detail;

UPDATE `nv4_vi_aita_devproject_id` SET `send_status`=0 WHERE `send_status` < 0;
UPDATE `nv4_vi_aita_plan_id` SET `send_status`=0 WHERE `send_status` < 0;
UPDATE `nv4_vi_aita_bid_id` SET `send_status`=0, error_info='' WHERE `send_status` < 0;
UPDATE `nv4_vi_aita_result_id` SET `send_status`=0, error_info='' WHERE `send_status` < 0;
