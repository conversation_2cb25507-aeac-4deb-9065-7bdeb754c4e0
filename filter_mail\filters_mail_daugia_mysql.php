<?php
$filters_mail_log = NV_ROOTDIR . '/data/filters_mail_dau_gia_log_' . date('Ymd') . '.txt';
$new_loop = 0;
$last_row_id = array();
$logs_info = $db->query('SELECT * FROM nv4_vi_bidding_dau_gia_logs WHERE status=0 limit 1')->fetch();
if (!empty($logs_info)) {
    $last_row_id = $logs_info;
    $last_userid = $logs_info['userid'];
} else {
    // lấy id mới nhất
    $bid = $db->query('SELECT max(id_bid) as to_id FROM nv4_dau_gia_bid')->fetch();
    $bid_select = $db->query('SELECT max(id_bid) as to_id_select FROM nv4_dau_gia_bid_select')->fetch();

    $last_logs = $db->query('SELECT * FROM nv4_vi_bidding_dau_gia_logs ORDER BY id DESC limit 1')->fetch();
    if (empty($last_logs)) { // lần đầu
        $logs_info = $last_row_id;

        $last_row_id['from_id'] = 0;
        $last_row_id['from_id_select'] = 0;

        $last_row_id['to_id'] = $bid['to_id'];
        $last_row_id['to_id_select'] = $bid_select['to_id_select'];
        $last_userid = 0;
        $new_loop = 1;
    } else {
        if ($last_logs['to_id'] < $bid['to_id'] or $last_logs['to_id_select'] < $bid_select['to_id']) {
            $new_loop = 1;
            $last_userid = 0;
            $last_row_id['from_id'] = $last_logs['to_id'];
            $last_row_id['from_id_select'] = $last_logs['from_id_select'];

            $last_row_id['to_id'] = $bid['to_id'];
            $last_row_id['to_id_select'] = $bid_select['to_id_select'];
        } else {
            file_put_contents($filters_mail_log, "Không có tin mới trong csdl. Hãy chạy tiếp khi có bài đăng mới\n", FILE_APPEND);
            die('Không có tin mới');
        }
    }
}

$time = time();
$start_time = $time;
if ($new_loop) {
    // thêm logs mới vào csdl
    $stmt = $db->prepare('INSERT INTO nv4_vi_bidding_dau_gia_logs (from_id, to_id, from_id_select, to_id_select, from_time, to_time, total_time, userid, status) VALUES (:from_id, :to_id, :from_id_select, :to_id_select, :from_time, :to_time, 0, 0, 0)');
    $stmt->bindParam(':from_id', $last_row_id['from_id'], PDO::PARAM_INT);
    $stmt->bindParam(':to_id', $last_row_id['to_id'], PDO::PARAM_INT);

    $stmt->bindParam(':from_id_select', $last_row_id['from_id_select'], PDO::PARAM_INT);
    $stmt->bindParam(':to_id_select', $last_row_id['to_id_select'], PDO::PARAM_INT);

    $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
    $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
    $exc = $stmt->execute();
    $logs_id = $db->lastInsertId();
    $run_time = 0;
} else {
    $logs_id = $logs_info['id'];
    $run_time = $logs_info['total_time'];
}

// lấy vip
$number_user_scan_all = 50;
$time_start_thisday = mktime(date('H'), date('i'), date('s'), date('n'), date('j'), date('Y'));
$query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id >' . $last_userid . ' AND vip=6 ORDER BY user_id ASC limit ' . $number_user_scan_all);
$number_user_scan = 0;
$user_id_end = 0;
$arr_vip = $arr_id_vip = array();
while ($vip_info = $query_vip->fetch()) {
    ++$number_user_scan;
    $user_id_end = $vip_info['user_id'];
    $arr_vip[$vip_info['user_id']] = $vip_info;
    $arr_vip[$vip_info['user_id']]['filter'] = array();

    $arr_id_vip[$vip_info['user_id']] = $vip_info['user_id'];
}

// bộ lọc
if (!empty($arr_id_vip)) {
    $query_filter = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE userid IN (' . implode(',', $arr_id_vip) . ') AND status=1 AND vip_use=6');
    while ($result = $query_filter->fetch()) {
        $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
    }
}

// lọc tin -> chuyển bảng tmp trước khi chuyển thành mail
if (empty($arr_vip)) {
    // Nếu không lấy được VIP nào thì coi như đã chạy xong một vòng và update lại status là đã chạy xong
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $db->query('UPDATE nv4_vi_bidding_dau_gia_logs SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_log, "Đã quét xong một lượt các vip. Tổng thời gian cho một lượt:" . $total_time . "\n", FILE_APPEND);
    die();
} else {
    $db->query('UPDATE nv4_vi_bidding_dau_gia_logs SET userid =' . $user_id_end . ' WHERE id =' . $logs_id);
    $db->beginTransaction();
    try {
        foreach ($arr_vip as $vip_id => $vip) {
            echo "Bắt đầu quét:" . $vip_id . "\n";
            file_put_contents($filters_mail_log, "Bắt đầu quét:" . $vip_id . "\n", FILE_APPEND);
            if (!empty($vip['filter'])) {
                foreach ($vip['filter'] as $filter) {
                    if (!empty($filter)) {
                        $where = ' 1=1 ';
                        if (!empty($filter['key_search'])) {
                            $where .= ' AND (';
                            $num = 0;
                            $arr_key = explode(',', $filter['key_search']);
                            foreach ($arr_key as $key) {
                                $key = str_replace('-', ' ', change_alias($key));
                                $key = trim($key);
                                if ($num == 0) {
                                    $where .= "searchtext_simple LIKE " . $db->quote('%' . $key . '%');
                                } else {
                                    $where .= " OR searchtext_simple LIKE " . $db->quote('%' . $key . '%');
                                }
                                $num++;
                            }
                            $where .= ')';
                        }
                        if (!empty($filter['without_key'])) {
                            // Tìm theo từ khóa loại trừ
                            $where .= ' AND (';
                            $num = 0;
                            $arr_key = explode(',', $filter['without_key']);
                            foreach ($arr_key as $key) {
                                $key = str_replace('-', ' ', change_alias($key));
                                $key = trim($key);
                                if ($num == 0) {
                                    $where .= "searchtext_simple NOT LIKE " . $db->quote('%' . $key . '%');
                                } else {
                                    $where .= " AND searchtext_simple NOT LIKE " . $db->quote('%' . $key . '%');
                                }
                                $num++;
                            }
                            $where .= ')';
                        }

                        // Nếu chọn tìm theo tên tài sản và nơi có tài sản thì sẽ lấy địa điểm làm từ khóa phụ
                        if ($filter['par_search']) {
                            $key_search_location = [];
                            if (!empty($filter['id_province'])) {
                                $province = $db->query("SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province WHERE id=" . $db->quote($filter['id_province']))->fetch();
                                $key_search_location[] = !empty($province) ? $province['title'] : '';
                            }
                            if (!empty($filter['id_district'])) {
                                $district = $db->query("SELECT id, title FROM " . NV_PREFIXLANG . "_location_district WHERE id=" . $db->quote($filter['id_district']))->fetch();
                                $key_search_location[] = !empty($district) ? $district['title'] : '';
                            }

                            $key_search2_array = empty($filter['key_search2']) ? [] : explode(',', $filter['key_search2']);
                            $key_search2_merge = array_unique(array_merge($key_search2_array, $key_search_location));
                            $filter['key_search2'] = implode(',', $key_search2_merge);
                        }

                        if (!empty($filter['key_search2'])) {
                            // Tìm theo từ khóa cùng
                            $where .= ' AND (';
                            $num = 0;
                            $arr_key = explode(',', $filter['key_search2']);
                            $operators = !empty($filter['search_one_key']) ? ' OR ' : ' AND ';
                            foreach ($arr_key as $key) {
                                $key = str_replace('-', ' ', change_alias($key));
                                $key = trim($key);
                                if ($num == 0) {
                                    $where .= "searchtext_simple LIKE " . $db->quote('%' . $key . '%');
                                } else {
                                    $where .= $operators . "searchtext_simple LIKE " . $db->quote('%' . $key . '%');
                                }
                                $num++;
                            }
                            $where .= ')';
                        }

                        $num_row = 0;
                        if ($filter['vip_use2'] == 1) { // TBMT
                            $where .= ' AND id_bid>' . $last_row_id['from_id'] . ' AND id_bid<=' . $last_row_id['to_id'];
                            if ($filter['time_find'] > 0) {
                                $time_find = $time_start_thisday - 86400 * $filter['time_find'];
                                $where .= ' AND date_bid>=' . $time_find;
                            }

                            $_where = [];
                            if ($filter['price_from'] > 0) {
                                $_where[] = " min_bid_prices >= " . $filter['price_from'];
                            }
                            if ($filter['price_to'] > 0) {
                                $_where[] = " min_bid_prices <= " . $filter['price_to'];
                            }
                            if (!empty($_where)) {
                                $where .= ' id_bid IN (SELECT id_bid FROM nv4_dau_gia_asset WHERE ' . implode(' AND ', $_where) . ')';
                            }
                            if (!empty($filter['id_bidder'])) {
                                $where .= ' AND id_bidder=' . $filter['id_bidder'];
                            }

                            if (!empty($filter['id_province'])) {
                                $where .= ' AND id_province_owner=' . $filter['id_province'];
                            }

                            if (!empty($filter['id_district'])) {
                                $where .= ' AND id_district_owner=' . $filter['id_district'];
                            }
                            $sql = 'SELECT id_bid from nv4_dau_gia_bid WHERE ' . $where;
                            $result = $db->query($sql);
                            while ($bidding = $result->fetch()) {
                                $num_row++;
                                $stmt = $db->prepare('INSERT INTO nv4_vi_bidding_dau_gia_id (userid, filter_id, bid_id, addtime, send_status) VALUES (:userid, :filter_id, :bid_id, :addtime, 0)');
                                $time = NV_CURRENTTIME;
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':bid_id', $bidding['id_bid'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                            }
                        } else if ($filter['vip_use2'] == 2) {
                            $where .= ' AND id_bid>' . $last_row_id['from_id_select'] . ' AND id_bid<=' . $last_row_id['to_id_select'];
                            if ($filter['time_find'] > 0) {
                                $time_find = $time_start_thisday - 86400 * $filter['time_find'];
                                $where .= ' AND opening_bid>=' . $time_find;
                            }

                            $_where = [];
                            if ($filter['price_from'] > 0) {
                                $_where[] = " min_bid_prices >= " . $filter['price_from'];
                            }
                            if ($filter['price_to'] > 0) {
                                $_where[] = " min_bid_prices <= " . $filter['price_to'];
                            }
                            if (!empty($_where)) {
                                $where .= ' id_bid IN (SELECT id_bid FROM nv4_dau_gia_asset_select WHERE ' . implode(' AND ', $_where) . ')';
                            }

                            if (!empty($filter['id_province'])) {
                                $where .= ' AND id_province_owner=' . $filter['id_province'];
                            }

                            if (!empty($filter['id_district'])) {
                                $where .= ' AND id_district_owner=' . $filter['id_district'];
                            }
                            $sql = 'SELECT id_bid from nv4_dau_gia_bid_select WHERE ' . $where;
                            $result = $db->query($sql);
                            while ($bidding = $result->fetch()) {
                                $num_row++;
                                $stmt = $db->prepare('INSERT INTO nv4_vi_bidding_dau_gia_id (userid, filter_id, select_id, addtime, send_status) VALUES (:userid, :filter_id, :select_id, :addtime, 0)');
                                $time = NV_CURRENTTIME;
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':select_id', $bidding['id_bid'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                            }
                        }

                        // ghi log bộ lọc
                        if ($num_row > 0) {
                            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastresult = ' . NV_CURRENTTIME . ', stat_totalresult = stat_totalresult + ' . $num_row . ' WHERE id=' . $filter['id']);
                        }
                    }
                }
            }
            // ghi lại logs cho quá trình tìm kiếm tin cho mỗi vip
            $time = NV_CURRENTTIME;
            $stmt = $db->prepare('UPDATE nv4_vi_bidding_dau_gia_logs SET to_time = :to_time, userid = :userid WHERE id =' . $logs_id);
            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
            $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
            $exc = $stmt->execute();
        }
        $db->commit();
    } catch (PDOException $e) {
        print_r($e);
        file_put_contents($filters_mail_log, "Lỗi INSERT:\n" . print_r($e, true) . "\n", FILE_APPEND);
        notify_to_slack($filters_mail_log, "filters_mail_daugia_mysql.php Lỗi INSERT:\n" . print_r($e, true) . "\n");
        $db->rollBack();
        die(); // Remove this line after checks finished
    }

    // Sau khi chạy xong một vòng 50 VIP thì thông báo chạy xong và thời gian chạy cho một vòng
    $time = time();
    $this_time = $time - NV_CURRENTTIME;
    $total_time = intval($run_time + $this_time);
    try {
        if ($number_user_scan < $number_user_scan_all) {
            $db->query('UPDATE nv4_vi_bidding_dau_gia_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ', status=1 where id =' . $logs_id);
        } else {
            $db->query('UPDATE nv4_vi_bidding_dau_gia_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ' where id =' . $logs_id);
        }
        if (sizeof($arr_id_vip)) {
            $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET filters_mail = ' . NV_CURRENTTIME . ' WHERE user_id IN (' . implode(',', $arr_id_vip) . ') AND vip=6');
        }
    } catch (PDOException $e) {
        file_put_contents($filters_mail_log, "Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP :\n" . print_r($e, true) . "\n", FILE_APPEND);
        notify_to_slack($filters_mail_log, "filters_mail_daugia_mysql.php Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP :\n" . print_r($e, true) . "\n");
        die();
    }
    $note = 'Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
    file_put_contents($filters_mail_log, $note . "\n\n", FILE_APPEND);
    die($note . "\n");
}

