<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$params = [];
$sql = "SELECT id, code, title FROM `" . NV_PREFIXLANG . "_bidding_result` WHERE alias='' ORDER BY id ASC LIMIT 500";
$_query = $db->query($sql);
$array_alias = [];
$_last_id = 0;
while ($row = $_query->fetch()) {
    $_num = ($row['id'] > 1378000) ? 110 : 200; // Số này cố định khi chạy chính thức
    $alias = nv_clean_alias($row['title'], $_num);
    if (empty($alias)) {
        $alias = nv_clean_alias($row['code'], $_num);
    }
    $params['body'][] = [
        'update' => [
            '_index' => NV_LANG_ELASTIC . 'dauthau_result',
            '_id' => $row['id']
        ]
    ];
    $params['body'][] = [
        'doc' => [
            'alias' => $alias
        ]
    ];
    $array_alias[$row['id']] = $alias;
}
$_query->closeCursor();

if (!empty($params['body'])) {
    print_r($params);
    $hosts = array(
        $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
    );

    $client = Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
    $responses = $client->bulk($params);
    if (!empty($responses['items'])) {
        foreach ($responses['items'] as $value) {
            if ($value['update']['status'] == 200) {
                $_id = $value['update']['_id'];
                $exec = $db->exec("UPDATE " . NV_PREFIXLANG . "_bidding_result SET alias = " . $db->quote($array_alias[$_id]) . " WHERE id=" . $_id);
            }
        }
    }
}

die("Thực hiện xong");

function nv_clean_alias($string, $num = 110, $lower = true)
{
    $search = [
        '&amp;',
        '&#039;',
        '&quot;',
        '&lt;',
        '&gt;',
        '&#x005C;',
        '&#x002F;',
        '&#40;',
        '&#41;',
        '&#42;',
        '&#91;',
        '&#93;',
        '&#33;',
        '&#x3D;',
        '&#x23;',
        '&#x25;',
        '&#x5E;',
        '&#x3A;',
        '&#x7B;',
        '&#x7D;',
        '&#x60;',
        '&#x7E;'
    ];
    $replace = [
        '&',
        '\'',
        '"',
        '<',
        '>',
        '\\',
        '/',
        '(',
        ')',
        '*',
        '[',
        ']',
        '!',
        '=',
        '#',
        '%',
        '^',
        ':',
        '{',
        '}',
        '`',
        '~'
    ];

    $string = str_replace($search, $replace, $string);
    $string = nv_compound_unicode($string);
    $string = nv_EncString($string);
    $string = preg_replace(array(
        '/[^a-zA-Z0-9]/',
        '/[ ]+/',
        '/^[\-]+/',
        '/[\-]+$/'
    ), array(
        ' ',
        '-',
        '',
        ''
    ), $string);

    $len = strlen($string);
    if ($num and $len > $num) {
        $_substring = substr($string, 0, $num);
        while (str_contains($_substring, '-') and substr($string, $num, 1) != '-') {
            --$num;
            $_substring = substr($_substring, 0, $num);
        }
        $string = substr($string, 0, $num);
    }

    if ($lower) {
        $string = strtolower($string);
    }
    return $string;
}

die('đã thực hiện xong');