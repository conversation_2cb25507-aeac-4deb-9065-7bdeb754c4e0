<?php
global $arr_filter;
$query_filter = $db->query('SELECT * FROM `nv4_vi_aita_filter` WHERE status=1 ORDER BY weight ASC');
while ($result = $query_filter->fetch()) {
    $arr_filter[$result['phanmuc']][$result['id']] = $result;
}

// hàm xác định hàng hóa thuộc chuyên ngành, phân mục CNTT nào
function goods_detail($_name)
{
    global $arr_filter;
    $goods = [];
    if (!empty($arr_filter)) {
        if ($_name != '') {
            $_name = preg_replace('/(\r\n|\n|\r|<\/br>|<br>)/', ' ', $_name);
            $_name = mb_strtolower($_name, "utf8");
            $searchtext_full = $_name;
            $searchtext_simple = nv_EncString($searchtext_full);

            foreach ($arr_filter as $phanmuc => $_array_filler) {
                foreach ($_array_filler as $_filter) {
                    $break = false;
                    if (!empty($_filter['without_key'])) {
                        // Tìm theo từ khóa loại trừ
                        $arr_key = explode(',', $_filter['without_key']);
                        foreach ($arr_key as $key) {
                            if ($key != '') {
                                if ($_filter['search_type'] == 1) {
                                    $key = trim($key);
                                    if (strpos($searchtext_full, $key) !== false) {
                                        $break = true;
                                        continue;
                                    }
                                } else {
                                    $key = str_replace('-', ' ', change_alias($key));
                                    $key = trim($key);
                                    if (stripos($searchtext_simple, $key) !== false) {
                                        $break = true;
                                        continue;
                                    }
                                }
                            }
                        }
                    }
                    if ($break === true) {
                        // Nếu có từ khoá loại trừ thì loại luôn, không kiểm tra nữa
                        continue;
                    }

                    if (!empty($_filter['key_search2'])) {
                        // Tìm theo từ khóa cùng
                        $arr_key = explode(',', $_filter['key_search2']);
                        foreach ($arr_key as $key) {
                            if ($key != '') {
                                if ($_filter['search_type'] == 1) {
                                    $key = trim($key);
                                    if (strpos($searchtext_full, $key) === false) {
                                        $break = true;
                                        continue;
                                    }
                                } else {
                                    $key = str_replace('-', ' ', change_alias($key));
                                    $key = trim($key);
                                    if (stripos($searchtext_simple, $key) === false) {
                                        $break = true;
                                        continue;
                                    }
                                }
                            }
                        }
                    }

                    if ($break === true) {
                        // Nếu không có từ khoá cùng thì loại luôn, không kiểm tra nữa
                        continue;
                    }

                    if (!empty($_filter['key_search'])) {
                        // Tìm theo từ khóa chính
                        $arr_key = explode(',', $_filter['key_search']);
                        foreach ($arr_key as $key) {
                            if ($key != '') {
                                if ($_filter['search_type'] == 1) {
                                    $key = trim($key);
                                    if (strpos($searchtext_full, $key) !== false) {
                                        $goods[$phanmuc] = $phanmuc;
                                    }
                                } else {
                                    $key = str_replace('-', ' ', change_alias($key));
                                    $key = trim($key);
                                    if (stripos($searchtext_simple, $key) !== false) {
                                        $goods[$phanmuc] = $phanmuc;
                                    }
                                }
                            }
                        }
                    }
                }

                if (!empty($goods)) {
                    continue;
                }
            }
        }
    }
    return $goods;
}

function get_solicitor_id($ben_moi_thau, $investorCode = '')
{
    global $db, $dbcr;

    $matches = [];
    $id_solicitor = 0;
    if (!empty($investorCode)) {
        $id_solicitor = $db->query('SELECT id FROM `nv4_bidding_solicitor` WHERE `org_code` = ' . $db->quote($investorCode))
            ->fetchColumn();
        if (empty($id_solicitor)) {

            if (preg_match('/([0-9]{10})/', $investorCode, $m)) {
                if (!empty($m[0])) {
                    $id_solicitor = $db->query('SELECT id FROM `nv4_bidding_solicitor_detail` WHERE `no_business_licence` = ' . $db->quote($m[0]))
                        ->fetchColumn();
                    if (empty($id_solicitor)) {
                        $id_solicitor = 0;
                    }
                }
            }
        }
    } else if (preg_match('/^\s*([z,Z,1,0]{1}[0-9]{6})\s*\-\s*(.+)\s*$/', $ben_moi_thau, $matches)) {
        // Trường hợp có mã nhà thầu
        $title_solicitor = trim_space($matches[2]);
        $code_solicitor = $matches[1];
        $alias = change_alias(trim_space($title_solicitor));
        $alias = strtolower($alias);
        $note = '';
        $id_solicitor = $db->query('SELECT id FROM `nv4_bidding_solicitor` WHERE `solicitor_code` = ' . $db->quote($code_solicitor))
            ->fetchColumn();
        if (empty($id_solicitor)) {
            $id_solicitor = $db->query('SELECT id FROM `nv4_bidding_solicitor` WHERE `title` = ' . $db->quote($title_solicitor))
                ->fetchColumn();
            if (empty($id_solicitor)) {
                $id_solicitor = $db->query('SELECT id FROM `nv4_bidding_solicitor` WHERE `alias` = ' . $db->quote($alias))
                    ->fetchColumn();
                if (empty($id_solicitor)) {
                    // sửa từ = thành like do 1 số bmt bị insert alias có dạng alias-2
                    $arr_solicitor = $db->query('SELECT id FROM `nv4_bidding_solicitor` WHERE `alias` LIKE ' . $db->quote('%' . $alias . '%'))
                        ->fetchAll();
                    if (sizeof($arr_solicitor) == 1) {
                        $id_solicitor = $arr_solicitor[0]['id'];
                    }
                }
            }
        }
    } else {
        // trường hợp k có mã ở đầu tên bên mời thầu
        $id_solicitor = $db->query('SELECT id FROM `nv4_bidding_solicitor` WHERE `title` = ' . $db->quote($ben_moi_thau))
            ->fetchColumn();
        if (empty($id_solicitor)) {
            $alias = change_alias(trim_space($ben_moi_thau));
            $alias = strtolower($alias);
            $id_solicitor = $db->query('SELECT id FROM `nv4_bidding_solicitor` WHERE `alias` = ' . $db->quote($alias))
                ->fetchColumn();
            if (empty($id_solicitor)) {
                // sửa từ = thành like do 1 số bmt bị insert alias có dạng alias-2
                $arr_solicitor = $db->query('SELECT id FROM `nv4_bidding_solicitor` WHERE `alias` LIKE ' . $db->quote('%' . $alias . '%'))
                    ->fetchAll();
                if (sizeof($arr_solicitor) == 1) {
                    $id_solicitor = $arr_solicitor[0]['id'];
                }
            }
        }
    }
    return $id_solicitor;
}

function trim_space($text)
{
    $text = preg_replace('/^[\pZ\pC]+|[\pZ\pC]+$/u', '', $text);
    return $text;
}
