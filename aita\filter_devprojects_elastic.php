<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36 5,574 4,521
 */
$waitTimeoutInSeconds = 2;
$filters_mail_devproject_log = NV_ROOTDIR . '/aita/data/filters_mail_devprojects_log_' . date('Ymd') . '.txt';

if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
    file_put_contents($filters_mail_devproject_log, "Server Elasticsearch didn't work.\n", FILE_APPEND);
    die();
}

fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
}

$new_loop = 0;
// Bước 1: Kiểm tra có bảng logs nào đang chạy không, nếu không thì bắt đầu chạy mới, nếu có thì tiếp tục chạy đến userid tiếp theo
$logs_info = $db->query('SELECT * FROM `nv4_vi_aita_devproject_logs` WHERE status=0 ORDER BY `id` DESC limit 1')->fetch();

if (!empty($logs_info)) {
    echo "\n\nLogid: " . $logs_info['id'] . " date: " . nv_date('H:i:s d/m/Y', $logs_info['from_time']) . "\n";
    $start_devproject_id = $logs_info['from_id'];
    $end_devproject_id = $logs_info['to_id'];
} else {
    // Nếu không có phần nào đang chạy thì kiểm tra xem có tin đấu thầu mới không. Nếu có tin mới thì chạy vòng lặp mới, nếu không thì kết thúc
    $last_logs = $db->query('SELECT * FROM `nv4_vi_aita_devproject_logs` ORDER BY id DESC limit 1')->fetch();

    $array_query_elastic = array();
    $array_query_elastic['size'] = 1;
    $array_query_elastic['sort'] = [
        [
            "id" => [
                "order" => "desc"
            ]
        ]
    ];
    $array_query_elastic['_source'] = array(
        'id'
    );
    $params = array();
    $params['index'] = 'dauthau_project_investment';

    $params['body'] = $array_query_elastic;
    $response = $client->search($params)->asArray();
    if ($response['hits']['total']['value'] > 0) {
        $info_devproject['to_id'] = $response['hits']['hits'][0]['_source']['id'];
    }
    if (!isset($info_devproject['to_id']) or $info_devproject['to_id'] <= 0) {
        file_put_contents($filters_mail_devproject_log, "Lỗi maxid ES \n", FILE_APPEND);
        die('Lỗi');
    }
    if (empty($last_logs)) {
        $query = $db->query('SELECT min(`id`) FROM `nv4_vi_project_investment` WHERE `date_post`>=' . $check_time_aita);
        $start_devproject_id = $query->fetchColumn(); // Chạy lần đầu: 2022: 2.403.307 2021: 195.213
        $end_devproject_id = $info_devproject['to_id'];
        $new_loop = 1;
    } else {
        if ($last_logs['to_id'] < $info_devproject['to_id']) {
            // Nếu có kế hoạch chọn nhà thầu mới thì lấy ra các bài mới để chạy vòng lặp mới
            $start_devproject_id = $last_logs['to_id'];
            $end_devproject_id = $info_devproject['to_id'];
            $new_loop = 1;
        } else {
            file_put_contents($filters_mail_devproject_log, "Không có tin mới trong csdl. Hãy chạy tiếp khi có kế hoạch Dự án đăng mới\n\n", FILE_APPEND);
            // Nếu không có dữ liệu mới để chạy thì dừng lại ngay
            die("Không Dự án mới\n");
        }
    }
}

$time = time();
$start_time = $time;
if ($new_loop) {
    if ($end_devproject_id > $start_devproject_id + 10000) {
        // Nếu có quá nhiều TBMT thì chỉ tìm kiếm trong 1000 TBMT
        $end_devproject_id = $start_devproject_id + 10000;
    }

    if ($start_devproject_id == $end_devproject_id) {
        die('No Data new');
    }

    try {
        // thêm logs mới vào csdl
        $stmt = $db->prepare('INSERT INTO nv4_vi_aita_devproject_logs (from_id, to_id, from_time, to_time, total_time, status) VALUES ( :from_id, :to_id, :from_time, :to_time, 0, 0)');
        $stmt->bindParam(':from_id', $start_devproject_id, PDO::PARAM_INT);
        $stmt->bindParam(':to_id', $end_devproject_id, PDO::PARAM_INT);
        $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
        $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
        $exc = $stmt->execute();
        $logs_id = $db->lastInsertId();
        $run_time = 0;
    } catch (PDOException $e) {
        file_put_contents($filters_mail_devproject_log, "Lỗi thêm logs mới vào csdl L94\n", FILE_APPEND);
        die($e->getMessage()); // Remove this line after checks finished
    }
} else {
    $logs_id = $logs_info['id'];
    $run_time = $logs_info['total_time'];
}
$arr_filter = [];
// Lấy ra ds các bộ lọc của AITA
$query_filter = $db->query('SELECT * FROM `nv4_vi_aita_filter` WHERE status=1 ORDER BY weight ASC');
while ($result = $query_filter->fetch()) {
    $arr_filter[$result['id']] = $result;
}

$new_devproject_number = 0;
$date_post = 0;
if (empty($arr_filter)) {
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $change = $db->query('UPDATE nv4_vi_aita_devproject_logs SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_devproject_log, "Không có bộ lọc", FILE_APPEND);
    die();
} else {
    file_put_contents($filters_mail_devproject_log, "ArrayID devprojects:" . $start_devproject_id . "-" . $end_devproject_id . ";\n", FILE_APPEND);
    $db->beginTransaction();
    try {
        foreach ($arr_filter as $filter_id => $filter) {
            file_put_contents($filters_mail_devproject_log, "Bắt đầu quét:" . $filter_id . ";", FILE_APPEND);
            if (!empty($filter)) {
                // Từ bộ lọc chọn ra điều kiện lọc where tương ứng
                $search_elastic = array();
                $search_elastic['must'][]['range']['id'] = [
                    "gt" => $start_devproject_id,
                    "lte" => $end_devproject_id
                ];

                $search_elastic['must'][]['range']['date_post'] = [
                    "gt" => $check_time_aita
                ];

                if (!empty($filter['key_search'])) {
                    $arr_key = explode(',', $filter['key_search']);
                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $search_elastic['should'][] = [
                            "match_phrase" => [
                                "name" => $key
                            ]
                        ];
                    }

                    $search_elastic['minimum_should_match'] = '1';
                    $search_elastic['boost'] = '1.0';
                }

                if (!empty($filter['key_search2'])) {
                    $arr_key = explode(',', $filter['key_search2']);
                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $search_elastic['must'][] = [
                            "match_phrase" => [
                                "name" => $key
                            ]
                        ];
                    }
                }

                if (!empty($filter['without_key'])) {
                    $arr_key = explode(',', $filter['without_key']);
                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $search_elastic['must_not'][] = [
                            "match_phrase" => [
                                "name" => $key
                            ]
                        ];
                    }
                }

                $array_query_elastic = array();
                if (!empty($search_elastic)) {
                    $array_query_elastic['query']['bool'] = $search_elastic;
                }
                $array_query_elastic['size'] = 1000;
                $array_query_elastic['sort'] = [
                    [
                        "date_post" => [
                            "order" => "desc"
                        ]
                    ]
                ];

                $params = array();
                $params['index'] = 'dauthau_project_investment';
                $params['body'] = $array_query_elastic;
                $response = $client->search($params)->asArray();
                // pr($array_query_elastic);
                foreach ($response['hits']['hits'] as $value) {
                    if (!empty($value['_source'])) {
                        $devproject = $value['_source'];
                        if ($devproject['date_post'] > $check_time_aita) { // Chỉ lấy các dữ liệu có ngày đăng tải lớn hơn quy định tại file config
                            $date_post = $devproject['date_post']; // Nếu tìm ra tin thầu thỏa mãn điều kiện thì thêm bảng nv4_vi_aita_devproject_id
                            $location = 'filter_devprojects_elastic.php';
                            $exc = $db->exec('INSERT INTO `nv4_vi_aita_devproject_id`(filter_id, devproject_id, location, send_status, addtime) VALUES (' . $filter['id'] . ', ' . $devproject['id'] . ', ' . $db->quote($location) . ' , 0, ' . NV_CURRENTTIME . ')');
                            if ($exc) {
                                ++ $new_devproject_number;
                            }
                        }
                    }
                }
            }
        }
        $db->commit();
    } catch (PDOException $e) {
        file_put_contents($filters_mail_devproject_log, "Lỗi INSERT  L401:\n" . print_r($e, true) . "\n", FILE_APPEND);
        $db->rollBack();
        die(); // Remove this line after checks finished
    }

    $time = time();
    $this_time = $time - $start_time;
    $total_time = $run_time + $this_time;
    try {
        $db->query('UPDATE nv4_vi_aita_devproject_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ', status=1, new_devproject_id=new_devproject_id+' . $new_devproject_number . ' where id =' . $logs_id);
    } catch (PDOException $e) {
        file_put_contents($filters_mail_devproject_log, "Lỗi update logs \n" . print_r($e, true) . "\n", FILE_APPEND);
        die();
    }
    $note = number_format($start_devproject_id) . ' -> ' . number_format($end_devproject_id) . '-> Thời gian:' . nv_date('H:i:s d/m/Y', $date_post) . ', runtime: ' . $this_time;
    file_put_contents($filters_mail_devproject_log, $note . "\n", FILE_APPEND);
    die($note . "\n new_project=" . $new_devproject_number . "\n");
}

/**
 *
 * @param array $data
 * @return string
 */
function debugArray($data)
{
    return "<pre><code>" . print_r($data, true) . "</code></pre>";
}
