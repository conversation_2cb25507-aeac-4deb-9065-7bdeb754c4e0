<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/aita/config_aita.php';

$array_plan_id = $array_filter_id = [];
$query = $db->query('SELECT plan_id, filter_id FROM nv4_vi_aita_plan_id WHERE send_status=0 ORDER BY `id` ASC LIMIT 100'); // send_status=0
while ($_plan = $query->fetch()) {
    $array_plan_id[] = $_plan['plan_id'];
    $array_filter_id[$_plan['plan_id']] = $_plan['filter_id'];
}

$location = 'push_khlcnt.php';
if (!empty($array_plan_id)) {
    $arr_filter = [];
    $db->query('UPDATE nv4_vi_aita_plan_id SET send_status=-9 WHERE plan_id IN (' . implode(',', $array_plan_id) . ') AND send_status=0');

    $query_filter = $db->query('SELECT * FROM `nv4_vi_aita_filter` WHERE status=1 ORDER BY weight ASC');
    while ($result = $query_filter->fetch()) {
        if (in_array($result['id'], $array_filter_id)) {
            $arr_filter[$result['id']] = $result;
        }
        $arr_filter_total[$result['id']] = $result;
    }

    $sql = 'SELECT * FROM nv4_vi_bidding_plans WHERE id IN (' . implode(',', $array_plan_id) . ')';
    $query = $db->query($sql);
    while ($bidding_plans = $query->fetch()) {
        $id = $bidding_plans['id'];
        echo "plan id = " . $id . "\n";

        if ($bidding_plans['is_aita'] == -1) {
            // Bỏ qua các dữ liệu đánh đấu bằng -1
            continue;
        }

        $filter_id = $array_filter_id[$id];
        $keyword = $arr_phanmuc = [];
        // xử lý xem từ khóa nào khớp
        if (isset($arr_filter[$filter_id])) {
            $filter = $arr_filter[$filter_id];
            $arr_phanmuc[$filter['phanmuc']] = $filter['phanmuc'];
            if (!empty($filter['key_search'])) {
                $arr_key = explode(',', $filter['key_search']);
                foreach ($arr_key as $key) {
                    $key = trim($key);
                    if (preg_match('/^(.*?)' . nv_preg_quote($key) . '/uis', $bidding_plans['content_full'], $matches)) {
                        $keyword[] = $key;
                    }
                }
            }
        }
        $keyword = implode(', ', $keyword);

        $request = [
            // Tham số bắt buộc
            'apikey' => $apikey,
            'language' => 'vi',
            'module' => 'bidding',
            'action' => 'KHLCNT',
            'id' => $id,
            'filter_id' => $filter_id,
            'keyword' => $keyword
        ];

        if ($bidding_plans['solicitor_id'] > 0) {
            $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $bidding_plans['solicitor_id'];
            $request['bidding_solicitor'] = $db->query($sql)->fetch();

            $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $bidding_plans['solicitor_id'];
            $request['bidding_solicitor_detail'] = $db->query($sql)->fetch();
            $db->query('UPDATE nv4_bidding_solicitor SET update_aita=' . time() . ' WHERE id=' . $bidding_plans['solicitor_id']);
        }

        // tìm thêm theo tên chủ đầu tư
        $solicitor_id = get_solicitor_id($bidding_plans['investor']);
        if ($solicitor_id > 0) {
            if ($bidding_plans['solicitor_id'] == 0) {
                $bidding_plans['solicitor_id'] = $solicitor_id;
                $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $solicitor_id;
                $request['bidding_solicitor'] = $db->query($sql)->fetch();

                $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $solicitor_id;
                $request['bidding_solicitor_detail'] = $db->query($sql)->fetch();
            } else if ($bidding_plans['solicitor_id'] != $solicitor_id) {
                $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $solicitor_id;
                $request['bidding_investor'] = $db->query($sql)->fetch();

                $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $solicitor_id;
                $request['bidding_investor_detail'] = $db->query($sql)->fetch();
            }
            $db->query('UPDATE nv4_bidding_solicitor SET update_aita=' . time() . ' WHERE id=' . $solicitor_id);
        }

        $_bidding_plans_contract = [];
        $sql = 'SELECT * FROM nv4_vi_bidding_plans_contract WHERE id_plan=' . $id;
        $query_plans_contract = $db->query($sql);
        $array_tbmt_id = [];
        while ($plans_contract = $query_plans_contract->fetch()) {
            // Chỗ này kiểm tra từng gói thầu xem có từ khoá CNTT không? Có thì cho is_aita = 1
            $searchtext_full = $plans_contract['title'];
            $searchtext_full = mb_strtolower($searchtext_full, "utf8");
            $plans_contract['is_aita'] = 0;
            foreach ($arr_filter_total as $_filter) {
                $break = false;
                if (!empty($_filter['without_key'])) {
                    // Tìm theo từ khóa loại trừ
                    $arr_key = explode(',', $_filter['without_key']);
                    foreach ($arr_key as $key) {
                        if ($key != '') {
                            $key = trim($key);
                            if (strpos($searchtext_full, $key) !== false) {
                                $break = true;
                                continue;
                            }
                        }
                    }
                }
                if ($break === true) {
                    // Nếu có từ khoá loại trừ thì loại luôn, không kiểm tra nữa
                    continue;
                }

                if (!empty($_filter['key_search2'])) {
                    // Tìm theo từ khóa cùng
                    $arr_key = explode(',', $_filter['key_search2']);
                    foreach ($arr_key as $key) {
                        if ($key != '') {
                            $key = trim($key);
                            if (strpos($searchtext_full, $key) === false) {
                                $break = true;
                                continue;
                            }
                        }
                    }
                }

                if ($break === true) {
                    // Nếu không có từ khoá cùng thì loại luôn, không kiểm tra nữa
                    continue;
                }

                if (!empty($_filter['key_search'])) {
                    // Tìm theo từ khóa chính
                    $arr_key = explode(',', $_filter['key_search']);
                    foreach ($arr_key as $key) {
                        if ($key != '') {
                            $key = trim($key);
                            if (strpos($searchtext_full, $key) !== false) {
                                $plans_contract['is_aita'] = 1;
                                $arr_phanmuc[$_filter['phanmuc']] = $_filter['phanmuc'];
                            }
                        }
                    }
                }
            }
            // echo 'is_aita ' . $plans_contract['id'] . ": " . $plans_contract['is_aita'] . "\n";
            $db->query('UPDATE nv4_vi_bidding_plans_contract SET is_aita = ' . $plans_contract['is_aita'] . ' WHERE id =' . $plans_contract['id']);
            $_bidding_plans_contract[] = $plans_contract;
            if ($plans_contract['is_aita']) {
                $array_tbmt_id[$plans_contract['tbmt_id']] = $plans_contract['tbmt_id'];
            }
        }
        $query_plans_contract->closeCursor();
        $request['bidding_plans_contract'] = json_encode($_bidding_plans_contract);
        $request['phanmuc'] = implode(',', $arr_phanmuc);

        $timestamp = time();
        $request['hashsecret'] = password_hash($apisecret . '_' . $timestamp, PASSWORD_DEFAULT);
        $request['timestamp'] = $timestamp;
        $request['bidding_plans'] = json_encode($bidding_plans);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_remote_url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        $safe_mode = (ini_get('safe_mode') == '1' || strtolower(ini_get('safe_mode')) == 'on') ? 1 : 0;
        $open_basedir = ini_get('open_basedir') ? true : false;
        if (!$safe_mode and !$open_basedir) {
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);

        curl_setopt($ch, CURLOPT_POST, sizeof($request));
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($request));
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);

        $res = curl_exec($ch);
        curl_close($ch);

        $responsive = json_decode($res, true);
        print_r($responsive);

        if (isset($responsive['status']) and $responsive['status'] == 'success') {
            $location = 'push_khlcnt id=' . $id;
            $db->query('UPDATE nv4_vi_bidding_plans SET is_aita = 1 WHERE id =' . $id);
            $db->query('UPDATE nv4_vi_aita_plan_id SET send_status=' . $timestamp . ' WHERE plan_id=' . $id . ' AND send_status<=0');
            // Kiểm tra xem đã có dự án chưa, có thì thêm vào hàng chờ sang aita
            if ($bidding_plans['id_project'] > 0) {
                $_count = $db->query("SELECT COUNT(*) FROM nv4_vi_aita_devproject_id WHERE devproject_id=" . $bidding_plans['id_project'])->fetchColumn();
                if (empty($_count)) {
                    $exc = $db->exec('INSERT INTO `nv4_vi_aita_devproject_id`(filter_id, devproject_id, location, send_status, addtime) VALUES (0, ' . $bidding_plans['id_project'] . ', ' . $db->quote($location) . ' , 0, ' . NV_CURRENTTIME . ')');
                }
            }

            // Kiểm tra xem đã có TBMT chưa, nếu chưa có cần bổ sung sang aita
            $array_bid_id = [];
            $_code = explode('-', $bidding_plans['code']);
            $result = $db->query("SELECT id FROM nv4_vi_bidding_detail WHERE khlcnt_code=" . $db->quote($_code[0]));
            while ($bidding = $result->fetch()) {
                $array_bid_id[] = $bidding['id'];
            }
            $result->closeCursor();

            $array_bid_exit = [];
            if (!empty($array_bid_id)) {
                $_query = $db->query("SELECT DISTINCT bid_id FROM nv4_vi_aita_bid_id WHERE bid_id IN (" . implode(',', $array_bid_id) . ")");
                while ($_r = $_query->fetch()) {
                    $array_bid_exit[] = $_r['bid_id'];
                }
            }
            foreach ($array_bid_id as $bid_id) {
                if (!in_array($bid_id, $array_bid_exit)) {
                    $db->exec('INSERT INTO `nv4_vi_aita_bid_id`(filter_id, bid_id, location, send_status, addtime) VALUES (0, ' . $bid_id . ', ' . $db->quote($location) . ' , 0, ' . NV_CURRENTTIME . ')');
                }
            }

            // Lấy tất cả code TBMT và ID của KQLCNT thuộc KHLCNT này
            $array_code_tbmt = $array_kqlcnt_id = [];
            $query_rs = $db->query("SELECT code, id FROM nv4_vi_bidding_result WHERE plan_code=" . $db->quote($_code[0]));
            while ($_result = $query_rs->fetch()) {
                $array_code_tbmt[$_result['code']] = $db->quote($_result['code']); // Mảng chứa code TBMT để kiểm tra xem TBMT có thật không
                $array_kqlcnt_id[$_result['code']] = $_result['id']; // Mảng chứa id KQLCNT
            }
            $query_rs->closeCursor();

            // Có KQLCNT thì tiếp tục
            if (!empty($array_code_tbmt)) {
                // Kiểm tra xem những KQLCNT có TBMT, chỉ lấy những KQLCNT không có TBMT
                $query_row = $db->query("SELECT so_tbmt FROM nv4_vi_bidding_row WHERE so_tbmt IN(" . implode(',', $array_code_tbmt) . ")");
                while ($_row = $query_row->fetchColumn()) {
                    // Nếu có TBMT thì unset bỏ
                    unset($array_kqlcnt_id[$_row]);
                }

                // Lấy danh sách ID KQLCNT đã được cập nhật sang aita
                $array_kqlcnt_exit = [];
                if (!empty($array_kqlcnt_id)) {
                    $_qr = $db->query("SELECT DISTINCT result_id FROM nv4_vi_aita_result_id WHERE result_id IN (" . implode(',', $array_kqlcnt_id) . ")");
                    while ($_rs = $_qr->fetch()) {
                        $array_kqlcnt_exit[] = $_rs['result_id'];
                    }
                    $_qr->closeCursor();
                }
                // Cập nhật những ID chưa được sang aita
                foreach ($array_kqlcnt_id as $kqlcnt_id) {
                    if (!in_array($kqlcnt_id, $array_kqlcnt_exit)) {
                        $db->exec('INSERT INTO `nv4_vi_aita_result_id` (filter_id, result_id, location, send_status, addtime) VALUES (0, ' . $kqlcnt_id . ', ' . $db->quote($location) . ' , 0, ' . NV_CURRENTTIME . ')');
                    }
                }
            }
        } else {
            if (isset($responsive['status'])) {
                $error_info = print_r($responsive, true);
                print_r($responsive);
            } else {
                $error_info = $res;
                echo $res . "\n";
            }

            $db->query('UPDATE nv4_vi_aita_plan_id SET send_status=-' . $timestamp . ', error_info= ' . $db->quote($error_info) . ' WHERE plan_id=' . $id . ' AND send_status<=0');
        }
    }
    echo "RunTime: " . (time() - NV_CURRENTTIME) . " \n";
} else {
    die("No Data");
}
