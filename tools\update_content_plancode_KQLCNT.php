<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
set_time_limit(0);

$id_tbmt = 0;
if (file_exists(NV_ROOTDIR . '/tools/update_content_plancode_KQLCNT.txt')) {
    $id_tbmt = file_get_contents(NV_ROOTDIR . '/tools/update_content_plancode_KQLCNT.txt');
    $id_tbmt = intval($id_tbmt);
}
try {

    $waitTimeoutInSeconds = 2;
    if ($fp = fsockopen($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
        // It worked
        $elastic_online = 1;
    } else {
        // It didn't work
        $elastic_online = 0;
        echo "Server Elasticsearch didn't work: ";
        echo "ERROR: $errCode - $errStr<br />\n";
    }
    fclose($fp);
    if ($elastic_online) {
        $hosts = array(
            $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
        );
        do {
            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();

            $query_url = $db->query('SELECT * FROM nv4_vi_bidding_result WHERE id > ' . $id_tbmt . ' ORDER BY id ASC LIMIT 100');
            $params = [
                'body' => []
            ];
            $id_tbmt = 0;
            while ($row = $query_url->fetch()) {
                $id_tbmt = $row['id'];

                if (empty($row['plan_code'])) {
                    // Nếu chưa có plan_code thì tìm lại
                    $khlcnt = $db->query('SELECT id_plan FROM nv4_vi_bidding_plans_contract WHERE kqlcnt_id=' . $db->quote($row['id']) . ' LIMIT 1')
                        ->fetch();
                    if (empty($khlcnt)) {
                        $khlcnt = $db->query('SELECT id_plan FROM nv4_vi_bidding_plans_contract WHERE so_tbmt_mst=' . $db->quote($row['code']) . ' LIMIT 1')
                            ->fetch();
                    }

                    if (!empty($khlcnt)) {
                        $code_plans = $db->query('SELECT code FROM nv4_vi_bidding_plans WHERE id = ' . $khlcnt['id_plan'] . ' LIMIT 1')->fetchColumn();
                        if (!empty($code_plans)) {
                            $row['plan_code'] = $code_plans;
                        }
                    }
                }

                if (!empty($row['plan_code'])) {
                    $row['content_full'] = str_replace($row['plan_code'], '', $row['content_full']);
                    $row['content_full'] .= ' ' . $row['plan_code'];

                    $row['content'] = change_alias($row['content_full']); // Không có dấu tiếng việt
                    $row['content'] = str_replace('-', ' ', $row['content']);

                    $db->query('UPDATE nv4_vi_bidding_result SET content = ' . $db->quote($row['content']) . ', content_full = ' . $db->quote($row['content_full']) . ', plan_code=' . $db->quote($row['plan_code']) . ', elasticsearch=' . NV_CURRENTTIME . ' WHERE id =' . $row['id']);

                    $params['body'][] = [
                        'index' => [
                            '_index' => 'dauthau_result',
                            '_id' => $row['id']
                        ]
                    ];
                    $params['body'][] = $row;
                    echo $row['code'] . " --> update\n";
                } else {
                    echo $row['code'] . " --> no plan_code\n";
                }
            }
            $query_url->closeCursor();

            if (!empty($params['body'])) {
                $responses = $client->bulk($params)->asArray();
                if (empty($responses['errors'])) {
                    unset($responses['items']);
                    echo "update thành công<br><br>\n";
                } else {
                    echo '<pre>';
                    print_r($responses);
                    echo '</pre>';
                }
            } else {
                echo 'Ko co du du lieu';
            }
            if ($id_tbmt > 0) {
                file_put_contents(NV_ROOTDIR . '/tools/update_content_plancode_KQLCNT.txt', $id_tbmt);
            }
        } while ($id_tbmt > 0);
    }
} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
}
echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
