<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/aita/config_aita.php';

$array_result_id = [];
$query = $db->query('SELECT result_id FROM nv4_vi_aita_result_id WHERE send_status=0 ORDER BY `id` ASC LIMIT 100');
while ($_result = $query->fetch()) {
    $array_result_id[] = $_result['result_id'];
}
$query->closeCursor();

if (!empty($array_result_id)) {
    $db->query('UPDATE nv4_vi_aita_result_id SET send_status=-9 WHERE result_id IN (' . implode(',', $array_result_id) . ') AND send_status=0');

    $sql = 'SELECT * FROM nv4_vi_bidding_result WHERE id IN (' . implode(',', $array_result_id) . ')';
    $query = $db->query($sql);
    while ($bidding_result = $query->fetch()) {
        $id = $bidding_result['id'];
        if ($bidding_result['is_aita'] == -1) {
            // Bỏ qua các dữ liệu đánh đấu bằng -1
            $db->query('UPDATE nv4_vi_aita_result_id SET send_status=-1 WHERE result_id=' . $id . ' AND send_status=0');
            continue;
        }

        echo "result id = " . $id . "\n";
        $id2 = $db->query('SELECT id FROM nv4_vi_bidding_result WHERE bid_code=' . $db->quote($bidding_result['bid_code']) . ' ORDER BY `code` DESC LIMIT 1')
            ->fetchColumn();
        if ($id != $id2) {
            // bỏ qua KQLCNT không phải mới nhất
            $db->query('UPDATE nv4_vi_aita_result_id SET send_status=-2 WHERE result_id=' . $id . ' AND send_status=0');
            continue;
        }

        // kiểm tra gói thầu thuộc cntt
        $plans_contract = $db->query('SELECT * FROM nv4_vi_bidding_plans_contract WHERE kqlcnt_id=' . $id)->fetch();
        $bid_cntt = 1; // mặc định các gói qua bộ lọc đều là CNTT
        if (!empty($plans_contract)) {
            if ($plans_contract['is_aita'] == 0) { // nếu gói thầu đánh dấu k phải CNTT thì gán lại
                $bid_cntt = 0;
            }
        } else {
            // các dữ liệu cũ, do lỗi gì đó chưa liên kết kqlcnt_id vào contract thì tìm kiếm theo mã tbmt vào nv4_vi_bidding_plans_contract
            $bid_code = $bidding_result['bid_code'] != '' ? $bidding_result['bid_code'] : ($bidding_result['code']);
            $plans_contract = $db->query('SELECT * FROM nv4_vi_bidding_plans_contract WHERE code=' . $db->quote($bid_code))
                ->fetch();
            if (!empty($plans_contract)) {
                if ($plans_contract['is_aita'] == 0) { // nếu gói thầu đánh dấu k phải CNTT thì gán lại
                    $bid_cntt = 0;
                }
            } else {
                // nếu k tìm thấy trong contract thì gói này k xác định dc nguồn=> $bid_cntt = 0;
                $bid_cntt = 0;
            }
        }

        $request = [
            // Tham số bắt buộc
            'apikey' => $apikey,
            'language' => 'vi',
            'module' => 'bidding',
            'action' => 'RESULT',
            'id' => $id
        ];

        $sql = 'SELECT * FROM nv4_bidding_result_goods WHERE result_id=' . $id;
        $_result_goods = $db->query($sql)->fetchAll();
        if (!empty($_result_goods)) {
            foreach ($_result_goods as $key => $value) {
                $is_cntt = 0;
                $_phanmuc = '';
                if ($value['type_info'] == 0) {
                    $phanmuc = goods_detail($value['goods_name']);
                    if (!empty($phanmuc)) {
                        $is_cntt = 1;
                        $bid_cntt = 1;
                        $_phanmuc = implode(',', array_unique($phanmuc));
                    }
                }
                $_result_goods[$key]['phanmuc'] = $_phanmuc;
                $_result_goods[$key]['is_cntt'] = $is_cntt;
                $_result_goods[$key]['bid_cntt'] = $bid_cntt;
            }
        }

        // nếu có hàng hóa là CNTT đánh dấu lại gói thầu đó là cntt ở nv4_vi_bidding_plans_contract
        if ($bid_cntt == 1) {
            $db->query('UPDATE nv4_vi_bidding_plans_contract SET is_aita = 1 WHERE kqlcnt_id=' . $id);
        }
        $request['bidding_result_goods'] = json_encode($_result_goods);

        $sql = 'SELECT * FROM nv4_vi_bidding_win WHERE result_id=' . $id;
        $_bidding_win = $db->query($sql)->fetchAll();
        $request['bidding_win'] = json_encode($_bidding_win);

        // Lưu code business vào 1 mảng
        $list_code_business = [];
        if ($bidding_result['no_business_licence'] != '') {
            $list_code_business[] = $db->quote($bidding_result['no_business_licence']);
        }
        if ($bidding_result['old_business_licence'] != '') {
            $list_code_business[] = $db->quote($bidding_result['old_business_licence']);
        }

        $_bidding_result_business = [];
        $sql = 'SELECT * FROM nv4_vi_bidding_result_business WHERE resultid=' . $id;
        $query_result_business = $db->query($sql);
        while ($bidding_result_business = $query_result_business->fetch()) {
            // Nếu có thì lưu code bussiness lại
            $list_code_business[] = $db->quote($bidding_result_business['no_business_licence']);
            // $list_code_business[] = $db->quote($bidding_result_business['old_business_licence']); // Đan comment lại vì nhà thầu trong này bị đánh dấu trùng
            $_bidding_result_business[] = $bidding_result_business;
        }
        $query_result_business->closeCursor();
        $request['bidding_result_business'] = json_encode($_bidding_result_business);

        $_bidding_result_fail = [];
        $sql = 'SELECT * FROM nv4_vi_bidding_result_fail WHERE resultid=' . $id;
        $query_result_fail = $db->query($sql);
        while ($bidding_result_fail = $query_result_fail->fetch()) {
            $_bidding_result_fail[] = $bidding_result_fail;
        }
        $query_result_fail->closeCursor();

        $request['bidding_result_fail'] = json_encode($_bidding_result_fail);

        $request['is_businesslistings'] = false;
        if (!empty($list_code_business)) {
            // Lấy thêm dữ liệu tại 2 bảng businesslistings_info và businesslistings_addinfo
            $business_licence = implode(',', array_map([$db, 'quote'], $list_code_business));

            $sql = 'SELECT * FROM nv4_businesslistings_info WHERE code IN(' . $business_licence . ')';
            $_bidding_result_goods = $db->query($sql)->fetchAll();
            $request['businesslistings_info'] = json_encode($_bidding_result_goods);

            $sql = 'SELECT * FROM nv4_businesslistings_addinfo WHERE so_dkkd IN(' . $business_licence . ')';
            $_businesslistings_addinfo = $db->query($sql)->fetchAll();
            $request['businesslistings_addinfo'] = json_encode($_businesslistings_addinfo);

            $request['is_businesslistings'] = true;
        }

        if ($bidding_result['solicitor_id'] > 0) {
            $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $bidding_result['solicitor_id'];
            $request['bidding_solicitor'] = $db->query($sql)->fetch();

            $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $bidding_result['solicitor_id'];
            $request['bidding_solicitor_detail'] = $db->query($sql)->fetch();
            $db->query('UPDATE nv4_bidding_solicitor SET update_aita=' . time() . ' WHERE id=' . $bidding_result['solicitor_id']);
        }

        // tìm thêm theo tên chủ đầu tư
        $solicitor_id = get_solicitor_id($bidding_result['investor']);
        if ($solicitor_id > 0) {
            if ($bidding_result['solicitor_id'] == 0) {
                $bidding_result['solicitor_id'] = $solicitor_id;
                $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $solicitor_id;
                $request['bidding_solicitor'] = $db->query($sql)->fetch();

                $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $solicitor_id;
                $request['bidding_solicitor_detail'] = $db->query($sql)->fetch();
            } else if ($bidding_result['solicitor_id'] != $solicitor_id) {
                $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $solicitor_id;
                $request['bidding_investor'] = $db->query($sql)->fetch();

                $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $solicitor_id;
                $request['bidding_investor_detail'] = $db->query($sql)->fetch();
            }
            $db->query('UPDATE nv4_bidding_solicitor SET update_aita=' . time() . ' WHERE id=' . $solicitor_id);
        }

        //Phần lô
        $sql = 'SELECT * FROM nv4_bidding_subdivision WHERE result_id=' . $id;
        $bidding_subdivision = $db->query($sql)->fetchAll();
        $request['bidding_subdivision'] = json_encode($bidding_subdivision);

        $timestamp = time();
        $request['hashsecret'] = password_hash($apisecret . '_' . $timestamp, PASSWORD_DEFAULT);
        $request['timestamp'] = $timestamp;
        $request['bidding_result'] = $bidding_result;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_remote_url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        $safe_mode = (ini_get('safe_mode') == '1' || strtolower(ini_get('safe_mode')) == 'on') ? 1 : 0;
        $open_basedir = ini_get('open_basedir') ? true : false;
        if (!$safe_mode and !$open_basedir) {
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);

        curl_setopt($ch, CURLOPT_POST, sizeof($request));
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($request));
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $res = curl_exec($ch);
        curl_close($ch);

        $responsive = json_decode($res, true);
        if (isset($responsive['status']) and $responsive['status'] == 'success') {
            $db->query('UPDATE nv4_vi_bidding_result SET is_aita = 1 WHERE id =' . $id);
            $db->query('UPDATE nv4_vi_aita_result_id SET send_status=' . $timestamp . ' WHERE result_id=' . $id . ' AND send_status<=0');

            $location = "push_result.php; id=" . $id;
            $time = time();
            // Kiểm tra xem KQLCNT này có TBMT không?
            $_tbmt = $db->query('SELECT COUNT(id) as count_id, id FROM nv4_vi_bidding_row WHERE so_tbmt = ' . $db->quote($bidding_result['code']))
                ->fetch();
            if (intval($_tbmt['count_id']) > 0) {
                // Nếu có TBMT thì cập nhật TBMT sang aita
                // Kiểm tra xem đã có trong bảng nv4_vi_aita_bid_id chưa?
                $bid_aita = $db->query("SELECT COUNT(id) FROM nv4_vi_aita_bid_id WHERE bid_id = " . $_tbmt['id'] . "  LIMIT 1")->fetchColumn();
                if (intval($bid_aita) == 0) {
                    // Chưa có trong bảng nv4_vi_aita_bid_id thì Insert vào bảng nv4_vi_aita_bid_id
                    $stmt = $db->prepare('INSERT INTO `nv4_vi_aita_bid_id`(filter_id, bid_id, location, send_status, addtime) VALUES ( 0, :bid_id, :location, 0, :addtime)');
                    $stmt->bindParam(':bid_id', $_tbmt['id'], PDO::PARAM_INT);
                    $stmt->bindParam(':location', $location, PDO::PARAM_STR);
                    $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                    $stmt->execute();
                }
            }
            // Không có thì kiểm tra KHCLNT
            if (!empty($bidding_result['plan_code'])) {
                // Kiểm tra xem KHLCNT cha của nó có được chuyển sang aita chưa? ChƯA THÌ ĐÁNH DẤU VÀO bảng nv4_vi_aita_plan_id
                // Lấy tất cả ID KHLCNT liên quan đến KQLCNT này
                $arr_plan_id = array_column($db->query("SELECT id FROM nv4_vi_bidding_plans  WHERE code LIKE " . $db->quote($bidding_result['plan_code'] . '%'))
                    ->fetchAll(), 'id');
                if (!empty($arr_plan_id)) {
                    $plan_id = array_column($db->query("SELECT plan_id FROM nv4_vi_aita_plan_id WHERE plan_id IN (" . implode(",", $arr_plan_id) . ")")->fetchAll(), 'plan_id');
                    // So sánh xem có những id nào của KHLCNT chưa được đưa sang aita thì đưa sang luôn
                    $aita_planid = array_diff($arr_plan_id, $plan_id);
                    foreach ($aita_planid as $_aita_plan_id) {
                        // Chưa có trong bảng nv4_vi_aita_plan_id thì Insert vào bảng nv4_vi_aita_plan_id
                        $stmt = $db->prepare('INSERT INTO `nv4_vi_aita_plan_id`(filter_id, plan_id, location, send_status, addtime) VALUES ( 0, :plan_id, :location, 0, :addtime)');
                        $stmt->bindParam(':plan_id', $_aita_plan_id, PDO::PARAM_INT);
                        $stmt->bindParam(':location', $location, PDO::PARAM_STR);
                        $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                        $stmt->execute();
                    }
                }
            }

            echo "OK\n";
        } else {
            if (isset($responsive['status'])) {
                $error_info = print_r($responsive, true);
                print_r($responsive);
            } else {
                $error_info = $res;
                echo $res . "\n";
            }
            $db->query('UPDATE nv4_vi_aita_result_id SET send_status=-' . $timestamp . ', error_info= ' . $db->quote($error_info) . ' WHERE result_id=' . $id . ' AND send_status<=0');
        }
    }
    $query->closeCursor();
} else {
    die("No Data");
}
