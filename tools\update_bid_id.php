<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$waitTimeoutInSeconds = 2;
if ($fp = fsockopen($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
        // It worked
    $elastic_online = 1;
    fclose($fp);
} else {
        // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
}

$hosts = array(
    $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
);

$file_name = NV_ROOTDIR . '/tools/update_bid_id.txt';
if (file_exists($file_name)) {
    $strID = file_get_contents($file_name);
    $arrID = explode('_', $strID);
    $id = intval($arrID[0]);
    $max_id = intval($arrID[1]);
} else {
    $id = $db->query("SELECT min(id) FROM nv4_vi_bidding_result")->fetchColumn();
    $max_id = $db->query("SELECT max(id) FROM nv4_vi_bidding_result")->fetchColumn();
}

echo ("\nmax_id: " . $max_id);
$last_id = ($id  + 1000);

if ($last_id >= $max_id) {
    $max_id = $db->query("SELECT max(id) FROM nv4_vi_bidding_result")->fetchColumn();
    $last_id = $max_id + 1;
}

$sql = $db->query("SELECT id, bid_id FROM nv4_vi_bidding_result WHERE id > " . $id . " AND id < " . $last_id . " ORDER BY id LIMIT 1000");
$db->beginTransaction();
try {
    while ($row = $sql->fetch()) {
        echo ("\n>>ID: " . $row['id']);
        $params['body'][] = [
            'update' => [
                '_index' => 'dauthau_result',
                '_id' => $row['id']
            ]
        ];

        $params['body'][] = [
            'doc' => [
                'bid_id' => $row['bid_id']
            ]
        ];

        $params1['body'][] = [
            'update' => [
                '_index' => 'en_dauthau_result',
                '_id' => $row['id']
            ]
        ];

        $params1['body'][] = [
            'doc' => [
                'bid_id' => $row['bid_id']
            ]
        ];

        $newid = $row['id'];
    }
    $sql->closeCursor();

    // Update lên ES
    if (!empty($params['body']) and !empty($params1['body'])) {
        $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();

        $response = $client->bulk($params);
        $response1 = $client->bulk($params1);

        if (empty($responses['errors'])) {
            unset($responses['items']);
            echo "\n>> Update Dauthau_result thành công ES";
        } else {
            echo '<pre>';
            print_r($responses);
            echo '</pre>';
        }

        if (empty($responses1['errors'])) {
            unset($responses1['items']);
            echo "\n>> Update En_Dauthau_result thành công ES";
        } else {
            echo '<pre>';
            print_r($responses);
            echo '</pre>';
        }
    }

    echo ("\nnewid:" . $newid);
    echo ("\nid: " . $id);
    if ($newid > $id) {
        echo ("\nnewid = " . $newid);
        file_put_contents($file_name, $newid . '_' . $max_id);
    } else {
        if ($last_id < $max_id) {
            $newid = $last_id;
            echo ("\nlast_id = " . $last_id);
            file_put_contents($file_name, $newid . '_' . $max_id);
        } else {
            echo (">> \033[91mHết dữ liệu \033[0m");
            exit(1);
        }
    }
} catch (Exception $e) {
    $db->rollBack();
    print_r($e);
    print_r("\nLỗi rồi\n");
    exit(1);
}


echo "\nThoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
echo "\n> Đã chạy xong: " . $i;
echo "\n-------------------------------------------\n";
