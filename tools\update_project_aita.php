<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
set_time_limit(0);

try {
    if (file_exists(NV_ROOTDIR . '/tools/nv4_vi_aita_project_id.txt')) {
        $minid = file_get_contents(NV_ROOTDIR . '/tools/nv4_vi_aita_project_id.txt');
        $minid = intval($minid);
    } else {
        $id_min_msc = $db->query("SELECT id FROM nv4_vi_project_investment WHERE is_new_msc = 1 ORDER BY id ASC LIMIT 1")->fetchColumn();
        $minid = $db->query("SELECT id FROM nv4_vi_aita_devproject_id WHERE devproject_id >= " . $id_min_msc . " ORDER BY id ASC LIMIT 1")->fetchColumn();
    }
    try {
        $sql = "SELECT id, devproject_id FROM nv4_vi_aita_devproject_id WHERE id > " . $minid . " ORDER BY id ASC LIMIT 1000";
        $query = $db->query($sql);

        $arr_id = [];
        $newid = $minid;
        $arr_id = array_column($query->fetchAll(), 'devproject_id', 'id');
        if (empty($arr_id)) {
            echo "Đã chạy hết!!";
            exit(1);
        }
        $newid = array_key_last($arr_id);
        $list_id = implode(',', $arr_id);
        $arr_id_new_msc = $db->query("SELECT id FROM nv4_vi_project_investment WHERE is_new_msc = 1 AND id IN(" . $list_id . ")")->fetchAll();

        $list_id_new_msc = implode(',', array_column($arr_id_new_msc, 'id'));
        if (!empty($list_id_new_msc)) {
            $db->query("UPDATE `nv4_vi_aita_devproject_id` SET `send_status`='0',`error_info`='' WHERE devproject_id IN(" . $list_id_new_msc . ")");
        }
    } catch (Exception $e) {
        echo '<pre>';
        print_r($e);
        echo '</pre>';
    }
    if ($newid > $minid) {
        file_put_contents(NV_ROOTDIR . '/tools/nv4_vi_aita_project_id.txt', $newid);
    }
    echo "minid:" . $minid . "\n";
} catch (PDOException $e) {
    print_r($e);
}
