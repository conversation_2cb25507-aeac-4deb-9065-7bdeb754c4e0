#!/bin/bash

whileccheck=1
while [ $whileccheck -gt 0 ] ; do
	ruledetail=$(php /home/<USER>/private/aita/filter_devprojects.php)
	echo $ruledetail
	if [[ "$ruledetail" == "Không Dự án mới" ]]; then
		whileccheck=0
		break
	fi
	sleep 0.3
done

whileccheck=1
while [ $whileccheck -gt 0 ] ; do
	ruledetail=$(php /home/<USER>/private/aita/filter_khlcnt.php)
	echo $ruledetail
	if [[ "$ruledetail" == "Không kế hoạch LCNT mới" ]]; then
		whileccheck=0
		break
	fi	
done

whileccheck=1
while [ $whileccheck -gt 0 ] ; do
	ruledetail=$(php /home/<USER>/private/aita/filter_tbmt.php)
	echo $ruledetail
	if [[ "$ruledetail" == "Không có TBMT" ]]; then
		whileccheck=0
		break
	fi	
done

whileccheck=1
while [ $whileccheck -gt 0 ] ; do
	ruledetail=$(php /home/<USER>/private/aita/filter_result.php)
	echo $ruledetail
	if [[ "$ruledetail" == "Không có KQLCNT mới trong csdl" ]]; then
		whileccheck=0
		break
	fi	
done

echo "push_aita_1"
screen -S push_aita_1 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

screen -ls
exit
#ps aux | grep '/home/<USER>/private/aita/' | awk '{print $2}' |  xargs sudo kill -9


echo "push_aita_2"
screen -S push_aita_2 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_3"
screen -S push_aita_3 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_4"
screen -S push_aita_4 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_5"
screen -S push_aita_5 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_6"
screen -S push_aita_6 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_7"
screen -S push_aita_7 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_8"
screen -S push_aita_8 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_9"
screen -S push_aita_9 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10


echo "push_aita_10"
screen -S push_aita_10 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_11"
screen -S push_aita_11 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_12"
screen -S push_aita_12 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_13"
screen -S push_aita_13 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_14"
screen -S push_aita_14 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_15"
screen -S push_aita_15 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_16"
screen -S push_aita_16 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_17"
screen -S push_aita_17 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_18"
screen -S push_aita_18 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_19"
screen -S push_aita_19 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

echo "push_aita_20"
screen -S push_aita_20 -dm bash -c "bash /home/<USER>/private/aita/push_aita.sh"
sleep 10

screen -ls