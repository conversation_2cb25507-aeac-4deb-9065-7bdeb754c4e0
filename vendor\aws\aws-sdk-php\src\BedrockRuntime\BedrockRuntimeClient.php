<?php
namespace Aws\BedrockRuntime;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Bedrock Runtime** service.
 * @method \Aws\Result invokeModel(array $args = [])
 * @method \GuzzleHttp\Promise\Promise invokeModelAsync(array $args = [])
 * @method \Aws\Result invokeModelWithResponseStream(array $args = [])
 * @method \GuzzleHttp\Promise\Promise invokeModelWithResponseStreamAsync(array $args = [])
 */
class BedrockRuntimeClient extends AwsClient {}
