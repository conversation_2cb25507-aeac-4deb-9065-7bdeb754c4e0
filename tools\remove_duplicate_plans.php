<?php

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_SYSTEM', true);
require NV_ROOTDIR . '/mainfile.php';

$waitTimeoutInSeconds = 2;
if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr\n";
    exit();
}
fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
    // xóa các row trùng
    do {
        $a = 0;
        $_sql = "SELECT MAX(id) as id, code, count(*) as sum FROM nv4_vi_bidding_plans GROUP BY `code` HAVING sum > 1 LIMIT 100";
        $_result = $db->query($_sql);
        $array_plans = [];
        while ($tmp = $_result->fetch()) {
            $array_plans[$tmp['id']] = $tmp;
            $a++;
        }
        $_result->closeCursor();
        if ($a > 0) {
            file_put_contents(NV_ROOTDIR . '/tools/' . NV_LANG_DATA . '_remove_duplicate_plans.txt', implode(PHP_EOL, array_column($array_plans, 'id')) . PHP_EOL, FILE_APPEND);
            $db->beginTransaction();
            try {
                // Xóa trên elasticsearch
                $client->deleteByQuery([
                    'index' => 'dauthau_plans',
                    'body' => [
                        'query' => [
                            'terms' => [
                                'id' => array_column($array_plans, 'id')
                            ]
                        ]
                    ]
                ]);

                $client->deleteByQuery([
                    'index' => 'en_dauthau_plans',
                    'body' => [
                        'query' => [
                            'terms' => [
                                'id' => array_column($array_plans, 'id')
                            ]
                        ]
                    ]
                ]);
                // xóa giá trị trùng
                $db->exec("DELETE FROM `nv4_vi_bidding_plans` WHERE `id` IN ( " . implode(',', array_column($array_plans, 'id')) . ")");
                $db->exec("DELETE FROM `nv4_vi_bidding_plans_contract` WHERE `id_plan` IN ( " . implode(',', array_column($array_plans, 'id')) . ")");
                $db->exec("DELETE FROM `nv4_vi_bidding_follow_plans` WHERE `plans_id` IN ( " . implode(',', array_column($array_plans, 'id')) . ")");

                $db->exec("DELETE FROM `nv4_en_bidding_plans` WHERE `id` IN ( " . implode(',', array_column($array_plans, 'id')) . ")");
                $db->exec("DELETE FROM `nv4_en_bidding_plans_contract` WHERE `id_plan` IN ( " . implode(',', array_column($array_plans, 'id')) . ")");
                $db->exec("DELETE FROM `nv4_en_bidding_follow_plans` WHERE `plans_id` IN ( " . implode(',', array_column($array_plans, 'id')) . ")");
                echo "Đã xóa id: \033[31m" . implode(', ', array_column($array_plans, 'id')) . "\033[0m\n";
            } catch (PDOException $e) {
                $db->rollBack();
                print_r($e);
                die($e->getMessage());
            }
            $db->commit();
        }
    } while ($a > 0);
}
echo "Update trong: \033[32m" . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\033[0m\n";

