<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
set_time_limit(0);

$id_tbmt = 0;
if (file_exists(NV_ROOTDIR . '/tools/update_content_notify_no_KQMT.txt')) {
    $id_tbmt = file_get_contents(NV_ROOTDIR . '/tools/update_content_notify_no_KQMT.txt');
    $id_tbmt = intval($id_tbmt);
}
try {

    $waitTimeoutInSeconds = 2;
    if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
        // It worked
        $elastic_online = 1;
    } else {
        // It didn't work
        $elastic_online = 0;
        echo "Server Elasticsearch didn't work: ";
        echo "ERROR: $errCode - $errStr<br />\n";
    }
    fclose($fp);
    if ($elastic_online) {
        $hosts = array(
            $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
        );
        do {
            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();

            $query_url = $db->query('SELECT * FROM nv4_vi_bidding_open WHERE so_tbmt > ' . $id_tbmt . ' ORDER BY id ASC LIMIT 100');
            $params = [
                'body' => []
            ];
            $id_tbmt = 0;
            while ($row = $query_url->fetch()) {
                $id_tbmt = $row['so_tbmt'];

                if (!empty($row['bidno'])) {
                    $row['content_full'] = str_replace($row['bidno'], '', $row['content_full']);
                    $row['content_full'] .= ' ' . $row['bidno'];

                    $row['content'] = change_alias($row['content_full']); // Không có dấu tiếng việt
                    $row['content'] = str_replace('-', ' ', $row['content']);

                    $db->query('UPDATE nv4_vi_bidding_open SET content = ' . $db->quote($row['content']) . ', content_full = ' . $db->quote($row['content_full']) . ', bidno=' . $db->quote($row['bidno']) . ' WHERE so_tbmt =' . $row['so_tbmt']);

                    $params['body'][] = [
                        'index' => [
                            '_index' => 'dauthau_open',
                            // '_type' => 'nv4_vi_bidding_row',
                            '_id' => $row['so_tbmt']
                        ]
                    ];
                    $params['body'][] = $row;
                    echo $row['bidno'] . " --> update\n";
                } else {
                    echo $row['bidno'] . " --> no mã gói thầu\n";
                }
            }
            $query_url->closeCursor();

            if (!empty($params['body'])) {
                $responses = $client->bulk($params)->asArray();
                if (empty($responses['errors'])) {
                    unset($responses['items']);
                    echo "update thành công<br><br>\n";
                } else {
                    echo '<pre>';
                    print_r($responses);
                    echo '</pre>';
                }
            } else {
                echo 'Ko co du du lieu';
            }
            if ($id_tbmt > 0) {
                file_put_contents(NV_ROOTDIR . '/tools/update_content_notify_no_KQMT.txt', $id_tbmt);
            }
        } while ($id_tbmt > 0);
    }
} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
}
echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
