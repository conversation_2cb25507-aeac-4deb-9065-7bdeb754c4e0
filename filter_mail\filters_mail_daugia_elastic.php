<?php
$waitTimeoutInSeconds = 2;
$filters_mail_log = NV_ROOTDIR . '/data/filters_mail_dau_gia_log_' . date('Ymd') . '.txt';

if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    file_put_contents($filters_mail_log, "Server Elasticsearch didn't work.\n", FILE_APPEND);
    die();
}
fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
}

/*
 * Dùng ?testuserid=USERID để kiểm tra trực tiếp đối với thành viên
 * Nếu testuserid hệ thống sẽ bỏ qua ghi dữ liệu vào CSLD chỉ hiển thị kết quả
 */

$testUserid = 0;

$last_row_id = [];
if (!$testUserid) {
    $new_loop = 0;

    $logs_info = $db->query('SELECT * FROM nv4_vi_bidding_dau_gia_logs WHERE status=0 limit 1')->fetch();
    //$logs_info = $db->query('SELECT * FROM nv4_vi_bidding_dau_gia_logs WHERE id =83389 limit 1')->fetch();

    if (!empty($logs_info)) {
        $last_row_id = $logs_info;
        $last_userid = $logs_info['userid'];
    } else {
        // lấy id mới nhất
        /*
         * $bid = $db->query('SELECT max(id_bid) as to_id FROM nv4_dau_gia_bid')->fetch();
         * $bid_select = $db->query('SELECT max(id_bid) as to_id_select FROM nv4_dau_gia_bid_select')->fetch();
         */

        $array_query_elastic = array();
        $array_query_elastic['size'] = 1;
        $array_query_elastic['sort'] = [
            [
                "id_bid" => [
                    "order" => "desc"
                ]
            ]
        ];
        $array_query_elastic['_source'] = array(
            'id_bid'
        );
        $params = array();
        $params['index'] = 'daugia_bid';
        $params['body'] = $array_query_elastic;
        $response = $client->search($params)->asArray();
        if ($response['hits']['total']['value'] > 0) {
            $bid['to_id'] = $response['hits']['hits'][0]['_source']['id_bid'];
        }
        if (!isset($bid['to_id']) or $bid['to_id'] <= 0) {
            file_put_contents($filters_mail_log, "Lỗi maxid ES \n", FILE_APPEND);
            die('Lỗi');
        }

        $array_query_elastic = array();
        $array_query_elastic['size'] = 10;
        $array_query_elastic['sort'] = [
            [
                "id_bid" => [
                    "order" => "desc"
                ]
            ]
        ];
        $array_query_elastic['_source'] = array(
            'id_bid'
        );
        $params = array();
        $params['index'] = 'daugia_bid_select';
        $params['body'] = $array_query_elastic;
        $response = $client->search($params)->asArray();
        if ($response['hits']['total']['value'] > 0) {
            $info['to_id_select'] = $response['hits']['hits'][0]['_source']['id_bid'];
        }
        if (!isset($info['to_id_select']) or $info['to_id_select'] <= 0) {
            file_put_contents($filters_mail_log, "Lỗi max to_id_select ES \n", FILE_APPEND);
            die('Lỗi');
        }

        $last_logs = $db->query('SELECT * FROM nv4_vi_bidding_dau_gia_logs ORDER BY id DESC limit 1')->fetch();
        if (empty($last_logs)) { // lần đầu
            $logs_info = $last_row_id;

            $last_row_id['from_id'] = 0;
            $last_row_id['from_id_select'] = 0;

            $last_row_id['to_id'] = $bid['to_id'];
            $last_row_id['to_id_select'] = $info['to_id_select'];
            $last_userid = 0;
            $new_loop = 1;
        } else {
            if ($last_logs['to_id'] < $bid['to_id'] or $last_logs['to_id_select'] < $info['to_id_select']) {
                $new_loop = 1;
                $last_userid = 0;
                $last_row_id['from_id'] = $last_logs['to_id'];
                $last_row_id['from_id_select'] = $last_logs['to_id_select'];

                $last_row_id['to_id'] = $bid['to_id'];
                $last_row_id['to_id_select'] = $info['to_id_select'];
            } else {
                file_put_contents($filters_mail_log, "Không có tin mới trong csdl. Hãy chạy tiếp khi có bài đăng mới\n", FILE_APPEND);
                die('Không có tin mới');
            }
        }
    }

    $time = time();
    $start_time = $time;
    if ($new_loop) {
        if ($last_row_id['to_id'] > $last_row_id['from_id'] + 500) {
            // Nếu có quá nhiều thì chỉ tìm kiếm trong 500
            $last_row_id['to_id'] = $last_row_id['from_id'] + 500;
        }
        if ($last_row_id['from_id'] == $last_row_id['to_id']) {
            die('No Data new');
        }

        // thêm logs mới vào csdl
        $stmt = $db->prepare('INSERT INTO nv4_vi_bidding_dau_gia_logs (from_id, to_id, from_id_select, to_id_select, from_time, to_time, total_time, userid, status) VALUES (:from_id, :to_id, :from_id_select, :to_id_select, :from_time, :to_time, 0, 0, 0)');
        $stmt->bindParam(':from_id', $last_row_id['from_id'], PDO::PARAM_INT);
        $stmt->bindParam(':to_id', $last_row_id['to_id'], PDO::PARAM_INT);

        $stmt->bindParam(':from_id_select', $last_row_id['from_id_select'], PDO::PARAM_INT);
        $stmt->bindParam(':to_id_select', $last_row_id['to_id_select'], PDO::PARAM_INT);

        $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
        $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
        $exc = $stmt->execute();
        $logs_id = $db->lastInsertId();
        $run_time = 0;
    } else {
        $logs_id = $logs_info['id'];
        $run_time = $logs_info['total_time'];
    }
} else {
    $bid = $db->query('SELECT max(id_bid) as to_id FROM nv4_dau_gia_bid')->fetchColumn();
    $bid_select = $db->query('SELECT max(id_bid) as to_id_select FROM nv4_dau_gia_bid_select')->fetchColumn();

    $last_row_id['from_id'] = 0;
    $last_row_id['from_id_select'] = 0;

    $last_row_id['to_id'] = $bid;
    $last_row_id['to_id_select'] = $bid_select;

    $time = time();
    $start_time = $time;
    $run_time = 0;
}

// lấy vip
$number_user_scan_all = 50;
$time_start_thisday = mktime(date('H'), date('i'), date('s'), date('n'), date('j'), date('Y'));
if ($testUserid) {
    $query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id =' . $testUserid . ' AND vip=6 ORDER BY user_id ASC limit ' . $number_user_scan_all);
} else {
    $query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id >' . $last_userid . ' AND vip=6 ORDER BY user_id ASC limit ' . $number_user_scan_all);
}
$number_user_scan = 0;
$user_id_end = 0;
$arr_vip = $arr_id_vip = array();
while ($vip_info = $query_vip->fetch()) {
    ++$number_user_scan;
    $user_id_end = $vip_info['user_id'];
    $arr_vip[$vip_info['user_id']] = $vip_info;
    $arr_vip[$vip_info['user_id']]['filter'] = array();

    $arr_id_vip[$vip_info['user_id']] = $vip_info['user_id'];
}

// Xuất thông tin test tài khoản VIP
if ($testUserid) {
    if (empty($arr_vip)) {
        echo ("User này không có tài khoản VIP<br/>");
    } else {
        echo ("Tài khoản VIP OK<br/>" . debugArray($arr_vip) . "<br/><br/>");
    }
}

// bộ lọc
if (!empty($arr_id_vip)) {
    $query_filter = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE userid IN (' . implode(',', $arr_id_vip) . ') AND status=1 AND vip_use=6');
    while ($result = $query_filter->fetch()) {
        $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
    }
}

// Xuất thông tin test bộ lọc
if ($testUserid) {
    if (empty($arr_vip[$testUserid]['filter'])) {
        echo ("User này không có bộ lọc<br/>");
    } else {
        echo ("Bộ lọc OK<br/>" . debugArray($arr_vip[$testUserid]['filter']) . "<br/><br/>");
    }
}

// lọc tin -> chuyển bảng tmp trước khi chuyển thành mail
if (empty($arr_vip)) {
    // Nếu không lấy được VIP nào thì coi như đã chạy xong một vòng và update lại status là đã chạy xong
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $db->query('UPDATE nv4_vi_bidding_dau_gia_logs SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_log, "Đã quét xong một lượt các vip. Tổng thời gian cho một lượt:" . $total_time . "\n", FILE_APPEND);
    die();
} else {
    // $db->query('UPDATE nv4_vi_bidding_dau_gia_logs SET userid =' . $user_id_end . ' WHERE id =' . $logs_id);
    file_put_contents($filters_mail_log, "ArrayID daugia:" . $last_row_id['from_id'] . "-" . $last_row_id['to_id'] . "; ArrayID tổ chức đấu giá:" . $last_row_id['from_id_select'] . "-" . $last_row_id['to_id_select'] . "\n", FILE_APPEND);

    $db->beginTransaction();
    try {
        foreach ($arr_vip as $vip_id => $vip) {
            echo "Bắt đầu quét:" . $vip_id . "\n";
            file_put_contents($filters_mail_log, "Bắt đầu quét:" . $vip_id . "\n", FILE_APPEND);
            if (!empty($vip['filter'])) {
                foreach ($vip['filter'] as $filter) {
                    if (!empty($filter)) {
                        $search_elastic = array();
                        if ($filter['search_type'] == 1) {
                            if ($filter['par_search']) {
                                $search_fields = [
                                    'title'
                                ];
                            } else {
                                $search_fields = [
                                    'searchtext_full'
                                ];
                            }
                        } else {
                            if ($filter['par_search']) {
                                $search_fields = [
                                    'title_search'
                                ];
                            } else {
                                $search_fields = [
                                    'searchtext_simple'
                                ];
                            }
                        }
                        if (!empty($filter['key_search'])) {
                            $arr_key = explode(',', $filter['key_search']);
                            if (!empty($filter['searchkind'])) {
                                $arr_key = array_map(function ($a) {
                                    return explode(' ', $a);
                                }, $arr_key);
                                $arr_key = new RecursiveIteratorIterator(new RecursiveArrayIterator($arr_key));
                            }
                            $keyword_type = $filter['searchkind'] <= 1 ? 'should' : 'must';
                            foreach ($search_fields as $f) {
                                foreach ($arr_key as $key) {
                                    if ($filter['search_type'] == 0) {
                                        $key = str_replace('-', ' ', change_alias($key));
                                    }
                                    $key = trim($key);
                                    if (preg_match('/^[0-9]*?$/', $key)) { // Nếu keyword chỉ là dãy số thì tìm wildcard
                                        $search_elastic[$keyword_type][] = [
                                            "wildcard" => [
                                                $f => [
                                                    "value" => '*' . $key . '*'
                                                ]
                                            ]
                                        ];
                                    } else {
                                        $search_elastic[$keyword_type][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    }
                                }
                            }
                            if (!empty($search_elastic['should'])) {
                                $search_elastic['minimum_should_match'] = '1';
                                $search_elastic['boost'] = '1.0';
                            }
                        }
                        if (!empty($filter['without_key'])) {
                            $arr_key = explode(',', $filter['without_key']);
                            foreach ($search_fields as $f) {
                                foreach ($arr_key as $key) {
                                    if ($filter['search_type'] == 1) {
                                        $key = trim($key);
                                        $search_elastic['must_not'][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    } else {
                                        $key = str_replace('-', ' ', change_alias($key));
                                        $key = trim($key);
                                        $search_elastic['must_not'][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    }
                                }
                            }
                        }

                        // Nếu chọn tìm theo tên tài sản và nơi có tài sản thì sẽ lấy địa điểm làm từ khóa phụ
                        if ($filter['par_search']) {
                            $key_search_location = [];
                            if (!empty($filter['id_province'])) {
                                $province = $db->query("SELECT id, title, alias FROM " . NV_PREFIXLANG . "_location_province WHERE id=" . $db->quote($filter['id_province']))->fetch();
                                $key_search_location[] = !empty($province) ? $province['title'] : '';
                            }
                            if (!empty($filter['id_district'])) {
                                $district = $db->query("SELECT id, title FROM " . NV_PREFIXLANG . "_location_district WHERE id=" . $db->quote($filter['id_district']))->fetch();
                                $key_search_location[] = !empty($district) ? $district['title'] : '';
                            }

                            $key_search2_array = empty($filter['key_search2']) ? [] : explode(',', $filter['key_search2']);
                            $key_search2_merge = array_unique(array_merge($key_search2_array, $key_search_location));
                            $filter['key_search2'] = implode(',', $key_search2_merge);
                        }

                        if (!empty($filter['key_search2'])) {
                            // Tìm theo từ khóa cùng
                            $arr_key = explode(',', $filter['key_search2']);
                            $search_bosung_should = [];
                            foreach ($search_fields as $f) {
                                foreach ($arr_key as $key) {
                                    if (!empty($filter['search_one_key'])) {
                                        //Một trong các từ khóa là điều kiện bắt buộc
                                        if ($filter['search_type'] == 1) {
                                            $key = trim($key);
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        } else {
                                            $key = str_replace('-', ' ', change_alias($key));
                                            $key = trim($key);
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        }
                                    } else {
                                        if ($filter['search_type'] == 1) {
                                            $key = trim($key);
                                            $search_elastic['must'][] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        } else {
                                            $key = str_replace('-', ' ', change_alias($key));
                                            $key = trim($key);
                                            $search_elastic['must'][] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        }
                                    }
                                }
                            }
                            if (!empty($search_bosung_should)) {
                                $search_elastic['must'][] = [
                                    "bool" => [
                                        "should" => $search_bosung_should,
                                        "minimum_should_match" => 1
                                    ]
                                ];
                            }
                        }
                        $num_row = 0;
                        if ($filter['vip_use2'] == 1) { // TBMT
                            $search_elastic['must'][]['range']['id_bid'] = [
                                "gt" => $last_row_id['from_id'],
                                "lte" => $last_row_id['to_id']
                            ];
                            if ($filter['time_find'] > 0) {
                                $time_find = mktime(0, 0, 0, date('n'), date('j'), date('Y')) - 86400 * $filter['time_find'];

                                $search_elastic['must'][]['range']['date_bid'] = [
                                    "gte" => $time_find
                                ];
                            }

                            if ($filter['price_from'] > 0 and $filter['price_to'] > 0) {
                                $search_elastic['must'][]['range']['min_bid_prices'] = [
                                    "gte" => $filter['price_from'],
                                    "lte" => $filter['price_to']
                                ];
                            } else {
                                // tìm theo giá gói thầu
                                if ($filter['price_from'] > 0) {
                                    $search_elastic['must'][]['range']['min_bid_prices'] = [
                                        "gte" => $filter['price_from']
                                    ];
                                }
                                if ($filter['price_to'] > 0) {
                                    $search_elastic['must'][]['range']['min_bid_prices'] = [
                                        "lte" => $filter['price_to']
                                    ];
                                }
                            }

                            if (!empty($filter['id_bidder'])) {
                                $search_elastic['must'][] = [
                                    'match' => [
                                        'id_bidder' => [
                                            'query' => $filter['id_bidder']
                                        ]
                                    ]
                                ];
                            }

                            if (!empty($filter['id_province'])) {
                                $search_elastic['must'][] = [
                                    'match' => [
                                        'id_province_owner' => [
                                            'query' => $filter['id_province']
                                        ]
                                    ]
                                ];
                            }

                            if (!empty($filter['id_district'])) {
                                $search_elastic['must'][] = [
                                    'match' => [
                                        'id_district_owner' => [
                                            'query' => $filter['id_district']
                                        ]
                                    ]
                                ];
                            }

                            $array_query_elastic = array();
                            if (!empty($search_elastic)) {
                                $array_query_elastic['query']['bool'] = $search_elastic;
                            }
                            $array_query_elastic['size'] = 500;
                            $array_query_elastic['sort'] = [
                                [
                                    "date_bid" => [
                                        "order" => "desc"
                                    ]
                                ]
                            ];
                            $array_query_elastic['_source'] = array(
                                'id_bid'
                            );

                            if ($testUserid) {
                                echo "Tìm kiếm với query: <br />" . debugArray($array_query_elastic) . "<br /><br />";
                                /* echo "Tìm kiếm với query: <br />" . json_encode($array_query_elastic) . "<br /><br />";
                                die; */
                            }

                            $params = array();
                            $params['index'] = 'daugia_bid';
                            $params['body'] = $array_query_elastic;
                            $response = $client->search($params)->asArray();
                            if ($testUserid) {
                                echo "Kết quả: <br />" . debugArray($response) . "<br /><br />";
                            } else {
                                $num_row = $response['hits']['total']['value'];
                                foreach ($response['hits']['hits'] as $value) {
                                    if (!empty($value['_source'])) {
                                        $bidding = $value['_source'];
                                        $arr_id[$bidding['id_bid']] = $bidding['id_bid'];
                                        $stmt = $db->prepare('INSERT IGNORE INTO nv4_vi_bidding_dau_gia_id (userid, filter_id, bid_id, addtime, send_status) VALUES (:userid, :filter_id, :bid_id, :addtime, 0)');
                                        $time = NV_CURRENTTIME;
                                        $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                        $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                        $stmt->bindParam(':bid_id', $bidding['id_bid'], PDO::PARAM_INT);
                                        $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                        $exc = $stmt->execute();
                                    }
                                }
                            }
                        } else if ($filter['vip_use2'] == 2) {
                            $search_elastic['must'][]['range']['id_bid'] = [
                                "gt" => $last_row_id['from_id_select'],
                                "lte" => $last_row_id['to_id_select']
                            ];
                            if ($filter['time_find'] > 0) {
                                $time_find = mktime(0, 0, 0, date('n'), date('j'), date('Y')) - 86400 * $filter['time_find'];

                                $search_elastic['must'][]['range']['opening_bid'] = [
                                    "gte" => $time_find
                                ];
                            }

                            if ($filter['price_from'] > 0 and $filter['price_to'] > 0) {
                                $search_elastic['must'][]['range']['min_bid_prices'] = [
                                    "gte" => $filter['price_from'],
                                    "lte" => $filter['price_to']
                                ];
                            } else {
                                // tìm theo giá gói thầu
                                if ($filter['price_from'] > 0) {
                                    $search_elastic['must'][]['range']['min_bid_prices'] = [
                                        "gte" => $filter['price_from']
                                    ];
                                }
                                if ($filter['price_to'] > 0) {
                                    $search_elastic['must'][]['range']['min_bid_prices'] = [
                                        "lte" => $filter['price_to']
                                    ];
                                }
                            }

                            if (!empty($filter['id_province'])) {
                                $search_elastic['must'][] = [
                                    'match' => [
                                        'id_province_owner' => [
                                            'query' => $filter['id_province']
                                        ]
                                    ]
                                ];
                            }

                            if (!empty($filter['id_district'])) {
                                $search_elastic['must'][] = [
                                    'match' => [
                                        'id_district_owner' => [
                                            'query' => $filter['id_district']
                                        ]
                                    ]
                                ];
                            }

                            $array_query_elastic = array();
                            if (!empty($search_elastic)) {
                                $array_query_elastic['query']['bool'] = $search_elastic;
                            }
                            $array_query_elastic['size'] = 500;
                            $array_query_elastic['sort'] = [
                                [
                                    "opening_bid" => [
                                        "order" => "desc"
                                    ]
                                ]
                            ];
                            $array_query_elastic['_source'] = array(
                                'id_bid'
                            );

                            if ($testUserid) {
                                echo "Tìm kiếm với query: <br />" . debugArray($array_query_elastic) . "<br /><br />";
                            }

                            $params = array();
                            $params['index'] = 'daugia_bid_select';
                            $params['body'] = $array_query_elastic;
                            $response = $client->search($params)->asArray();
                            if ($testUserid) {
                                echo "Kết quả: <br />" . debugArray($response) . "<br /><br />";
                            } else {
                                $num_row = $response['hits']['total']['value'];
                                foreach ($response['hits']['hits'] as $value) {
                                    if (!empty($value['_source'])) {
                                        $bidding = $value['_source'];
                                        $arr_id[$bidding['id_bid']] = $bidding['id_bid'];
                                        $stmt = $db->prepare('INSERT IGNORE INTO nv4_vi_bidding_dau_gia_id (userid, filter_id, select_id, addtime, send_status) VALUES (:userid, :filter_id, :select_id, :addtime, 0)');
                                        $time = NV_CURRENTTIME;
                                        $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                        $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                        $stmt->bindParam(':select_id', $bidding['id_bid'], PDO::PARAM_INT);
                                        $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                        $exc = $stmt->execute();
                                    }
                                }
                            }
                        }

                        // ghi log bộ lọc
                        if ($num_row > 0) {
                            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastresult = ' . NV_CURRENTTIME . ', stat_totalresult = stat_totalresult + ' . $num_row . ' WHERE id=' . $filter['id']);
                            file_put_contents($filters_mail_log, " Filter:" . $filter['id'] . "; Bid_id: " . implode(',', $arr_id) . "\n", FILE_APPEND);
                        }
                    }
                }
            }
            if ($testUserid) {
                die("Dừng test testUserid:" . $testUserid . "<br /><br />");
            }
            // ghi lại logs cho quá trình tìm kiếm tin cho mỗi vip
            $time = NV_CURRENTTIME;
            $stmt = $db->prepare('UPDATE nv4_vi_bidding_dau_gia_logs SET to_time = :to_time, userid = :userid WHERE id =' . $logs_id);
            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
            $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
            $exc = $stmt->execute();
        }
        $db->commit();
    } catch (PDOException $e) {
        print_r($e);
        file_put_contents($filters_mail_log, "Lỗi INSERT:\n" . print_r($e, true) . "\n", FILE_APPEND);
        $db->rollBack();
        notify_to_slack($filters_mail_log, "filters_mail_daugia_elastic.php Lỗi INSERT:\n" . print_r($e, true) . "\n");
        die(); // Remove this line after checks finished
    }

    // Sau khi chạy xong một vòng 50 VIP thì thông báo chạy xong và thời gian chạy cho một vòng
    $time = time();
    $this_time = $time - NV_CURRENTTIME;
    $total_time = intval($run_time + $this_time);
    try {
        if ($number_user_scan < $number_user_scan_all) {
            $db->query('UPDATE nv4_vi_bidding_dau_gia_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ', status=1 where id =' . $logs_id);
        } else {
            $db->query('UPDATE nv4_vi_bidding_dau_gia_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ' where id =' . $logs_id);
        }
        if (sizeof($arr_id_vip)) {
            $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET filters_mail = ' . NV_CURRENTTIME . ' WHERE user_id IN (' . implode(',', $arr_id_vip) . ') AND vip=6');
        }
    } catch (PDOException $e) {
        file_put_contents($filters_mail_log, "Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP :\n" . print_r($e, true) . "\n", FILE_APPEND);
        notify_to_slack($filters_mail_log, "filters_mail_daugia_elastic.php Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP :\n" . print_r($e, true) . "\n");
        die();
    }
    $note = 'Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
    file_put_contents($filters_mail_log, $note . "\n\n", FILE_APPEND);
    die($note . "\n");
}

/**
 *
 * @param array $data
 * @return string
 */
function debugArray($data)
{
    return "<pre><code>" . print_r($data, true) . "</code></pre>";
}

