<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
// [detached from 2174788.pts-17.craws]

$filename = NV_ROOTDIR . '/tools/dauthau_plans_last_id_vi.txt';
if (file_exists($filename)) {
    $_last_id = intval(file_get_contents($filename));
} else {
    $_last_id = 0;
}

do {
    echo "_last_id_vi: " . $_last_id . "\n";
    $_last_id2 = $_last_id + 1000;

    $params = [];
    $sql = "SELECT id, code, title FROM `nv4_vi_bidding_plans` WHERE id > " . $_last_id . " AND id < " . $_last_id2 . " ORDER BY id ASC LIMIT 500";
    $_query = $db->query($sql);
    $array_alias = [];
    $_last_id = 0;
    while ($row = $_query->fetch()) {
        $_last_id = $row['id'];
        if ($_last_id > 1250500) {
            break;
        }
        $alias = nv_clean_alias($row['title'], 200);
        if (empty($alias)) {
            $alias = nv_clean_alias($row['code'], 200);
        }

        $params['body'][] = [
            'update' => [
                '_index' => 'dauthau_plans',
                '_id' => $row['id']
            ]
        ];
        $params['body'][] = [
            'doc' => [
                'alias' => $alias
            ]
        ];
        $array_alias[$row['id']] = $alias;
    }
    $_query->closeCursor();

    if (!empty($params['body'])) {
        $hosts = array(
            $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
        );

        $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
            ->setHosts($hosts)
            ->setRetries(0)
            ->build();
        $responses = $client->bulk($params)->asArray();
        if (!empty($responses['items'])) {
            foreach ($responses['items'] as $value) {
                if ($value['update']['status'] == 200) {
                    $_id = $value['update']['_id'];
                    $exec = $db->exec("UPDATE nv4_vi_bidding_plans SET alias = " . $db->quote($array_alias[$_id]) . " WHERE id=" . $_id);
                }
            }
        }
        file_put_contents($filename, $_last_id);
    }

    if (empty($_last_id)) {
        $_last_id = $_last_id2;
    }

    if ($_last_id > 1250500) {
        echo ('Thực hiện xong');
        break;
    }
} while ($_last_id > 0);

$filename = NV_ROOTDIR . '/tools/dauthau_plans_last_id_en.txt';
if (file_exists($filename)) {
    $_last_id = intval(file_get_contents($filename));
} else {
    $_last_id = 0;
}

do {
    echo "_last_id_en: " . $_last_id . "\n";
    $_last_id2 = $_last_id + 1000;

    $params = [];
    $sql = "SELECT id, code, title FROM `nv4_en_bidding_plans` WHERE id > " . $_last_id . " AND id < " . $_last_id2 . " ORDER BY id ASC LIMIT 500";
    $_query = $db->query($sql);
    $array_alias = [];
    $_last_id = 0;
    while ($row = $_query->fetch()) {
        $_last_id = $row['id'];
        if ($_last_id > 1250500) {
            break;
        }
        $alias = empty($row['title']) ? nv_clean_alias($row['code']) : nv_clean_alias($row['title'], 200);

        $params['body'][] = [
            'update' => [
                '_index' => 'en_dauthau_plans',
                '_id' => $row['id']
            ]
        ];
        $params['body'][] = [
            'doc' => [
                'alias' => $alias
            ]
        ];
        $array_alias[$row['id']] = $alias;
    }
    $_query->closeCursor();

    if (!empty($params['body'])) {
        $hosts = array(
            $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
        );

        $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
            ->setHosts($hosts)
            ->setRetries(0)
            ->build();
        $responses = $client->bulk($params)->asArray();
        if (!empty($responses['items'])) {
            foreach ($responses['items'] as $value) {
                if ($value['update']['status'] == 200) {
                    $_id = $value['update']['_id'];
                    $exec = $db->exec("UPDATE nv4_en_bidding_plans SET alias = " . $db->quote($array_alias[$_id]) . " WHERE id=" . $_id);
                }
            }
        }
        file_put_contents($filename, $_last_id);
    }

    if (empty($_last_id)) {
        $_last_id = $_last_id2;
    }
    if ($_last_id > 1250500) {
        echo ('Thực hiện xong');
        break;
    }
} while ($_last_id > 0);

die("Thực hiện xong");

function nv_clean_alias($string, $num = 110, $lower = true)
{
    $search = [
        '&amp;',
        '&#039;',
        '&quot;',
        '&lt;',
        '&gt;',
        '&#x005C;',
        '&#x002F;',
        '&#40;',
        '&#41;',
        '&#42;',
        '&#91;',
        '&#93;',
        '&#33;',
        '&#x3D;',
        '&#x23;',
        '&#x25;',
        '&#x5E;',
        '&#x3A;',
        '&#x7B;',
        '&#x7D;',
        '&#x60;',
        '&#x7E;'
    ];
    $replace = [
        '&',
        '\'',
        '"',
        '<',
        '>',
        '\\',
        '/',
        '(',
        ')',
        '*',
        '[',
        ']',
        '!',
        '=',
        '#',
        '%',
        '^',
        ':',
        '{',
        '}',
        '`',
        '~'
    ];

    $string = str_replace($search, $replace, $string);
    $string = nv_compound_unicode($string);
    $string = nv_EncString($string);
    $string = preg_replace(array(
        '/[^a-zA-Z0-9]/',
        '/[ ]+/',
        '/^[\-]+/',
        '/[\-]+$/'
    ), array(
        ' ',
        '-',
        '',
        ''
    ), $string);

    $len = strlen($string);
    if ($num and $len > $num) {
        $_substring = substr($string, 0, $num);
        while (str_contains($_substring, '-') and substr($string, $num, 1) != '-') {
            --$num;
            $_substring = substr($_substring, 0, $num);
        }
        $string = substr($string, 0, $num);
    }

    if ($lower) {
        $string = strtolower($string);
    }
    return $string;
}

die('đã thực hiện xong');
