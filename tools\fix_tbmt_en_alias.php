<?php

/**
 * @Project DauThau
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License commercial
 * @Createdate 26/03/2021, 10:36
 */
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';
// Tool fix alias của TBMT tiếng anh
try {
    do {
        $run = 0;
        $params = $array_alias = [];
        $result = $db->query('SELECT id, alias, goi_thau FROM nv4_en_bidding_row WHERE alias = "" LIMIT 100');
        while ($row = $result->fetch()) {
            $run++;
            print_r("Xử lý id: \033[33m" . $row['id'] . "\033[0m ");
            $_num = ($row['id'] > 1256896) ? 110 : 200;
            $alias = nv_clean_alias($row['goi_thau'], $_num);
            print_r($alias . PHP_EOL);

            $params['body'][] = [
                'update' => [
                    '_index' => 'en_dauthau_bidding',
                    '_id' => $row['id']
                ]
            ];
            $params['body'][] = [
                'doc' => [
                    'alias' => $alias
                ]
            ];
            $array_alias[$row['id']] = $alias;            
        }
        $result->closeCursor();
        if (!empty($params['body'])) {
            $hosts = array(
                $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
            );
    
            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();
            $responses = $client->bulk($params)->asArray();
            if (!empty($responses['items'])) {
                foreach ($responses['items'] as $value) {
                    if ($value['update']['status'] == 200) {
                        $_id = $value['update']['_id'];
                        $exec = $db->exec("UPDATE nv4_en_bidding_row SET alias = " . $db->quote($array_alias[$_id]) . " WHERE id=" . $_id);
                    }
                }
            }
        }
    } while ($run > 0);
} catch (Exception $e) {
    print_r($e);
    exit();
}

die("END\n");

function nv_clean_alias($string, $num = 110, $lower = true)
{
    $search = [
        '&amp;',
        '&#039;',
        '&quot;',
        '&lt;',
        '&gt;',
        '&#x005C;',
        '&#x002F;',
        '&#40;',
        '&#41;',
        '&#42;',
        '&#91;',
        '&#93;',
        '&#33;',
        '&#x3D;',
        '&#x23;',
        '&#x25;',
        '&#x5E;',
        '&#x3A;',
        '&#x7B;',
        '&#x7D;',
        '&#x60;',
        '&#x7E;'
    ];
    $replace = [
        '&',
        '\'',
        '"',
        '<',
        '>',
        '\\',
        '/',
        '(',
        ')',
        '*',
        '[',
        ']',
        '!',
        '=',
        '#',
        '%',
        '^',
        ':',
        '{',
        '}',
        '`',
        '~'
    ];

    $string = str_replace($search, $replace, $string);
    //$string = nv_compound_unicode($string);
    $string = vn_to_str($string);
    $string = preg_replace(array(
        '/[^a-zA-Z0-9]/',
        '/[ ]+/',
        '/^[\-]+/',
        '/[\-]+$/'
    ), array(
        ' ',
        '-',
        '',
        ''
    ), $string);

    $len = strlen($string);
    if ($num and $len > $num) {
        $_substring = substr($string, 0, $num);
        while (str_contains($_substring, '-') and substr($string, $num, 1) != '-') {
            --$num;
            $_substring = substr($_substring, 0, $num);
        }
        $string = substr($string, 0, $num);
    }

    if ($lower) {
        $string = strtolower($string);
    }
    return $string;
}

function vn_to_str($str)
{
    if (!$str)
        return false;
    $unicode = array(
        'a' => 'á|à|ả|ã|ạ|ă|ắ|ặ|ằ|ẳ|ẵ|â|ấ|ầ|ẩ|ẫ|ậ',
        'd' => 'đ',
        'e' => 'é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ',
        'i' => 'í|ì|ỉ|ĩ|ị',
        'o' => 'ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ',
        'u' => 'ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự',
        'y' => 'ý|ỳ|ỷ|ỹ|ỵ',
        'A' => 'Á|À|Ả|Ã|Ạ|Ă|Ắ|Ặ|Ằ|Ẳ|Ẵ|Â|Ấ|Ầ|Ẩ|Ẫ|Ậ',
        'D' => 'Đ',
        'E' => 'É|È|Ẻ|Ẽ|Ẹ|Ê|Ế|Ề|Ể|Ễ|Ệ',
        'I' => 'Í|Ì|Ỉ|Ĩ|Ị',
        'O' => 'Ó|Ò|Ỏ|Õ|Ọ|Ô|Ố|Ồ|Ổ|Ỗ|Ộ|Ơ|Ớ|Ờ|Ở|Ỡ|Ợ',
        'U' => 'Ú|Ù|Ủ|Ũ|Ụ|Ư|Ứ|Ừ|Ử|Ữ|Ự',
        'Y' => 'Ý|Ỳ|Ỷ|Ỹ|Ỵ'
    );
    foreach ($unicode as $nonUnicode => $uni) {
        $str = preg_replace("/($uni)/i", $nonUnicode, $str);
    }

    return $str;
}
