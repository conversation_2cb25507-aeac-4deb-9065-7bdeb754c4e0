<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */

// Gửi mail nhắc nhở nộp HSDT cho VIP1

if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';
require NV_ROOTDIR . '/functions/remind_theme.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$create_mail_file = NV_ROOTDIR . '/data/create_mail_remind_nop_hsdt_' . $prefix_lang . '_' . date('Ymd') . '.txt';

register_shutdown_function("fatal_handler");

function fatal_handler()
{
    global $create_mail_file;
    $error = error_get_last();
    if ($error !== NULL) {
        echo ('<pre><code>' . print_r($error, true) . '</code></pre>');
        file_put_contents($create_mail_file, '<pre><code>' . print_r($error, true) . '</code></pre>', FILE_APPEND);
    }
}

if (file_exists(NV_ROOTDIR . '/data/create_mail_remind_nop_hsdt_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "remind.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit("Chay trung nhau");
}

file_put_contents(NV_ROOTDIR . '/data/create_mail_remind_nop_hsdt_' . $prefix_lang . '.txt', NV_CURRENTTIME);
file_put_contents($create_mail_file, "======== Bắt đầu chạy: " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);

try {
    // Từ bảng tmp tổng hợp lại mail gửi đi cho từng VIP
    // Lấy danh sách các mail chưa gửi trong csdl để tiến hành gửi đi gom theo userid
    $time_start_thisday = mktime(0, 0, 0, date('n'), date('j'), date('Y'));
    $vips = array();
    $list_vipid = array();
    $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE userid IN (SELECT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE use_point != 2) AND send_status = 0 AND remind_hsdt = 1 LIMIT 20');
    while ($row = $query->fetch()) {
        $vips[$row['userid']] = array();
        $list_vipid[$row['userid']] = $row['userid'];
    }

    $list_vipid_all = implode(',', $list_vipid);
    if (!empty($list_vipid_all)) {
        $arr_vip = $db->query('SELECT id as cusid, phone, user_id as userid, email, sub_email, time_send, active_user FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id IN ( ' . $list_vipid_all . ') AND vip=1 AND prefix_lang = ' . $prefix_lang . '');

        while ($vip_data = $arr_vip->fetch()) {
            // nếu vip này bật chế độ gửi email theo tài khoản
            if ($vip_data['active_user'] == 1) {
                $arr_subemail = [];
                $query_permission = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_permission WHERE vipid=' . $vip_data['cusid'] . ' AND totime >' . NV_CURRENTTIME . ' AND send_mail = 1 AND prefix_lang = ' . $prefix_lang . '');
                while ($permission = $query_permission->fetch()) {
                    $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid = ' . $permission['userid'] . '');
                    while ($user = $arr_user->fetch()) {
                        $arr_subemail[$user['email']] = $user['email'];
                    }
                }
                $vip_data['sub_email'] = implode(',', $arr_subemail);
            }

            $vips[$vip_data['userid']] = $vip_data;
            $vips[$vip_data['userid']]['list_remind_bid'] = array();
            $vips[$vip_data['userid']]['is_vip'] = 1;
        }
    }
    // Nếu có dữ liệu danh sách user thì vào đây
    if (!empty($vips)) {
        // lấy username
        $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid IN ( ' . implode(',', array_keys($vips)) . ')');
        while ($user = $arr_user->fetch()) {
            $vips[$user['userid']]['username'] = $user['username'];
        }

        // Lấy ra tất cả tin đấu thầu trong danh sách gửi đi của tất cả các VIP
        // Lưu nội dung mail vào csdl
        foreach ($vips as $vip_id => $vip) {
            // Thử nghiệm gửi mail cho nội bộ công ty trước
            // if (!empty($vip)) {
                file_put_contents($create_mail_file, 'BEGIN: ' . $vip_id . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
                $arr_bid = array();
                $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row a INNER JOIN ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_detail b ON a.id=b.id WHERE a.id IN (SELECT bid_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE send_status = 0 AND remind_hsdt = 1 AND bid_id>0 AND userid=' . $vip_id . ')');
                while ($bid_row = $query->fetch()) {
                    $arr_bid[$bid_row['id']] = $bid_row;
                }

                $array_bid_id = array();
                $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE send_status = 0 AND remind_hsdt = 1 AND userid=' . $vip_id . ' LIMIT 50');
                while ($bid_data = $query->fetch()) {
                    $array_bid_id[$bid_data['id']] = $bid_data['id'];
                    $vips[$vip_id]['list_remind_bid'][] = $arr_bid[$bid_data['bid_id']]; // danh sách các tbmt cần nhắc nhở của mỗi user
                }

                $db->beginTransaction();

                try {
                    $remind_bids = $vips[$vip_id]['list_remind_bid'];
                    $data_insert = array();
                    $data_insert['addtime'] = NV_CURRENTTIME;
                    $data_insert['send_time'] = 0;
                    $data_insert['status'] = 0;
                    $arrInform = []; // khai báo để tránh lỗi
                    
                    // Gửi mail nhắc nhở nộp hồ sơ dự thầu

                    if (!empty($remind_bids)) {
                        $data_insert['title'] = $lang_module['title_remind'];
                        $data_insert['type'] = 1;
                        $data_insert['vip'] = 1;
                        $data_insert['content'] = nv_theme_remind_mail($remind_bids, $arrInform, $vip['username']);

                        try {
                            $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt1->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt1->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($vip['sub_email']));
                            $stmt1->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc1 = $stmt1->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (Exception $e) {
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "remind.php ERROR INSERT INTO mail 175: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                        }
                    }

                    echo "vip_id = " . $vip_id . "\n";
                    # Thực hiện lưu thông báo vào biến tạm
                    foreach ($arrInform as $k => $v) {
                        foreach ($v as $v1) {
                            $list_info[] = $v1;
                        }
                    }

                    // Reset lại biến này về rỗng để tiến hành lưu mảng dữ liệu tiếp theo
                    $arrInform = [];

                    // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                    if (!empty($array_bid_id) and !empty($_mailid)) {
                        $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_bid_id) . ') AND send_status = 0 AND remind_hsdt = 1');
                    }
                    $db->commit();

                    file_put_contents($create_mail_file, "number bid = " . sizeof($array_bid_id) . ": " . implode(',', $array_bid_id) . "\n", FILE_APPEND);
                    file_put_contents($create_mail_file, "END: " . $vip_id . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
                    // print_r($array_bid_id);
                } catch (Exception $e) {
                    file_put_contents($create_mail_file, 'rollBack ' . $vip_id . "\n\n", FILE_APPEND);
                    file_put_contents($create_mail_file, 'ERROR 390: ' . print_r($e, true) . "\n\n", FILE_APPEND);
                    $db->rollBack();
                    echo '<pre>';
                    print_r($e);
                }
            // }
        }
    }

    unlink(NV_ROOTDIR . '/data/create_mail_remind_nop_hsdt_' . $prefix_lang . '.txt');
} catch (\Throwable $th) {
    //throw $th;
}
