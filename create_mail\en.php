<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2018 VINADES.,JSC. All rights reserved
 * @License: Not free read more http://nukeviet.vn/vi/store/modules/nvtools/
 * @Createdate Tue, 16 Jan 2018 07:25:26 GMT
 */
$lang_module = array();
$lang_module['main'] = 'Main Page';
$lang_module['detail'] = 'Bidding Notice';
$lang_module['search'] = 'Search';
$lang_module['description'] = '%s, bid %s: %s';

// Lang for function main
$lang_module['add'] = 'Add New';
$lang_module['edit'] = 'Edit';
$lang_module['delete'] = 'Delete';
$lang_module['number'] = 'STT';
$lang_module['active_send_mail'] = 'Send mail';
$lang_module['search_title'] = 'Enter search keywords';
$lang_module['search_submit'] = 'Search';
$lang_module['so_tbmt'] = 'TBMT Number';
$lang_module['time_send'] = 'Email delivery cycle';
$lang_module['ngay_dang_tai'] = 'Posting time';
$lang_module['loai_thong_bao'] = 'Message type';
$lang_module['chu_dau_tu'] = 'Investor';
$lang_module['linh_vuc_thong_bao'] = 'Notification field';
$lang_module['hinh_thuc_thong_bao'] = 'Notification form';
$lang_module['goi_thau'] = 'Bid name';
$lang_module['phan_loai'] = 'Classified';
$lang_module['ten_du_an'] = 'Project name';
$lang_module['nguon_von'] = 'Capital';
$lang_module['ben_moi_thau'] = 'Bid solicitor';
$lang_module['hinh_thuc_lua_chon'] = 'Contractor selection form';
$lang_module['thoi_gian_ban_hsyc_tu'] = 'Time to sell HSYC word';
$lang_module['thoi_gian_nhan_hsdt_tu'] = 'Time to receive bid from';
$lang_module['dia_diem_mo_thau'] = 'Bid opening location';
$lang_module['gia_goi_thau'] = 'Establishment of the package';
$lang_module['so_tien_chu_gia_goi'] = 'Amount in words';
$lang_module['thoi_gian_ban_hsmt_tu'] = 'Time to sell HSMT from';
$lang_module['den_ngay'] = 'Bid closing time';
$lang_module['dia_diem'] = 'Receipt location';
$lang_module['dien_thoai'] = 'Contact phone';
$lang_module['gia_ban'] = 'Price';
$lang_module['thoi_diem_mo_thau'] = 'Bid opening time';
$lang_module['hinh_thuc_dam_bao'] = 'Guaranteed form';
$lang_module['hinh_thuc_nhan_hs'] = 'Bid form';
$lang_module['phuong_thuc'] = 'Method';
$lang_module['so_tien_dam_bao'] = 'Guaranteed amount';
$lang_module['so_tien_bang_chu'] = 'Amount in words';
$lang_module['thoi_gian_thuc_hien'] = 'Execution time';
$lang_module['noi_dung'] = 'Main content of the package';
$lang_module['ho_so'] = 'Bidding Documents';
$lang_module['note'] = 'Note';
$lang_module['mua_ho_so_moi_thau'] = 'Buy bidding documents';
$lang_module['phuong_thuc_hop_dong'] = 'Contract method';
$lang_module['kieu_lien_danh'] = 'Partnership type';
$lang_module['dia_diem_nhan_hsdt'] = 'Bid receiving location';
$lang_module['thoi_diem_ket_thuc_nop'] = 'Submission closing time';
$lang_module['hinh_thuc'] = 'Appearance';
$lang_module['dia_diem_nop_tien'] = 'Payment Location';
$lang_module['thong_bao_lien_quan'] = 'Related notice';
$lang_module['quyet_dinh_phe_duyet'] = 'Approval decision';
$lang_module['time_dong_thau'] = 'To date';
$lang_module['bidding_info'] = 'Details';
$lang_module['s_key'] = 'Keyword';
$lang_module['s_key2'] = 'Additional keywords';
$lang_module['note_key2'] = 'If this is declared, all keywords included in the entry will be required search conditions. You can use up to 5 keywords, separated by commas (,)';
$lang_module['from'] = 'From';
$lang_module['to'] = 'To';
$lang_module['find_type'] = 'Find by';
$lang_module['cat'] = 'Appearance';
$lang_module['field'] = 'Field';
$lang_module['search_alert'] = 'You need to select at least one search condition';
$lang_module['bid_security'] = 'Bid Security';
$lang_module['bid_openning'] = 'Bid opening';
$lang_module['bid_join'] = 'Bid';
$lang_module['dia_diem_thuc_hien_goi_thau'] = 'Location of bidding package';
$lang_module['modtitle_viplist'] = 'Registered VIP packages';

// Lang for function vip
$lang_module['vip'] = 'VIP Package';
$lang_module['user_id'] = 'User id';
$lang_module['from_time'] = 'From date';
$lang_module['to_time'] = 'To date';
$lang_module['parent_id'] = 'Parent Account';
$lang_module['child_id'] = 'Child account';
$lang_module['name'] = 'Company/Organization Name';
$lang_module['enlish_name'] = 'International transaction name';
$lang_module['tax'] = 'Tax ID';
$lang_module['phone'] = 'Phone number';
$lang_module['contact_phone'] = 'Phone number';
$lang_module['email'] = 'Mail received';
$lang_module['address_bill'] = 'Billing address';
$lang_module['require_bill'] = 'Invoice request';
$lang_module['contact_to'] = 'Full name';
$lang_module['contact_address'] = 'Address';
$lang_module['search_keys'] = 'Keywords';
$lang_module['province'] = 'Province(Place of Bidding)';
$lang_module['type_org'] = 'Enterprise Type';
$lang_module['money_bid'] = 'Secured Amount';
$lang_module['price_bid'] = 'Bid Price';
$lang_module['error_required_vip'] = 'Error: You have not selected a VIP package';
$lang_module['error_required_from_time'] = 'Error: you need to import data from date';
$lang_module['error_required_name'] = 'Error: you need to enter data for Personal/Organization Name';
$lang_module['error_required_phone'] = 'Error: you need to enter data for Registered Phone';
$lang_module['error_required_email'] = 'Error: you need to enter data for Mail subscription';
$lang_module['error_required_contact_to'] = 'Error: you need to enter data for Contact Name';
$lang_module['error_required_contact_address'] = 'Error: you need to enter data for Company/Organization Address';
$lang_module['error_required_search_keys'] = 'Error: you need to enter data for Keywords';
$lang_module['error_required_tax'] = 'Error: You need to enter a Tax ID for billing information';
$lang_module['main_mail_incorrect'] = 'Error: Invalid email address';
$lang_module['sub_mail_incorrect'] = 'Error: Invalid mail address %s';
$lang_module['save'] = 'Save changes';
$lang_module['vip_register'] = 'VIP registration';
$lang_module['vip1'] = 'VIP 1';
$lang_module['vip2'] = 'VIP 2';
$lang_module['vip3'] = 'VIP 3';
$lang_module['vip11'] = 'VIP 1 International';
$lang_module['vip21'] = 'VIP 2 International';
$lang_module['vip4'] = 'VIP 4';
$lang_module['vip5'] = 'VIP 5';
$lang_module['vip6'] = 'VIP 6';
$lang_module['vip7'] = 'VIP 7';
$lang_module['vip19'] = 'PRO 1';
$lang_module['vip99'] = 'VIEWEB';

$lang_module['register'] = 'Register';
$lang_module['vip_his_title'] = 'Purchase/renew history';
$lang_module['history_payment'] = 'View payment history';
$lang_module['filter_manage'] = 'Manage filters';
$lang_module['res_vip_info'] = 'Subscription information';
$lang_module['receipt_info'] = 'Invoice information';
$lang_module['contact_info'] = 'Contact info';
$lang_module['coppy_number'] = 'Copy phone number';

$lang_module['sub_email'] = 'Sub-mails';
$lang_module['sub_email_note'] = 'Sub-mails separated by commas \',\' and up to 2 emails';
$lang_module['captcha'] = 'Security Code';
$lang_module['resgister'] = 'Submit registration information';
$lang_module['error_time_select'] = 'Error: Registration time must count from current';
$lang_module['error_time'] = 'Error: Registration expiration time must be after registration time';
$lang_module['error_phone_require'] = 'Error: You need to enter a phone number';
$lang_module['error_phone_number'] = 'Phone number must be 10 to 11 characters';
$lang_module['numbers_year'] = 'Registration period';
$lang_module['error_required_numbers_year'] = 'Error: You need to enter registration time';
$lang_module['search_advance'] = 'Advanced Search';
$lang_module['search_simple'] = 'Basic Search';
$lang_module['without_key'] = 'No words';
$lang_module['money_bid_choose'] = 'Smaller guarantee amount';
$lang_module['show_info_notice'] = 'To view full information please <strong><a href="%s">Login</a></strong> or <strong><a href="%s ">Register</a></strong>';
$lang_module['show_vip_notice'] = 'For full information please <strong><a href="%s">upgrade to VIP account</a></strong>';
$lang_module['show_info_down'] = 'To download the file please <strong><a href="%s">Login</a></strong> or <strong><a href="%s">Login sign</a></strong>';
$lang_module['renewal'] = 'Your VIP account has expired, you can renew the VIP package to continue using the service';
$lang_module['vip_exist'] = 'You have already registered for the VIP package, please visit the VIP account management page to view details';
$lang_module['myvip'] = 'Manage VIP accounts';
$lang_module['note_payment'] = 'The VIP package you signed up for is in a waiting state for activation. Please complete the payment to activate the service';
$lang_module['login_require'] = 'You need to be logged in to use this function.';
$lang_module['register_sucsses'] = 'The system has recorded your registration information. Please complete the payment to activate the service.';
$lang_module['renewal_sucsses'] = 'The system has recorded your renewal information. Please complete the payment to update the service time.';

// Lang for function filters
$lang_module['filters'] = 'Filter';
$lang_module['userid'] = 'Token';
$lang_module['title'] = 'Filter name';
$lang_module['key'] = 'Keyword';
$lang_module['type'] = 'Search by posting/closed date';
$lang_module['moneybid'] = 'Security';
$lang_module['price_from'] = 'New price bid from';
$lang_module['price_to'] = 'To';
$lang_module['status'] = 'Status';
$lang_module['title_filters'] = 'Filter name';
$lang_module['save_filter'] = 'Save';
$lang_module['add_filter'] = 'Add new filter';
$lang_module['filter_title_require'] = 'Error: You need to enter a filter name';
$lang_module['over_filter'] = 'Sorry: The number of filters you created has exceeded the limit, please upgrade to VIP to be able to create unlimited filters. <a href="%s">VIP Sign Up</a>';
$lang_module['filter_create'] = 'You have not created any filters. Let\'s create filters to make finding information faster.';
$lang_module['filter_create_vip'] = 'You have not created any filters. Create filters at <a href=\"%s\">here</a> so we can find information for you';
$lang_module['full_filter'] = 'Notice: The number of filters you create has reached the limit, to be able to create more filters please register for the VIP package. <a href="%s">VIP Sign Up</a>';

$lang_module['history_payment_no'] = 'Unpaid';
$lang_module['history_payment_send'] = 'Payment sent';
$lang_module['history_payment_check'] = 'Paid, on hold';
$lang_module['history_payment_cancel'] = 'Refunded';
$lang_module['history_payment_yes'] = 'Paid, money paida';
$lang_module['payment'] = 'Payment';
$lang_module['new_customs'] = 'You have not signed up for the VIP package. Please register to use the services of VIP members';
$lang_module['error_data'] = 'Unfortunately the data you need to view may have been deleted or is not available on the system. Sincere apologies for the inconvenience!';
$lang_module['out_of_date'] = 'Notice: Your VIP account will expire on %s. After expiration, to continue using the service, please renew the service at <a href="%s">%s</a>, or contact us for help.';
$lang_module['new_vip'] = 'Announcement: Customers register for VIP package';
$lang_module['new_vip_body'] = 'Customer information and discount code';
$lang_module['renewal_vip'] = 'Notice: Customer renews VIP package';
$lang_module['renewal_vip_mess'] = 'Account %s has renewed its VIP package. Please check and confirm customer information';
$lang_module['note_key'] = 'You can use up to 5 keywords, separated by commas (,)';
$lang_module['time_find'] = 'Find because of search';
$lang_module['note_time_find'] = 'From 1 to 60 days';
$lang_module['error_time_find'] = 'Error: Search range is within 1-60 days only.';
$lang_module['full_filter_vip'] = 'Let\'s create a filter to filter information more conveniently. You can create up to ';
$lang_module['full_filter_vip1'] = '%d filter with VIP package 1';
$lang_module['full_filter_vip2'] = '%d filter with VIP package 2';
$lang_module['full_filter_vip11'] = '%d filter with VIP package 1 QT';
$lang_module['full_filter_vip21'] = '%d filter with VIP 2 QT package';
$lang_module['full_filter_vip5'] = '%d filter with VIP package 5';

$lang_module['err_filter_vip1'] = 'Notice: The number of filters you created for TBMT has exceeded the limit. Please go to <a href="%s">"Manage Filters"</a> and delete unused filters so you can add new ones.';
$lang_module['err_filter_vip2'] = 'Notice: The number of filters you created for KHLCNT has exceeded the limit. Please go to <a href="%s">"Manage Filters"</a> and delete unused filters so you can add new ones.';
$lang_module['err_filter_vip11'] = 'Notice: The number of filters you created for TBMT International has exceeded the limit. Please go to <a href="%s">"Manage Filters"</a> and delete unused filters so you can add new ones.';
$lang_module['err_filter_vip21'] = 'Notice: The number of filters you have created for the International KHLCNT has exceeded the limit. Please go to <a href="%s">"Manage Filters"</a> and delete unused filters so you can add new ones.';
$lang_module['full_key'] = 'Keyword, You need to enter from 1 -> 5 keywords';
$lang_module['full_key2'] = 'No more than 5 keyword combinations';
$lang_module['full_withoutkey'] = 'The number of excluded keywords does not exceed 5';
$lang_module['full_withoutkey'] = 'The number of excluded keywords does not exceed 5';
$lang_module['note_withoutkey'] = 'Exclude keywords separated by commas (,), up to 5 words';
$lang_module['empty_solicitor'] = 'No matches found, please choose another keyword';
$lang_module['empty_result'] = 'The requested results were not found, please extend the search time or change the search parameters in the right column';
$lang_module['send_time'] = 'Mail delivery cycle';
$lang_module['add_key_filter'] = 'The keyword <span class="red">%s</span> is not in the filter yet. <br>You can create a filter with this keyword to find it faster next time <a href="%s" class="red">here</a>.';
$lang_module['code_solicitor'] = 'Authorization Code';
$lang_module['title_solicitor'] = 'Name of bid solicitor';
$lang_module['list_bid_solicitor'] = 'View invitation to bid';
$lang_module['follow_sucess'] = 'The system has recorded the bid you are interested in.';
$lang_module['view_follow'] = 'The system will redirect to the following bidding page';
$lang_module['address'] = 'Address';
$lang_module['phone'] = 'Phone number';
$lang_module['aproval_time'] = 'Approval time';
$lang_module['so_khlcnt'] = 'KHLCNT Number';
$lang_module['so_tbmst'] = 'TBMST';
$lang_module['ten_du_an'] = 'Project name';
$lang_module['chu_dau_tu'] = 'Investor';
$lang_module['list_plan'] = 'Contractor selection plan';
$lang_module['plans'] = 'Details of contractor selection plan';
$lang_module['plan_title'] = 'Plan name';
$lang_module['approval_status'] = 'Decision status';
$lang_module['total_invest'] = 'Total investment';
$lang_module['approval_org'] = 'Approval Authority';
$lang_module['no_approval'] = 'No. of approval documents';
$lang_module['approval_date'] = 'Approval date';
$lang_module['code_contract'] = 'S/Tender Package';
$lang_module['type_choose_invest'] = 'NT selection form';
$lang_module['time_choose_invest'] = 'Time selection NT';
$lang_module['type_contract'] = 'Type of contract';
$lang_module['time_todo'] = 'Time to execute the activity';
$lang_module['price_contract'] = 'Bid price';
$lang_module['see_result'] = 'View filter results';
$lang_module['open_time'] = 'Bid opening time';
$lang_module['result'] = 'Contractor selection results';
$lang_module['type_bid'] = 'Bid form';
$lang_module['cat'] = 'Image\'bids';
$lang_module['finish_time'] = 'Time to complete';
$lang_module['bidder_name'] = 'Contractor name';
$lang_module['so_dkkd'] = 'Business registration number';
$lang_module['tender_price'] = 'Bid Price';
$lang_module['tender_title'] = 'Tender Notice';
$lang_module['win_price'] = 'Winning price';
$lang_module['point'] = 'Specification';
$lang_module['evaluating_price'] = 'Evaluating price';
$lang_module['reason'] = 'Reason for selecting contractor';
$lang_module['reason_title'] = 'Why';
$lang_module['document_approval'] = 'Decision on approval of bidding results';
$lang_module['document_test'] = 'Authorization Text';
$lang_module['report_result'] = 'Bid result report';
$lang_module['prequalification'] = 'Notice of invitation to prequalification';
$lang_module['status_notify'] = 'Notification Status';
$lang_module['time_post_pq'] = 'HSMST release time';
$lang_module['address_post_pq'] = 'Delivery and Receipt of HSMST';
$lang_module['time_open_pq'] = 'Time to open prequalification';
$lang_module['method_get_pq'] = 'HSMST mode';
$lang_module['pq_open_address'] = 'HSMST open location';
$lang_module['type_get_bidder'] = 'LCNT form';
$lang_module['method_bidder'] = 'LCNT Method';
$lang_module['content_pq'] = 'Main content of the package';
$lang_module['reason_fail'] = 'Reason for bid failure';
$lang_module['step_fail'] = 'Skip at what stage';
$lang_module['name_document'] = 'Text type name';
$lang_module['real_name'] = 'Filename';
$lang_module['download'] = 'Download file';
$lang_module['download_title'] = 'Download';
$lang_module['wating_active'] = 'Waiting for activation';
$lang_module['active'] = 'Active';
$lang_module['out_of_date'] = 'Expires';
$lang_module['filters_for'] = 'Apply mail to';
$lang_module['no_filters'] = 'You have not created any filters. Please <a href="%s">create filters</a> to make finding information faster next time.';
$lang_module['res_vip'] = 'Please <a href="%s">upgrade to a VIP account</a> to receive bids via email as configured in the filters.';
$lang_module['price_estimate'] = 'Estimated price';
$lang_module['date_caculate'] = 'Conversion date';
$lang_module['result_attach'] = 'Attach LCNT result message';
$lang_module['win_bidder'] = 'Winning bidder';
$lang_module['hinh_thuc_hop_dong'] = 'Contract form';
$lang_module['van_ban_phe_duyet'] = 'Text of approval';
$lang_module['other_current'] = 'Other currency';
$lang_module['same_cat'] = 'Same field';
$lang_module['same_solicitor'] = 'Invitation to bid with the solicitor';
$lang_module['see_more'] = 'See more';
$lang_module['plan_same_solicitor'] = 'Plan for selection of contractors and solicitors';
$lang_module['list_same_solicitor'] = 'Trust with the soliciting party';

$lang_module['promo_viprequire1'] = 'This promo code only applies to package <strong class="text-info">%s</strong> when included with package <strong class="text-info">%s </strong> (registered with or already VIP respectively)';
$lang_module['promo_viprequire2'] = 'This promo code only applies to package <strong class="text-info">%s</strong>';
$lang_module['promo_viprequire3'] = 'This promo code is only valid when included with the package <strong class="text-info">%s</strong> (registered with or already VIP respectively)';
$lang_module['promo_viprequire4'] = 'To receive this promo code you must pay from <strong class="text-info">%s year</strong> to <strong class="text-info">% s years</strong>';
$lang_module['promo_viprequire5'] = 'To get this promo code you must pay <strong class="text-info">%s year</strong>';
$lang_module['promo_viprequire6'] = 'Don\'t worry, just click continue and you can change the billing years on the next page';
$lang_module['vipstatus0'] = 'Suspended';
$lang_module['vipstatus1'] = 'Active';
$lang_module['vipstatus2'] = 'Expires';

$lang_module['log_promo_code'] = 'Promo code';
$lang_module['log_money'] = 'Total service charge';
$lang_module['log_discount'] = 'Reduced';
$lang_module['log_total'] = 'Total payment price';
$lang_module['log_cancel_order'] = 'Cancel order';
$lang_module['log_assign_order'] = 'Assign an order to an account';
$lang_module['log_renewal_years'] = 'Renewal years';
$lang_module['log_update_acc_info'] = 'Update VIP account information';
$lang_module['log_update_order_status'] = 'Update payment status';
$lang_module['log_update_order_statuso'] = 'Old Status';

$lang_module['hisorder'] = 'List of your orders';
$lang_module['hisorder_sa'] = 'All';
$lang_module['hisorder_sq'] = 'Unpaid';
$lang_module['hisorder_sc'] = 'Done';
$lang_module['hisorder_so'] = 'Other';
$lang_module['hisorder_pay_error_exp'] = 'This order has expired, so you cannot pay, please create another order';
$lang_module['hisorder_pay_error_status'] = 'This order is in the process of being locked for processing, you cannot continue to process the payment order. If you have completed the payment but the status is still not completed, please contact the staff for support';
$lang_module['hisorder_pay_error_complete'] = 'This order has already been paidn done, you don\'t need to make a payment anymore';
$lang_module['hisorder_back_list'] = 'Review Orders';
$lang_module['hisorder_back_vip'] = 'View your VIP packages';
$lang_module['hisorder_pay_success'] = 'Congratulations, your order has been successfully paid, your service plan has been activated';
$lang_module['hisorder_pay_unsuccess'] = 'Your order is in <strong>%s</strong>';

$lang_module['active_vip_sucess'] = 'Your VIP %s account has been activated';
$lang_module['new_vip_mess'] = 'Notice: Your %s VIP account has been successfully verified and activated.</br> The expiry date is from %s to %s.</br > For detailed information, please visit the link %s.</br> Thank you for using our service!';
$lang_module['renewal_vip_sucess'] = 'Your VIP %s account has been renewed';
$lang_module['renewal_vip_mess'] = 'Announcement: Your %s VIP account has been successfully renewed.</br> Expiry date is %s.</br> For details please visit link %s.</br> Thank you for using our service!';

$lang_module['cancel_time'] = 'Cancellation time';

$lang_module['listbusiness'] = "List of bidders that participated in the bid";
$lang_module['business'] = "Contractor";
$lang_module['STT'] = "STT";
$lang_module['goithau_total'] = 'Total number of packages';
$lang_module['goithau_trung'] = 'Number of winning bids';
$lang_module['goithau_truot'] = 'Number of bid slips';
$lang_module['goithau_chuaketqua'] = 'Number of bidding packages without results';
$lang_module['number_business'] = "<b>Summarization:</b> Procuring party %s is related to %s contractor.";
$lang_module['viewdetail'] = "View details";

$lang_module['view_result_client'] = 'Data table has been partially hidden, to view full information please <strong><a href="%s">Login</a></strong> or <strong><a href="%s">Register</a></strong>';
$lang_module['view_result_user'] = 'The data table has been partially hidden, to see the full analysis please <strong><a href="%s">subscribe to the VIP3 package</a></strong >';
$lang_module['view_result_vip'] = 'Full View <strong><a href="%s">here</a></strong>';
$lang_module['view_result_client_table'] = '<strong><a href="%s" title="To view full information please Login or Register">Login</a></strong> or < strong><a href="%s" title ="To see full information please Login or Register">Register</a></strong>';
$lang_module['view_result_user_table'] = '<strong><a href="%s" title="To see the full analysis please Subscribe to the VIP 3 package">subscribe to the VIP3 package</a></strong> ';
$lang_module['select_0'] = "---- Sort ----";
$lang_module['select_1'] = "Maximum number of winning bids";
$lang_module['select_2'] = "Least number of winning bids";
$lang_module['select_3'] = "Most failed bids";
$lang_module['select_4'] = "Least number of bids missed";

$lang_module['subemail_subject'] = 'Notice: activate email account on Tender system';
$lang_module['subemail_mess'] = 'Notice: To receive email information about bidding packages, please activate this email account by clicking on the link below: <br>%s <br> Thank you trusted to use our service!';

$lang_module['check_email_ok'] = 'Email activation successful! Your email will receive bidding information as soon as possible.';
$lang_module['check_email_err'] = 'Email activation failed. Please check your email again.';
$lang_module['redirect_to_main'] = 'Go to homepage';
$lang_module['click_active'] = 'Click to resend the authentication link';
$lang_module['email_active'] = 'Verified';
$lang_module['email_notactive'] = 'Not authenticated';
$lang_module['sendmail_ok'] = 'The system sent an email asking for verification to your email account. Please login to the email system to authenticate.';
$lang_module['sendmail_err'] = 'Send email failed. Please check your email again.';

$lang_module['num_business'] = "Number of contractors";
$lang_module['trang_thai'] = "Status";
$lang_module['listopen'] = "Bid opening result";
$lang_module['listopendetailt'] = "Result of bid opening:";
$lang_module['mua_hs'] = "Purchase Bidding Documents";
$lang_module['gia'] = "Bid price";
$lang_module['thoi_diem_hoan_thanh'] = "Time to complete";
$lang_module['danhgia_kt'] = "Technical evaluation";
$lang_module['danhgia_gia'] = "Evaluation";
$lang_module['ly_do_huy'] = "Cancellation reason";
$lang_module['phuongthuc'] = "Method";
$lang_module['id_loai'] = "Type";
$lang_module['ty_le'] = "Discount rate (%)";
$lang_module['gia_giam'] = "Bid price after discount (VND)";
$lang_module['hieu_luc_hs'] = "Validation of Bid (date)";
$lang_module['hieu_luc_hskt'] = "Effective Technical Proposal (date)";
$lang_module['hieu_luc_bd'] = "Effect of BDDT (date)";
$lang_module['type_info'] = 'Type info';
$lang_module['find_tbmt'] = 'Find bids';
$lang_module['find_khlcnt'] = 'Find a contractor selection plan';
$lang_module['find_plan'] = 'Find the PI';
$lang_module['date_title'] = 'Time';
$lang_module['content_title'] = 'Content';
$lang_module['number_money'] = 'Amount';
$lang_module['action_title'] = 'Action';
$lang_module['vip_update_title'] = 'Update package information';
$lang_module['res_vip_list'] = 'Registered VIP package list';
$lang_module['type_view_status'] = 'View by status';
$lang_module['view'] = 'View';
$lang_module['list_bidding'] = 'List of bids';
$lang_module['last_chance_time'] = 'Last changed';
$lang_module['to_title'] = 'to';
$lang_module['list_attach'] = 'List of attachments';
$lang_module['genergal_info'] = 'General Information';
$lang_module['crawl_time'] = 'Upload time';
$lang_module['num_plan'] = 'KHLCNT number';
$lang_module['name_plan'] = 'Personnel name';
$lang_module['solicitor_info'] = 'Proposer Information';
$lang_module['num_org'] = 'Authority Code';
$lang_module['fullname_org'] = 'Company name (full)';
$lang_module['sort_name'] = 'Abbreviated name';
$lang_module['eng_name'] = 'English name';
$lang_module['so_dkkd'] = 'Business registration number';
$lang_module['directly_under'] = 'Subject classification';
$lang_module['type_org_title'] = 'Type of agency';
$lang_module['province_org'] = 'Province/TP';
$lang_module['fax_num'] = 'Fax number';
$lang_module['website'] = 'Website';
$lang_module['sort_des'] = 'Bid brief description';
$lang_module['good_name'] = 'Goods name';
$lang_module['good_num'] = 'Quantity';
$lang_module['capacity'] = 'Capacity';
$lang_module['description_good'] = 'Basic features, specifications';
$lang_module['origin'] = 'Origin';
$lang_module['bid_price_win'] = 'Winning Price/Unit Price';
$lang_module['list_solicitor_win'] = 'List of successful bidders';
$lang_module['joint_venture'] = 'Joint name';
$lang_module['type_contract_title'] = 'Contract type';
$lang_module['list_fail'] = 'List of unsuccessful bidders';
$lang_module['bid_info'] = 'Bid information';
$lang_module['close_time'] = 'Bid closing date';
$lang_module['step_cancel'] = 'Cancellation phase';
$lang_module['cancel_info'] = 'Cancellation information';
$lang_module['number_document'] = 'Text number';
$lang_module['document_approval'] = 'Attach Decision';

$lang_module['static_solicitor'] = 'TOP 10 procuring entities that publish the most bidding results';
$lang_module['static'] = 'The results of reading analysis of data from the national bidding database for bid solicitors <b> %s:</b> are as follows: </br>- Done invite bid %s package, perform prequalification invitation %s package. </br>- Result of %s package announced, canceled bid %s package (among the packages above).</br>- There are %s packages with results without TBMT, TBMST.';
$lang_module['period_doan'] = 'Statistical period:';
$lang_module['select_total'] = 'Whole';
$lang_module['total'] = 'Total:';
$lang_module['static_solicitor_block'] = 'TOP of the procuring entity that publishes the most bidding results';
$lang_module['view_more'] = 'View more';

$lang_module['follow'] = 'Track bid information';
$lang_module['notification_follow_client'] = 'The tracking function of the bid package helps businesses to track the information, the software will send an email to notify the business as soon as the bidding package changes information, the business will also receive email notification of bidding results when the results are uploaded to the system.</br></br>This function is only available to members using VIP package 1. Please <a href="% s" ><b>login</b></a> to verify the right to use VIP service 1.</br></br>If you do not have a software account, please <a href=" %s" ><b>register</b></a> account (free) then register for VIP 1 package to use this feature.';
$lang_module['notification_follow_user'] = 'Bidding tracking function helps businesses track information, the software will send email notifications to businesses as soon as the bidding package changes information, businesses will also receive notify via email of bidding results when the results are uploaded to the system.</br></br>
This function is only available to members who are using VIP1 package. Please <a href="%s" ><b>register for VIP package 1</b></a> to use this feature.';
$lang_module['notification_follow_over'] = 'You have used up the maximum number of bids tracked.</br></br>
You can only track up to <b> %s </b>tender packages, please delete old packages to be able to track new packages.</br></br>
You can manage tracking bids <a href="%s" ><b>here</b></a>.';

$lang_module['notification_follow'] = 'The bid package has been marked as track!</br></br>
The software will send you an email to notify you as soon as the package number <b>[%s]</b> (named [%s], belonging to the project [%s]) changes information, the business will also receive email notification of bidding results when the results are uploaded to the system.</br></br>
You can track up to %s of bid packages, you can manage tracking packages <a href="%s" ><b>here</b></a>.';

$lang_module['notification_followed'] = 'You have already bookmarked this bid!</br></br>
You can track up to %s of bid packages, you can manage ongoing bidsfollow <a href="%s" ><b>here<b></a>.';
$lang_module['notification_func_follow'] = 'Bidding tracking function helps businesses track information, the software will send email notifications to businesses as soon as the bidding package changes information, businesses will also receive notify by email of the bidding results when the results are uploaded to the system.';
$lang_module['date_follow'] = 'Follow date';
$lang_module['link_follow_detail'] = 'Search for TBMTs.';
$lang_module['link_follow_result'] = 'Search for bidding results.';
$lang_module['result_follow'] = 'Tracking results';
$lang_module['follow_text'] = 'Enter number of TBMT to follow';
$lang_module['follow_button'] = 'Follow';
$lang_module['follow_search_noresult'] = 'No bids found.';
$lang_module['follow_search_result'] = '<a href="%s" >Found bid: %s named [%s]. Click to add to watchlist.</a>';

$lang_module['follow_plans'] = 'Follow contractor selection plan information';
$lang_module['notification_follow_client_plans'] = 'The contractor selection plan tracking function helps businesses to track information, the software will send email notifications to businesses as soon as the contractor selection plan changes. information, the enterprise will also receive an email notification when a bid invitation notice of the contractor selection plan is uploaded to the system.</br></br>This function is only for current members. Use the VIP 2 package. Please <a href="%s" ><b>login</b></a> to verify the right to use the VIP 2 service.</br></br>If you do not have a software account, please <a href="%s" ><b>register</b></a> an account (free) then register to use the VIP 2 package to use the feature. this ability.';
$lang_module['notification_follow_user_plans'] = 'The tracking function of contractor selection plan helps businesses to track information, the software will send email notifications to businesses as soon as the contractor selection plan changes. information, enterprises will also receive an email notification when a bid invitation notice of the contractor selection plan is uploaded to the system.</br></br>
This function is only available to members using VIP 2 package. Please <a href="%s" ><b>register for VIP 2 package</b></a> to use this feature. This.';
$lang_module['notification_follow_over_plans'] = 'You have exhausted the maximum number of tracked contractor selection plans.</br></br>
You can only follow up to <b> %s </b> contractor selection plans, please delete old contractor selection plans to be able to track new LLCs.</br></br >
You can manage the following clients <a href="%s" ><b>here</b></a>.';

$lang_module['notification_follow_plans'] = 'The contractor selection plan has been marked for follow!</br></br>
The software will send an email to notify you as soon as the contractor selection plan No. <b>[%s]</b> (named [%s], belonging to the project [%s]) changes information, enterprises will also receive an email notification when a bid invitation notice of the contractor selection plan is uploaded to the system.</br></br>
You can track up to %s of contractor selection plans, you can manage your following contractor selection plans <a href="%s" ><b>here</b></a>.';

$lang_module['notification_followed_plans'] = 'You have already bookmarked this contractor selection plan!</br></br>
You can track up to %s of contractor selection plans, you can manage your following contractor selection plans <a href="%s" ><b>here<b></a >.';
$lang_module['notification_func_follow_plans'] = 'The contractor selection plan tracking function helps businesses keep track of information, the software will send email notifications to businesses as soon as the contractor selection plan changes information, enterprises will also receive an email notification when a bid invitation notice of the contractor selection plan is uploaded to the system.';
$lang_module['link_follow_detail_plans'] = 'Looking for changes to the LCL.';
$lang_module['link_follow_result_plans'] = 'Search TBMT.';
$lang_module['link_follow_noresult_plans'] = 'No TBMT yet.';
$lang_module['follow_text_plans'] = 'Enter the number of customers to follow';
$lang_module['follow_search_noresult_plans'] = 'No KHLCNT found.';
$lang_module['follow_search_result_plans'] = '<a href="%s" >Found KHLCNT: %s named [%s]. Click to add to watchlist.</a>';

$lang_module['find_tbmtqt'] = 'Find International TBMT';
$lang_module['find_plansqt'] = 'Find the International Plans for International Development';
$lang_module['result_no_business'] = 'No successful bidder';
$lang_module['find_tbmtvt'] = 'Find other bids';

$lang_module['so_khlcdnt'] = 'Investment Plan Number';
$lang_module['dia_diem'] = 'Location';
$lang_module['gia_goi_thau'] = 'Bid price';
$lang_module['type_du_an'] = 'Project type';
$lang_module['total_cost'] = 'Total project cost';
$lang_module['quy_dat'] = 'Land fund';
$lang_module['post_time'] = 'Posting time';
$lang_module['date_results'] = 'Result input date';
$lang_module['reception_date'] = 'Preliminary opening date';

$lang_module['kqmt_status1'] = 'Bid opening completed';
$lang_modulee['kqmt_status2'] = 'Complete opening technical file';
$lang_module['kqmt_status3'] = 'Complete bid evaluation';
$lang_module['kqmt_status4'] = 'Finance opening completed';
$lang_module['kqmt_status5'] = 'Cancel bid';
$lang_module['kqmt_status6'] = 'Bid not open';

$lang_module['so_luong_nha_thau'] = 'Number of contractors';
$lang_module['filter_bid'] = 'Filter news';
$lang_module['limit_filter_bid'] = 'Note: DauThau.info aggregates %s of news, but because the system can only send 50 emails, you need to visit the website to view other news';
$lang_module['info'] = 'General Information';
$lang_module['notify_chance'] = 'Note: The bid you are interested in has changed, for changes please click View Details';
$lang_module['notify_old'] = 'Note: There is bid information older than this, for previous information, please click the "View Details" button and look at the "Related Notices" section. ';
$lang_module['view_detail'] = 'View details';
$lang_module['tbmt_change'] = 'Notify information change';
$lang_module['tbmt_cancel'] = 'Notice of bid cancellation';
$lang_module['tbmt_cancel_time'] = 'Cancellation time';
$lang_module['tbmt_cancel_reson'] = 'Cancellation reason';

$lang_module['mail_title_tbmt'] = 'Notice of Bidding News on %s';
$lang_module['mail_title_tbdg'] = 'Notice of auction News on %s';
$lang_module['mail_title_tblctcdg'] = 'Notice of Selecting auction organizer on %s';
$lang_module['with_filter_number'] = ' with filter number %s';
$lang_module['mail_footer_tbmt'] = "<br/>You have registered for the package <strong>%s</strong> and agree to receive bidding information via email from the website <strong>%s</strong> from date <strong>%s</strong>.<br/>If you do not want to receive mail, please log in to your <strong>%s</strong> account on the website and turn off the email sending function from the <a href filter =\"%s/filters/\"><strong>here</strong></a><hr/><br/>";
$lang_module['mail_title_khlcnt'] = 'Announcement: There is a contractor selection plan on %s';
$lang_module['mail_title_kqlcnt'] = 'Announcement: Result on contractor selection date %s';
$lang_module['mail_title_kqmt'] = 'Announcement: Bid opening results on %s';
$lang_module['mail_title_tbmt_update'] = 'Notice of date change/correction %s';
$lang_module['mail_title_tbmt_cancel'] = 'Notice of bid cancellation date %s';
$lang_module['mail_title_tbmtqt'] = 'Announcement of international bidding news on %s';
$lang_module['mail_title_tbmtvk'] = 'Announcement of new capital tenders with different date %s';
$lang_module['mail_title_khlcntqt'] = 'Announcement: There is an International Contractor Selection Plan on %s';

$lang_module['mail_title_ndt_tbmt'] = 'Announcement with information on invitation to bid for investors on %s';
$lang_module['mail_title_ndt_khlcnt'] = 'Announcement of investor selection plan on %s';
$lang_module['mail_title_ndt_kqlcnt'] = 'Announcement of investor selection results on %s';
$lang_module['mail_title_ndt_dmda'] = 'Announcement of the announcement of the project list on %s';
$lang_module['mail_title_ndt_tbmst'] = 'Announcement with notice of invitation for prequalification of investors on %s';
$lang_module['mail_title_ndt_kqst'] = 'Announcement of investor prequalification results on %s';
$lang_module['bid_result'] = 'Notify contractor selection results';
$lang_module['ndt_plans'] = 'Investor selection plan';
$lang_module['ndt_result'] = 'Investor selection results';
$lang_module['ndt_cbdmda'] = 'Publish project catalog';
$lang_module['ndt_kqst'] = 'Pre-qualification results';

$lang_module['mail_footer_follow'] = "<br/>You have registered to follow this bidding package from date %s on website %s. To stop receiving this email, log in to your account <strong>%s</strong > on the website and remove this bidding package from the watch list.";

$lang_module['mail_footer_follow_khclnt'] = "<br/>You have subscribed to follow this KHLCNT since %s on website %s. To stop receiving this email, log in to your account <strong>%s</strong> on the website and remove this KHLCNT from the watch list.";

$lang_module['DTPT'] = 'Development investment';
$lang_module['TX'] = 'Regular spending';
$lang_module['KHAC'] = 'Other';

$lang_module['bid_notify_status_1'] = 'Published';
$lang_module['bid_notify_status_2'] = 'Unpublished';
$lang_module['bid_notify_status_3'] = 'Canceled';
$lang_module['bid_notify_status_4'] = 'All';
$lang_module['dm_khlcnt_1'] = 'Investment and Development Spending';
$lang_module['dm_khlcnt_2'] = 'Mandatory spending';
$lang_module['dm_khlcnt_3'] = 'Other';

$lang_module['title_remind'] = 'Note: the deadline to submit bid documents for these bids is approaching.';
$lang_module['content_remind'] = 'You should prepare bid documents as our recommendations in this post: <a href="https://dauthau.asia/en/news/general-information/share-important-experiences-when-participating-in-online-bidding-in-2023-153.html" target="_blank">Share important experiences when participating in online bidding in 2023</a>';
$lang_module['business_winning_price'] = 'Bidders and winning bid prices';
$lang_module['title_business'] = 'List of winning bidders';
$lang_module['total_business_winning_price'] = 'Total winning bid price';

$lang_module['mail_title_extend'] = 'Notice of bid extension for package "%s" dated %s';
$lang_module['time_close_old'] = 'Old bid closing time';
$lang_module['time_close_new'] = 'New bid closing time';
$lang_module['time_open_old'] = 'Old bid opening time';
$lang_module['time_open_new'] = 'New bid opening time';
$lang_module['reason_extend'] = 'Reason for extension';
$lang_module['title_extend_tbmt'] = 'Notice of bid extension: %s';

$lang_module['mail_title_yclr'] = 'Notice of clarification request for bid package %s dated %s';
$lang_module['mailtitle_yclr'] = 'Notice of bid clarification request';
$lang_module['clarify_id'] = 'Clarification request ID';
$lang_module['req_name'] = 'Clarification request name';
$lang_module['clarify_content'] = 'Clarification request content';
$lang_module['clarify_response'] = 'Response content';
$lang_module['sign_req_date'] = 'Request time';
$lang_module['sign_res_date'] = 'Response time';
$lang_module['title_yclr_tbmt'] = 'Notice of clarification request: %s';

$lang_module['mail_title_cancel'] = 'Notice of bid cancellation for package "%s" dated %s';
$lang_module['mail_titlecancel'] = 'Notice of bid package cancellation';
$lang_module['cancel_id'] = 'Cancellation decision number';
$lang_module['decision_date'] = 'Decision date';
$lang_module['reason'] = 'Reason for cancellation';
$lang_module['title_cancel_tbmt'] = 'Notice of bid cancellation: %s';

$lang_module['reason_ttqdpl'] = "All bid submissions and proposals do not meet the requirements of the bidding documents and request documents.";
$lang_module['reason_sptdt'] = "Change in investment objectives and scope as stated in the bidding documents and request documents.";
$lang_module['reason_pvdt'] = "Evidence of bribery, bid rigging, fraud, abuse of position and power to illegally interfere with bidding activities leading to distortion of contractor and investor selection results.";
$lang_module['reason_ct'] = "Organizations and individuals other than the winning bidder perform prohibited acts specified in Article 16 of the Bidding Law No. 22/2023/QH15 leading to distortion of contractor selection results";
$lang_module['reason_ntct'] = "The winning bidder performs prohibited acts specified in Article 16 of the Bidding Law No. 22/2023/QH15";
$lang_module['reason_kdun'] = "All expressions of interest, prequalification applications, bids, and proposals do not meet the requirements of the invitation documents, prequalification documents, bidding documents, and request documents";
$lang_module['reason_tdmt'] = "Changes in investment objectives and scope in the approved investment decision that change the work volume and evaluation criteria stated in the invitation documents, prequalification documents, bidding documents, and request documents";
$lang_module['reason_hsmt'] = "Expression of interest documents, prequalification documents, bidding documents, and request documents do not comply with the provisions of the Bidding Law No. 22/2023/QH15 and other relevant laws leading to selected contractors not meeting requirements to perform the bid package";
$lang_module['reason_cptpp_ttqdpl'] = "All bids and proposals do not meet the basic requirements of the bidding documents and request documents. In case all bids do not meet the basic requirements of the bidding documents, direct appointment is applied according to point i of the clause";
$lang_module['reason_cptpp_sptdt'] = "Changes in investment objectives and scope approved in the feasibility study report, Investment Decision affecting the bidding documents.";
$lang_module['reason_cptpp_kdu'] = "Bidding documents and request documents do not comply with bidding law regulations or other relevant laws leading to selected contractors not meeting requirements to perform the bid package, project; bidding documents, request documents or organization";
$lang_module['reason_cptpp_pvdt'] = "Evidence of bribery, bid rigging, fraud, abuse of position and power to illegally interfere with bidding activities leading to distortion of contractor selection results.";
$lang_module['reason_vk_kdu'] = "All bids and proposals do not meet the requirements of the bidding documents and request documents.";
$lang_module['reason_vk_pvdt'] = "Change in investment objectives and scope as stated in the bidding documents and request documents";
$lang_module['reason_vk_ttqdpl'] = "Bidding documents and request documents do not comply with bidding law regulations or other relevant laws leading to selected contractors and investors not meeting requirements to perform the bid package, project.";
$lang_module['reason_vk_sptdt'] = "Evidence of bribery, bid rigging, fraud, abuse of position and power to illegally interfere with bidding activities leading to distortion of contractor and investor selection results.";
$lang_module['reason_vk_ct'] = "Organizations and individuals other than the winning bidder perform prohibited acts specified in Article 16 of the Bidding Law No. 22/2023/QH15 leading to distortion of contractor selection results";
$lang_module['reason_vk_ntct'] = "The winning bidder performs prohibited acts specified in Article 16 of the Bidding Law No. 22/2023/QH15";
$lang_module['reason_vk_kdun'] = "All expressions of interest, prequalification applications, bids, and proposals do not meet the requirements of the invitation documents, prequalification documents, bidding documents, and request documents";
$lang_module['reason_vk_tdmt'] = "Changes in investment objectives and scope in the approved investment decision that change the work volume and evaluation criteria stated in the invitation documents, prequalification documents, bidding documents, and request documents";
$lang_module['reason_vk_hsmt'] = "Expression of interest documents, prequalification documents, bidding documents, and request documents do not comply with the provisions of the Bidding Law No. 22/2023/QH15 and other relevant laws leading to selected contractors not meeting requirements to perform the bid package";
$lang_module['reason_vk_thk'] = "Other cases";

// Language variables for petitions
$lang_module['mail_title_petition'] = 'Notice of petition for bid package "%s"';
$lang_module['petition_name'] = 'Petition number';
$lang_module['petition_content'] = 'Petition content';
$lang_module['petition_response'] = 'Response content';
$lang_module['sign_req_date'] = 'Submission time';
$lang_module['sign_res_date'] = 'Response time';
$lang_module['title_petition_tbmt'] = 'Notice of petition: %s';

// Language variables for tbmt_follow_petition.tpl
$lang_module['petition_title'] = 'Notice of petition for TBMT';
$lang_module['so_tbmt'] = 'TBMT number';
$lang_module['ben_moi_thau'] = 'Bid solicitor';
$lang_module['goi_thau'] = 'Bid package';
$lang_module['petition_number'] = 'Petition number';
$lang_module['petition_content'] = 'Petition content';
$lang_module['petition_time'] = 'Petition submission time';
$lang_module['petition_response'] = 'Response content';
$lang_module['petition_response_time'] = 'Response time';
$lang_module['petition_additional_response'] = 'Additional response content';
$lang_module['petition_additional_time'] = 'Additional response time';
$lang_module['view_detail'] = 'View details';

// Email titles by petition type
$lang_module['title_new_petition'] = 'Notice of new petition: %s';
$lang_module['title_petition_response'] = 'Notice of petition response: %s';
$lang_module['title_additional_response'] = 'Notice of additional petition response: %s';

$lang_module['with_filter'] = ' with filter %s';
$lang_module['mail_title_tbmt_split'] = 'Notice of Bidding News on %s';
$lang_module['mail_title_khlcnt_split'] = 'Notice of Contractor Selection Plan on %s';
$lang_module['mail_title_kqlcnt_split'] = 'Notice of Contractor Selection Result on %s';
$lang_module['mail_title_khlcntqt_split'] = 'Notice of International Contractor Selection Plan on %s';
$lang_module['mail_title_kqmt_split'] = 'Notice of Bid Opening Result on %s';
$lang_module['mail_title_tbmtqt_split'] = 'Notice of International Bidding News on %s';
$lang_module['mail_title_tbmtvk_split'] = 'Notice of Bidding News in Foreign Country on %s';
$lang_module['mail_title_ycbg_split'] = 'Notice of Request for Quotation on %s';
$lang_module['reoffer_result'] = 'Online bidding result';
$lang_module['reoffer_room'] = 'Online bidding room';
