<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
set_time_limit(0);

$id_tbmt = 0;
if (file_exists(NV_ROOTDIR . '/tools/update_content_notify_no_TBMT.txt')) {
    $id_tbmt = file_get_contents(NV_ROOTDIR . '/tools/update_content_notify_no_TBMT.txt');
    $id_tbmt = intval($id_tbmt);
}
try {

    $waitTimeoutInSeconds = 2;
    if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
        // It worked
        $elastic_online = 1;
    } else {
        // It didn't work
        $elastic_online = 0;
        echo "Server Elasticsearch didn't work: ";
        echo "ERROR: $errCode - $errStr<br />\n";
    }
    fclose($fp);
    if ($elastic_online) {
        $hosts = array(
            $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
        );
        do {
            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();

            $query_url = $db->query('SELECT id, so_tbmt, ngay_dang_tai, linh_vuc_thong_bao, pham_vi, goi_thau, ben_moi_thau, den_ngay, content, content_full, notify_chance_time, price, money_bid, type_org, solicitor_id, type_bid, get_ho_so, time_ho_so, get_time, fget_time, upcount, totalview, sourceurl, id_hinhthucluachon, hinh_thuc_lua_chon, hinh_thuc_thong_bao, elasticsearch, is_vip5, exported_sitemap, province_id, phanmuc, is_aita, notify_no FROM nv4_vi_bidding_row WHERE id > ' . $id_tbmt . ' ORDER BY id ASC LIMIT 100');
            $params = [
                'body' => []
            ];
            $id_tbmt = 0;
            while ($row = $query_url->fetch()) {
                $id_tbmt = $row['id'];

                if (!empty($row['notify_no'])) {
                    $row['content_full'] = str_replace($row['notify_no'], '', $row['content_full']);
                    $row['content_full'] .= ' ' . $row['notify_no'];

                    $row['content'] = change_alias($row['content_full']); // Không có dấu tiếng việt
                    $row['content'] = str_replace('-', ' ', $row['content']);

                    $db->query('UPDATE nv4_vi_bidding_row SET content = ' . $db->quote($row['content']) . ', content_full = ' . $db->quote($row['content_full']) . ', notify_no=' . $db->quote($row['notify_no']) . ' WHERE id =' . $row['id']);

                    $params['body'][] = [
                        'index' => [
                            '_index' => 'dauthau_bidding',
                            // '_type' => 'nv4_vi_bidding_row',
                            '_id' => $row['id']
                        ]
                    ];
                    $params['body'][] = $row;
                    echo $row['notify_no'] . " --> update\n";
                } else {
                    echo $row['notify_no'] . " --> no mã gói thầu\n";
                }
            }
            $query_url->closeCursor();

            if (!empty($params['body'])) {
                $responses = $client->bulk($params)->asArray();
                if (empty($responses['errors'])) {
                    unset($responses['items']);
                    echo "update thành công<br><br>\n";
                } else {
                    echo '<pre>';
                    print_r($responses);
                    echo '</pre>';
                }
            } else {
                echo 'Ko co du du lieu';
            }
            if ($id_tbmt > 0) {
                file_put_contents(NV_ROOTDIR . '/tools/update_content_notify_no_TBMT.txt', $id_tbmt);
            }
        } while ($id_tbmt > 0);
    }
} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
}
echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
