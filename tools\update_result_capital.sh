#!/bin/bash

SOURCE="${BASH_SOURCE[0]}"
while [ -h "$SOURCE" ]; do
  TARGET="$(readlink "$SOURCE")"
  if [[ $TARGET == /* ]]; then
    SOURCE="$TARGET"
  else
    DIR="$( dirname "$SOURCE" )"
    SOURCE="$DIR/$TARGET"
  fi
done
DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
DIR_PATH=$PWD

echo '---------------- Bắt đầu chạy tool ----------------'
echo ''

while : ; do
  basetime=$(date +%s%N)
  php $DIR_PATH/update_result_capital.php
  code=$?
  if [[ $code == 1 ]]; then
    echo "Đã hoàn tất VN"
    break
  fi
  echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds"
  sleep 1
done

while : ; do
  basetime=$(date +%s%N)
  php $DIR_PATH/update_result_capital.php  --site_lang=en
  code=$?
  if [[ $code == 1 ]]; then
    echo "Đã hoàn tất EN"
    break
  fi
  echo "$(echo "scale=4;($(date +%s%N) - ${basetime})/(1*10^09)" | bc) seconds"
  sleep 1
done
