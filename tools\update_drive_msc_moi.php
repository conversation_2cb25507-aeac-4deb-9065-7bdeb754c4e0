<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';

$minid = 0;
if (file_exists(NV_ROOTDIR . '/tools/update_drive_msc_moi.txt')) {
    $minid = file_get_contents(NV_ROOTDIR . '/tools/update_drive_msc_moi.txt');
    $minid = intval($minid);
}
$newid = $minid;

try {
    $sql = "SELECT id, google_drive, filesize_detail FROM nv4_bidding_drive_new WHERE id > " . $minid . " ORDER BY id ASC LIMIT 1000";
    $sql = "SELECT id, google_drive, filesize_detail FROM nv4_bidding_drive_new WHERE sync_drive=0 ORDER BY id ASC LIMIT 1000";
    $query = $db->query($sql);
    $arr_drive = $query->fetchAll();
    $arr_id = array_column($arr_drive, 'id');
    if (empty($arr_id)) {
        echo "Đã chạy hết!!";
        exit(1);
    }
    $hs_arr_name_file = $qd_arr_name_file = [];
    $arr_row = $db->query('SELECT id, ho_so_download, quyet_dinh_download FROM nv4_vi_bidding_detail WHERE id IN(' . implode(',', $arr_id) . ')');
    while ($_row = $arr_row->fetch()) {
        $ho_so_array = json_decode($_row['ho_so_download'], true);
        foreach ($ho_so_array as $hoso_p) {
            ksort($hoso_p);
            foreach ($hoso_p as $key => $hoso) {
                if ($key != -1) {
                    if (!empty($hoso['arr_file'])) {
                        foreach ($hoso['arr_file'] as $_file) {
                            $hs_arr_name_file[$_file['fileId']] = $_file['fileName'];
                        }
                    }
                }
            }
        }

        $qd_array = json_decode($_row['quyet_dinh_download'], true);
        foreach ($qd_array as $qd_p) {
            ksort($qd_p);
            foreach ($qd_p as $key => $qd) {
                if ($key != -1) {
                    if (!empty($qd['arr_file'])) {
                        foreach ($qd['arr_file'] as $_file) {
                            $qd_arr_name_file[$_file['fileId']] = $_file['fileName'];
                        }
                    }
                }
            }
        }
    }
    $arr_bid_id = [];
    if (!empty($hs_arr_name_file) || !empty($qd_arr_name_file)) {

        $arr_sql = [];
        foreach ($arr_drive as $drive) {
            $newid = $drive['id'];
            $google_drive = [];
            $_arr_file = explode('@', $drive['google_drive']);
            foreach ($_arr_file as $v) {
                if (!empty($v)) {
                    $file = explode('	', $v);
                    $google_drive[$file[0]] = $file[1];
                }
            }
            $filesize_detail = json_decode($drive['filesize_detail'], true);
            foreach ($google_drive as $id_file_msc => $url) {
                if (!empty($url)) {
                    $type = 0;
                    if (!empty($hs_arr_name_file[$id_file_msc])) {
                        $title = $hs_arr_name_file[$id_file_msc];
                        $type = 1;
                    } elseif (!empty($qd_arr_name_file[$id_file_msc])) {
                        $title = $qd_arr_name_file[$id_file_msc];
                        $type = 2;
                    }
                    if ($type > 0) {
                        $arr_bid_id[] = $drive['id'];
                        $arr_sql[] = '(' . $db->quote($drive['id']) . ',' . $db->quote($title) . ',' . $db->quote($url) . ',' . $db->quote($filesize_detail[$id_file_msc]) . ',' . $db->quote($id_file_msc) . ',' . $type . ')';
                    }
                }
            }
        }

        if (!empty($arr_sql)) {
            $db->exec('DELETE FROM nv4_bidding_drive_tbmt WHERE bid_id IN(' . implode(',', $arr_bid_id) . ')');
            $_sql = "INSERT IGNORE INTO `nv4_bidding_drive_tbmt`(`bid_id`, `title`, `url`, `filesize`, `id_file_msc`, `type`) VALUES " . implode(',', $arr_sql);
            $db->exec($_sql);

            $db->exec('UPDATE nv4_vi_bidding_detail SET sync_drive = ' . NV_CURRENTTIME . ' WHERE id IN(' . implode(',', $arr_bid_id) . ')');
            $db->exec('UPDATE nv4_bidding_drive_new SET sync_drive = ' . NV_CURRENTTIME . ' WHERE id IN(' . implode(',', $arr_bid_id) . ')');
        }
    }
} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
}
if ($newid > $minid) {
    file_put_contents(NV_ROOTDIR . '/tools/update_drive_msc_moi.txt', $newid);
}
echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
