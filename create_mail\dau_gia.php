<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$create_mail_file = NV_ROOTDIR . '/data/create_mail_dau_gia_' . date('Ymd') . '.txt';
if (file_exists(NV_ROOTDIR . '/data/create_mail_dau_gia.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    exit('Chay trung nhau');
}
file_put_contents(NV_ROOTDIR . '/data/create_mail_dau_gia.txt', NV_CURRENTTIME);

try {
    // Từ bảng tmp tổng hợp lại mail gửi đi cho từng VIP
    // Lấy danh sách các mail chưa gửi trong csdl để tiến hành gửi đi gom theo userid
    $vips = array();
    $list_vipid = array();
    $query = $db->query('SELECT DISTINCT userid FROM nv4_vi_bidding_dau_gia_id WHERE userid IN (SELECT user_id FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE ((' . NV_CURRENTTIME . ' - last_email) >= time_send*3600) AND vip=6 AND status = 1 ORDER BY last_email ASC) AND send_status = 0 LIMIT 20');
    while ($row = $query->fetch()) {
        $vips[$row['userid']] = array();
        $list_vipid[$row['userid']] = $row['userid'];
    }

    if (!empty($list_vipid)) {
        $arr_vip = $db->query('SELECT id as cusid, phone, user_id as userid, email, sub_email, time_send, last_email, from_time FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id IN ( ' . implode(',', $list_vipid) . ') AND vip=6 AND status = 1');
        while ($vip_data = $arr_vip->fetch()) {
            $vips[$vip_data['userid']] = $vip_data;
            $vips[$vip_data['userid']]['filter'] = array();
            $vips[$vip_data['userid']]['bid'] = array();
            $vips[$vip_data['userid']]['select'] = array();
        }
    }

    if (!empty($vips)) {
        // lấy username
        $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid IN ( ' . implode(',', array_keys($vips)) . ')');
        while ($user = $arr_user->fetch()) {
            $vips[$user['userid']]['username'] = $user['username'];
        }

        // Lấy ra tất cả tin đấu thầu trong danh sách gửi đi của tất cả các VIP
        // Lưu nội dung mail vào csdl
        foreach ($vips as $vip_id => $vip) {
            file_put_contents($create_mail_file, 'BEGIN: ' . $vip_id . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
            $arr_bid = array();
            $query = $db->query('SELECT * FROM nv4_dau_gia_bid WHERE id_bid IN (SELECT bid_id FROM `nv4_vi_bidding_dau_gia_id` WHERE send_status = 0 AND bid_id>0 AND userid=' . $vip_id . ')');
            while ($bid_row = $query->fetch()) {
                $arr_bid[$bid_row['id_bid']] = $bid_row;
            }
            $arr_select = array();
            $query = $db->query('SELECT * FROM nv4_dau_gia_bid_select WHERE id_bid IN (SELECT select_id FROM `nv4_vi_bidding_dau_gia_id` WHERE send_status = 0 AND select_id>0 AND userid=' . $vip_id . ')');
            while ($plans_row = $query->fetch()) {
                $arr_select[$plans_row['id_bid']] = $plans_row;
            }

            // Gộp nhóm những mail chung chủ đề lại theo từng VIP
            $array_bid_id = array();
            $query = $db->query('SELECT * FROM `nv4_vi_bidding_dau_gia_id` WHERE send_status = 0 AND userid=' . $vip_id . ' LIMIT 50');
            while ($data = $query->fetch()) {
                $array_bid_id[$data['id']] = $data['id'];
                if ($data['filter_id'] > 0) {
                    if ($data['bid_id'] > 0 and isset($arr_bid[$data['bid_id']])) {
                        $vip['filter']['list_bid'][$data['filter_id']][] = $arr_bid[$data['bid_id']];
                    }
                    if ($data['select_id'] > 0 and isset($arr_select[$data['select_id']])) {
                        $vip['filter']['list_select'][$data['filter_id']][] = $arr_select[$data['select_id']];
                    }
                    $vip['bid'][] = $data['bid_id'];
                    $vip['select'][] = $data['select_id'];
                }
            }

            // Cập nhật lại lần cuối cùng gửi mail cho các tài khoản VIP, Phần này cần chạy trước beg-----inTransaction để nếu có lỗi thì bỏ qua 1 tài khảo.
            if (sizeof($array_bid_id) < 50) {
                // Nếu 1 lần tổng hợp có > 50 thì không cập nhật ngay last_email để 1 phút sau sẽ chạy tiếp
                $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET last_email = ' . NV_CURRENTTIME . ' WHERE id =' . $vip['cusid'] . ' AND vip = 6');
            } else {
                file_put_contents($create_mail_file, "Con chay nua: " . sizeof($array_bid_id) . " / " . sizeof($arr_bid) . "\n", FILE_APPEND);
            }
            $db->beginTransaction();
            try {
                $data_insert = array();
                $data_insert['addtime'] = NV_CURRENTTIME;
                $data_insert['send_time'] = 0;
                $data_insert['status'] = 0;

                // Gửi mail thông báo mới thầu, kế hoạch chọn nhà thầu, thông báo sơ tuyển nhóm theo từng bộ lọc
                if (!empty($vip['filter'])) {
                    foreach ($vip['filter'] as $key_type => $filter) {
                        if ($key_type == 'list_bid') {
                            $data_insert['title'] = sprintf($lang_module['mail_title_tbdg'], date('d/m/Y'));
                            $data_insert['content'] = nv_theme_bidding_mail($filter, $vip_id);
                        } else {
                            $data_insert['title'] = sprintf($lang_module['mail_title_tblctcdg'], date('d/m/Y'));
                            $data_insert['content'] = nv_theme_mail_plans($filter, $vip_id);
                        }
                        $data_insert['vip'] = 6;
                        $data_insert['content'] .= sprintf($lang_module['mail_footer_tbmt'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date('d/m/Y', $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');

                        $data_insert['type'] = 0;

                        // Nội dung htm sẽ gửi cho từng khách
                        try {
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '')");
                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
                            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $exc = $stmt->execute();
                            $_mailid = $db->lastInsertId();
                        } catch (PDOException $e) {
                            print_r('Lỗi thêm mail tin mới vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            trigger_error($e->getMessage());
                            echo ($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }
                }
                echo "vip_id = " . $vip_id . "\n";
                // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                if (!empty($array_bid_id)) {
                    $db->query('UPDATE nv4_vi_bidding_dau_gia_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_bid_id) . ') AND send_status = 0');
                }
                $db->commit();
                file_put_contents($create_mail_file, "number bid = " . sizeof($array_bid_id) . ": " . implode(',', $array_bid_id) . "\n", FILE_APPEND);
                file_put_contents($create_mail_file, "END: " . $vip_id . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
            } catch (PDOException $e) {
                file_put_contents($create_mail_file, 'rollBack: ' . $vip_id . "\n\n", FILE_APPEND);
                $db->rollBack();
                echo '<pre>';
                print_r($e);
                echo '</pre>';
            }
        }
    }

    unlink(NV_ROOTDIR . '/data/create_mail_dau_gia.txt');
} catch (PDOException $e) {
    file_put_contents($create_mail_file, 'ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
}
echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function nv_theme_bidding_mail($array_data, $vip_id)
{
    global $db, $create_mail_file, $site_lang;

    if (!empty($array_data)) {
        $xtpl = new XTemplate('template_email_dau_gia.tpl', NV_ROOTDIR . '/create_mail');
        include NV_ROOTDIR . '/create_mail/vi.php';
        $xtpl->assign('LANG', $lang_module);
        foreach ($array_data as $filter_id => $arr_list) {
            // Lấy tên bộ lọc
            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();
            // link sửa bộ lọc
            $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
            // ghi log bộ lọc
            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);
            $xtpl->assign('FILTER_NAME', $filter_info['title']);

            if (!empty($arr_list)) {
                if (sizeof($arr_list) > 50) {
                    $xtpl->assign('FILTER_NUMBER', number_format(sizeof($arr_list)));
                    $xtpl->parse('main.filter.number');
                }

                file_put_contents($create_mail_file, 'list_bid: ' . sizeof($arr_list) . "\n", FILE_APPEND);
                $i_break = 0;
                foreach ($arr_list as $_data) {
                    ++$i_break;
                    if ($i_break > 50) {
                        break;
                    }
                    $_data['title_a'] = nv_htmlspecialchars($_data['title']);

                    // Bôi vàng những từ trùng với từ khóa tìm kiếm, tìm kiếm tuyệt đối
                    $arr_key = explode(',', $filter_info['key_search']);

                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $_data['title'] = BoldKeywordInStr(strip_tags($_data['title']), $key);
                    }
                    $_data['opening_bid'] = nv_date('H:i d/m/y', $_data['opening_bid']);
                    $_data['date_bid'] = nv_date('H:i d/m/y', $_data['date_bid']);
                    $_data['link_view'] = DAUGIA_DOMAIN . '/vi/' . $_data['alias'] . '-' . $_data['id_bid'] . '.html';

                    $bidder_info = $db->query('SELECT * FROM nv4_dau_gia_bidder WHERE id_bidder=' . $_data['id_bidder'])->fetch();
                    if (!empty($bidder_info)) {
                        $_data['bidder'] = $bidder_info['name_bidder'];
                    }
                    $xtpl->assign('DATA', $_data);
                    $bidder_info = $db->query('SELECT * FROM nv4_dau_gia_asset WHERE id_bid=' . $_data['id_source']);
                    $i = 1;
                    while ($_asset = $bidder_info->fetch()) {
                        $_asset['i'] = $i;
                        $_asset['min_bid_prices'] = number_format($_asset['min_bid_prices'], 0, ",", ".") . ' vnđ';
                        if ($_asset['deposit'] < 30) {
                            $html2 = $_asset['deposit'] . '%';
                        } else {
                            $html2 = number_format($_asset['deposit'], 0, ",", ".") . ' vnđ';
                        }
                        $_asset['deposit'] = $html2;
                        $xtpl->assign('ASSET', $_asset);
                        $xtpl->parse('main.filter.content.asset');
                        $i++;
                    }

                    $xtpl->parse('main.filter.content');
                }
            }
            $xtpl->parse('main.filter');
        }
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_mail_plans($array_data, $vip_id)
{
    global $db, $create_mail_file, $site_lang;

    if (!empty($array_data)) {
        $xtpl = new XTemplate('template_email_dau_gia.tpl', NV_ROOTDIR . '/create_mail');
        include NV_ROOTDIR . '/create_mail/vi.php';
        $xtpl->assign('LANG', $lang_module);
        foreach ($array_data as $filter_id => $arr_list) {
            // Lấy tên bộ lọc
            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();
            // link sửa bộ lọc
            $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
            // ghi log bộ lọc
            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);
            $xtpl->assign('FILTER_NAME', $filter_info['title']);
            if (!empty($arr_list)) {
                if (sizeof($arr_list) > 50) {
                    $xtpl->assign('FILTER_NUMBER', number_format(sizeof($arr_list)));
                    $xtpl->parse('main.filter.number');
                }
                file_put_contents($create_mail_file, 'list_plan: ' . sizeof($arr_list) . "\n", FILE_APPEND);
                $i_break = 0;
                foreach ($arr_list as $_data) {
                    ++$i_break;
                    if ($i_break > 100) {
                        break;
                    }
                    $_data['title_a'] = nv_htmlspecialchars($_data['title']);
                    // Bôi vàng những từ trùng với từ khóa tìm kiếm
                    if ($filter_info['search_type'] == 1) {
                        // Nếu tìm kiếm tuyệt đối
                        $arr_key = explode(',', $filter_info['key_search']);
                    } else {
                        // Nếu tìm kiếm tương đối
                        $filter_info['key_search'] = str_replace(',', ' ', $filter_info['key_search']);
                        $arr_key = explode(' ', trim($filter_info['key_search']));
                        $arr_key = array_unique($arr_key);
                    }

                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $_data['title'] = BoldKeywordInStr(strip_tags($_data['title']), $key);
                    }
                    $_data['opening_bid'] = nv_date('H:i d/m/y', $_data['opening_bid']);
                    $_data['date_bid'] = nv_date('H:i d/m/y', $_data['date_bid']);
                    $_data['link_view'] = DAUGIA_DOMAIN . '/vi/bidorganization/' . $_data['alias'] . '-' . $_data['id_bid'] . '.html';

                    $xtpl->assign('DATA', $_data);
                    $bidder_info = $db->query('SELECT * FROM nv4_dau_gia_asset_select WHERE id_bid=' . $_data['id_source']);
                    $i = 1;
                    while ($_asset = $bidder_info->fetch()) {
                        $_asset['i'] = $i;
                        $_asset['min_bid_prices'] = number_format($_asset['min_bid_prices'], 0, ",", ".") . ' vnđ';
                        if ($_asset['deposit'] < 30) {
                            $html2 = $_asset['deposit'] . '%';
                        } else {
                            $html2 = number_format($_asset['deposit'], 0, ",", ".") . ' vnđ';
                        }
                        $_asset['deposit'] = $html2;
                        $xtpl->assign('ASSET', $_asset);
                        $xtpl->parse('main.filter.content_plan.asset');
                        $i++;
                    }
                    $xtpl->parse('main.filter.content_plan');
                }
            }
            $xtpl->parse('main.filter');
        }
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}
