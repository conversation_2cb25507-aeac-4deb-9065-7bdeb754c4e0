<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
// require NV_ROOTDIR . '/aita/config_aita.php';

/*
 * 1. l<PERSON>y hết plans_contract bị trống tbmt_id hoặc kqlcnt_id
 * 2. L<PERSON>y mã KHLCNT trong bảng nv4_vi_bidding_plans
 * 3. Tìm TBMT ứng với plans_contract
 */

$last_id = 0;
do {
    $query = $db->query('SELECT id, code, id_plan, title, tbmst_id, kqst_id, so_tbmt_mst FROM `nv4_vi_bidding_plans_contract` WHERE id> ' . $last_id . '  ORDER BY `id` ASC LIMIT 100');
    $last_id = 0;
    while ($_row = $query->fetch()) {
        $last_id = $_row['id'];
        $id_plan = $_row['id_plan'];
        echo $id_plan . "\t";
        $code_plan = $db->query('SELECT `code`, solicitor_id FROM `nv4_vi_bidding_plans` WHERE `id`=' . $id_plan)->fetch();
        if (!empty($code_plan)) {
            $solicitor_id = $code_plan['solicitor_id'];
            $code_plan = strstr($code_plan['code'], '-', true); // code không có phần mở rộng
            echo 'code_plan: ' . $code_plan . "\t";
        }

        $tbmt_id = 0;
        $kqlcnt_id = 0;
        $tbmst_id = $_row['tbmst_id'] > 0 ? $_row['tbmst_id'] : 0;
        $kqst_id = $_row['kqst_id'] > 0 ? $_row['kqst_id'] : 0;
        $kqmt_id = 0;

        $so_tbmt = '';
        $so_tbmt_mst = $_row['so_tbmt_mst']; // Biến này dùng cho TBMST
        $bid_code = '';
        $bid_code_contract = '';

        /**
         * Đối với MSC mới, mã gói thầu được lưu:
         * - Bảng plans_contract: code
         * - Bảng row: notify_no
         * - Bảng result: bidno
         * - Bảng open: bidno
         */
        if (!empty($code_plan)) {
            unset($_row2);
            if (!empty($so_tbmt_mst)) {
                $_row2 = $db->query('SELECT t1.id, t2.so_tbmt, t2.notify_no, t2.is_new_msc FROM `nv4_vi_bidding_detail` as t1 INNER JOIN nv4_vi_bidding_row as t2 ON  t1.`id`=t2.`id` WHERE t2.so_tbmt=' . $db->quote($so_tbmt_mst))
                    ->fetch();
            }
            if (empty($_row2)) {
                if (!empty($_row['code'])) {
                    $_sql2 = 'SELECT t1.id, t2.so_tbmt, t2.notify_no, t2.is_new_msc FROM `nv4_vi_bidding_detail` as t1 INNER JOIN nv4_vi_bidding_row as t2 ON  t1.`id`=t2.`id` WHERE
                    t1.khlcnt_code=' . $db->quote($code_plan) . ' AND
                    (
                        (t2.notify_no = ' . $db->quote($_row['code']) . ') OR  (t2.notify_no = "" AND t2.goi_thau=' . $db->quote($_row['title']) . ')
                    ) ORDER BY t2.so_tbmt DESC LIMIT 1';
                } else {
                    $_sql2 = 'SELECT t1.id, t2.so_tbmt, t2.notify_no, t2.is_new_msc FROM `nv4_vi_bidding_detail` as t1 INNER JOIN nv4_vi_bidding_row as t2 ON  t1.`id`=t2.`id` WHERE
                            t1.khlcnt_code=' . $db->quote($code_plan) . ' AND (t2.notify_no = "" AND t2.goi_thau=' . $db->quote($_row['title']) . ')  ORDER BY t2.so_tbmt DESC LIMIT 1';
                }
                $_row2 = $db->query($_sql2)->fetch();
            }
            if (!empty($_row2)) {
                $tbmt_id = $_row2['id'];
                $so_tbmt_mst = $so_tbmt = $_row2['so_tbmt'];
                $bid_code = strstr($so_tbmt, '-', true);

                if ($_row2['is_new_msc'] == 1) {
                    $bid_code_contract = $_row2['notify_no'];
                } else {
                    $bid_code_contract = $bid_code;
                }
                /**
                 * Ý nghĩa các biến
                 * $so_tbmt : số tbmt đầy đủ với phiên bản
                 * $bid_code : số tbmt không có phiên bản
                 * $bid_code_contract : số tbmt không có phiên bản đối với MSC cũ, mã gói thầu đối với MSC mới
                 */
                if (!empty($bid_code)) {
                    $kqmt_id = $db->query('SELECT so_tbmt FROM `nv4_vi_bidding_open` WHERE
                        `so_tbmt` = ' . $db->quote($so_tbmt) . ' OR
                        `so_tbmt` LIKE ' . $db->quote($bid_code . '%') .
                    // `bidno` = ' . $db->quote($bid_code_contract) . ' OR // Khi nào bảng open thêm trường này thì uncomment
                    'ORDER BY so_tbmt DESC LIMIT 1')
                        ->fetchColumn();
                    $kqlcnt_id = $db->query('SELECT id FROM `nv4_vi_bidding_result` WHERE
                        `code` = ' . $db->quote($so_tbmt) . ' OR
                        `code` LIKE ' . $db->quote($bid_code . '%') . ' OR
                        `bid_code`=' . $db->quote($bid_code) . ' OR
                        `bidno` = ' . $db->quote($bid_code_contract) . '
                         ORDER BY `code` DESC LIMIT 1')
                        ->fetchColumn();
                }
                echo 'tbmt_id: ' . $tbmt_id . "\t";
                echo 'kqmt_id: ' . $kqmt_id . "\t";
            }
            if (empty($kqlcnt_id)) {
                // trường hợp chỉ định thầu
                $kqlcnt = $db->query('SELECT id, code, is_new_msc, bidno FROM `nv4_vi_bidding_result` WHERE `plan_code`=' . $db->quote($code_plan) . ' AND title=' . $db->quote($_row['title']) . ' ORDER BY code DESC LIMIT 1')
                    ->fetch();
                if (!empty($kqlcnt)) {
                    $kqlcnt_id = $kqlcnt['id'];
                    if (empty($so_tbmt)) {
                        $so_tbmt = $kqlcnt['code'];
                        if ($kqlcnt['is_new_msc'] == 1) {
                            $bid_code_contract = $kqlcnt['bidno'];
                        } else {
                            $bid_code_contract = strstr($so_tbmt, '-', true);
                        }
                    }
                }
            }
            // Nếu có KQLCNT nhưng không có TBMT thì tìm ngược lại một lần cho chắc
            if (empty($tbmt_id) && !empty($kqlcnt_id)) {
                if (empty($kqlcnt)) {
                    $kqlcnt = $db->query('SELECT id, code, is_new_msc, bidno FROM `nv4_vi_bidding_result` WHERE `id`=' . $kqlcnt_id)->fetchColumn();
                }
                $tbmt = $db->query('SELECT so_tbmt, is_new_msc, notify_no FROM `nv4_vi_bidding_row` WHERE `so_tbmt`=' . $db->quote($kqlcnt['id']))
                    ->fetch();
                if (!empty($tbmt)) {
                    $tbmt_id = $tbmt['id'];
                    if (empty($so_tbmt)) {
                        $so_tbmt = $tbmt['so_tbmt'];
                        if ($tbmt['is_new_msc'] == 1) {
                            $bid_code_contract = $tbmt['notify_no'];
                        } else {
                            $bid_code_contract = strstr($so_tbmt, '-', true);
                        }
                    }
                }
            }

            // TBMST không bị phụ thuộc vào TBMT
            /**
             * Thường thì đối với TBMST thì trong bảng plans_contract đã có sẵn số TBMT/TBMST (so_tbmt_mst)
             * 1.
             * Nếu gói thầu chưa có $tbmst_id thì tìm theo so_tbmt_mst (Nếu đã tìm ra KQLCNT/TBMT thì cập nhật code vào đây).
             * 2. Nếu chưa có cả tbmst_id, so_tbmt_mst thì tìm theo title và bên mời thầu
             * 3. Nếu gói thầu đã có $tbmst_id thì chỉ tìm thêm kqst
             * 4. Nếu đã có $kqst_id mà chưa có $tbmst_id thì tìm ngược lại
             * Do TBMST và KQST mới chưa được bóc về nên sẽ không tìm kiếm theo mã gói thầu
             */
            !empty($so_tbmt) && $so_tbmt_mst = $so_tbmt;
            if (empty($tbmst_id)) {
                if (!empty($so_tbmt_mst)) {
                    $tbmst_id = $db->query('SELECT id FROM nv4_vi_bidding_prequalification WHERE code =' . $db->quote($so_tbmt_mst) . ' OR code LIKE ' . $db->quote(strstr($so_tbmt_mst, '-', true) . '%') . ' ORDER BY code DESC LIMIT 1')
                        ->fetchColumn();
                    !empty($tbmst_id) && $so_tbmt = $so_tbmt_mst;
                } else {
                    if (!empty($solicitor_id)) {
                        $tbmst = $db->query('SELECT id, code FROM nv4_vi_bidding_prequalification WHERE title =' . $db->quote($_row['title']) . ' AND solicitor_id= ' . $solicitor_id . '  ORDER BY `code` DESC LIMIT 1')
                            ->fetch();
                        if (!empty($tbmst)) {
                            $tbmst_id = $tbmst['id'];
                            $so_tbmt = $tbmst['code'];
                        }
                    }
                }
            }
            if (!empty($tbmst_id)) {
                if (empty($tbmst)) {
                    $tbmst = $db->query('SELECT id, code FROM nv4_vi_bidding_prequalification WHERE id=' . $tbmst_id)->fetch();
                }
                $kqst = $db->query('SELECT id, code FROM nv4_vi_bidding_result_prequalification WHERE code=' . $db->quote($tbmst['code']))
                    ->fetch();
                if (!empty($kqst)) {
                    $kqst_id = $kqst['id'];
                    $so_tbmt = $kqst['code'];
                    $bid_code_contract = strstr($so_tbmt, '-', true);
                }
            } elseif (!empty($kqst_id)) { // Nếu có KQST mà không có TBMST thì tìm ngược lại TBMST
                $kqst_code = $db->query('SELECT code FROM nv4_vi_bidding_result_prequalification WHERE id=' . $kqst_id)->fetchColumn;
                $tbmst = $db->query('SELECT id, code FROM nv4_vi_bidding_prequalification WHERE code=' . $db->quote($kqst_code))
                    ->fetch();
                if (!empty($tbmst)) {
                    $tbmst_id = $tbmst['id'];
                    $so_tbmt = $tbmst['code'];
                    $bid_code_contract = strstr($so_tbmt, '-', true);
                }
            }
            // Tìm KQLCNT
            if (empty($kqlcnt_id) && !empty($so_tbmt)) {
                $kqlcnt_id = $db->query('SELECT id FROM `nv4_vi_bidding_result` WHERE `code` = ' . $db->quote($so_tbmt))
                    ->fetchColumn();
            }
            echo 'tbmst_id: ' . $tbmst_id . "\t";
            echo 'kqst_id: ' . $kqst_id . "\t";
            echo 'kqlcnt_id: ' . $kqlcnt_id . "\t";
        }
        if (!empty($bid_code_contract)) {
            (empty($so_tbmt) && !empty($so_tbmt_mst)) && $so_tbmt = $so_tbmt_mst;
            $db->query('UPDATE nv4_vi_bidding_plans_contract SET
                    code=' . $db->quote($bid_code_contract) . ',
                    so_tbmt_mst=' . $db->quote($so_tbmt) . ',
                    tbmt_id =' . intval($tbmt_id) . ',
                    kqlcnt_id =' . intval($kqlcnt_id) . ',
                    kqmt = ' . (empty($kqmt_id) ? 0 : 1) . ',
                    tbmst_id = ' . intval($tbmst_id) . ',
                    kqst_id = ' . intval($kqst_id) . '
                WHERE id = ' . $_row['id']);
        }
        echo "\n";
    }
    $query->closeCursor();
} while ($last_id > 0);
DIE('end');
