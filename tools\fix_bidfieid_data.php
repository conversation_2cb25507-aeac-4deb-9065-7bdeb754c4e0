<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$hosts = array(
    $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
);

$file_name = NV_ROOTDIR . '/tools/fix_bidfieid.txt';
if (file_exists($file_name)) {
    $strID = file_get_contents($file_name);
    $arrID = explode('_', $strID);
    $id = intval($arrID[0]);
    $max_id = intval($arrID[1]);
} else {
    $id = $db->query("SELECT min(id) FROM nv4_vi_bidding_result WHERE bidfieid='PT'")->fetchColumn();
    $max_id = $db->query("SELECT max(id) FROM nv4_vi_bidding_result WHERE bidfieid='PT'")->fetchColumn();
}

echo ("\nmax_id: " . $max_id);
$last_id = ($id  + 1000);

if ($last_id >= $max_id) {
    $max_id = $db->query("SELECT max(id) FROM nv4_vi_bidding_result WHERE bidfieid='PT'")->fetchColumn();
    $last_id = $max_id + 1;
}

$sql = $db->query("SELECT id, bid_id FROM nv4_vi_bidding_result WHERE bidfieid='PT' AND id >= " . $id . " AND id < " . $last_id . " ORDER BY id LIMIT 50");
try {
    
    $need_fix_ids = [];
    $params = [];
    while ($row = $sql->fetch()) {
        echo ("\n>>ID: " . $row['id']);
        
        $need_fix_ids[] = $row['id'];
        
        $params['body'][] = [
            'update' => [
                '_index' => 'dauthau_result',
                '_id' => $row['id']
            ]
        ];
        $params['body'][] = [
            'doc' => [
                'bidfieid' => 'PTV'
            ]
        ];
        $params['body'][] = [
            'update' => [
                '_index' => 'en_dauthau_result',
                '_id' => $row['id']
            ]
        ];
        $params['body'][] = [
            'doc' => [
                'bidfieid' => 'PTV'
            ]
        ];

        $newid = $row['id'];
    }
    $sql->closeCursor();
    
    if (!empty($need_fix_ids)) {
        $db->exec('UPDATE nv4_vi_bidding_result SET bidfieid="PTV" WHERE id IN (' . implode(',', $need_fix_ids) . ')');
        $db->exec('UPDATE nv4_en_bidding_result SET bidfieid="PTV" WHERE id IN (' . implode(',', $need_fix_ids) . ')');
        $need_fix_ids = null;
    }

    if (!empty($params['body'])) {
        $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
            ->setHosts($hosts)
            ->setRetries(0)
            ->build();
        $responses = $client->bulk($params)->asArray();
        $params = null;
        if (!empty($responses['items'])) {
            foreach ($responses['items'] as $value) {
                if ($value['update']['status'] == 404) {
                    if (str_starts_with($value['update']['_index'], 'en_')) {
                        $db->exec('UPDATE nv4_vi_bidding_result SET elasticsearch=19 WHERE id = ' . $value['update']['_id']);
                    } else {
                        $db->exec('UPDATE nv4_en_bidding_result SET elasticsearch=19 WHERE id = ' . $value['update']['_id']);
                    }
                } elseif ($value['update']['status'] != 200) {
                    print_r("Xử lý id: \033[33m" . $value['update']['_id']);
                    print_r("\033[31m ERROR\033[0m\n");
                    file_put_contents(NV_ROOTDIR . '/tools/fix_bidfieid.log', (str_starts_with($value['update']['_index'], 'en_') ? 'en' : 'vi')  . ' ' . $value['update']['_id'] . PHP_EOL, FILE_APPEND);
                }
            }
        }
        $responses = null;
        $client = null;
    }

    echo ("\nnewid:" . $newid);
    echo ("\nid: " . $id);
    if ($newid > $id) {
        echo ("\nnewid = " . $newid);
        file_put_contents($file_name, $newid . '_' . $max_id);
    } else {
        if ($last_id < $max_id) {
            $newid = $last_id;
            echo ("\nlast_id = " . $last_id);
            file_put_contents($file_name, $newid . '_' . $max_id);
        } else {
            echo (">> \033[91mHết dữ liệu \033[0m");
            exit(1);
        }
    }
} catch (Exception $e) {
    trigger_error($e);
    print_r($e);
    print_r("\nLỗi rồi\n");
    exit(1);
}


echo "\nThoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
echo "\n> Đã chạy xong: " . $i;
echo "\n-------------------------------------------\n";
