<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$date_format = ($prefix_lang == 1) ? 'm/d/Y' : 'd/m/Y';
$datetime_format = ($prefix_lang == 1) ? 'm/d/Y H:i' : 'd/m/Y H:i';
$create_mail_file = NV_ROOTDIR . '/data/create_mail_kqlcnt_' . $prefix_lang . '_' . date('Ymd') . '.txt';
if (file_exists(NV_ROOTDIR . '/data/create_mail_kqlcnt_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "kqlcnt.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit();
}

file_put_contents(NV_ROOTDIR . '/data/create_mail_kqlcnt_' . $prefix_lang . '.txt', NV_CURRENTTIME);

try {
    // Từ bảng tmp tổng hợp lại mail gửi đi cho từng VIP
    // Lấy danh sách các mail chưa gửi trong csdl để tiến hành gửi đi gom theo userid
    $vips = [];
    $list_vipid = [];
    $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bidkqlc_id WHERE userid IN (SELECT user_id FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE ((' . NV_CURRENTTIME . ' - last_email) >= time_send*3600) AND vip=7 AND status = 1 AND prefix_lang = ' . $prefix_lang . ' ORDER BY last_email ASC) AND send_status = 0 LIMIT 20');
    while ($row = $query->fetch()) {
        $vips[$row['userid']] = array();
        $list_vipid[$row['userid']] = $row['userid'];
    }

    if (!empty($list_vipid)) {
        $arr_vip = $db->query('SELECT id as cusid, phone, user_id as userid, email, sub_email, time_send, split_email_filters, last_email, active_user, from_time FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id IN ( ' . implode(',', $list_vipid) . ') AND vip=7 AND status = 1 AND prefix_lang = ' . $prefix_lang . '');
        while ($vip_data = $arr_vip->fetch()) {
            // nếu vip này bật chế độ gửi email theo tài khoản
            if ($vip_data['active_user'] == 1) {
                $arr_subemail = [];
                $query_permission = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_permission WHERE vipid=' . $vip_data['cusid'] . ' AND totime >' . NV_CURRENTTIME . ' AND send_mail = 1 AND prefix_lang = ' . $prefix_lang . '');
                while ($permission = $query_permission->fetch()) {
                    $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid = ' . $permission['userid'] . '');
                    while ($user = $arr_user->fetch()) {
                        $arr_subemail[$user['email']] = $user['email'];
                    }
                }
                $vip_data['sub_email'] = implode(',', $arr_subemail);
            }
            $vips[$vip_data['userid']] = $vip_data;
            $vips[$vip_data['userid']]['filter'] = array();
            $vips[$vip_data['userid']]['bid'] = array();
            $vips[$vip_data['userid']]['is_vip'] = 1;
        }
    }
    if (!empty($vips)) {
        // lấy username
        $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid IN ( ' . implode(',', array_keys($vips)) . ')');
        while ($user = $arr_user->fetch()) {
            $vips[$user['userid']]['username'] = $user['username'];
        }

        // Lưu nội dung mail vào csdl
        foreach ($vips as $vip_id => $vip) {
            $array_result_id = [];
            $array_result_ids = [];
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bidkqlc_id WHERE send_status = 0 AND userid=' . $vip_id . ' LIMIT 50');
            while ($result_data = $query->fetch()) {
                $array_result_id[$result_data['id']] = $result_data;
                $array_result_ids[$result_data['bid_id']] = $result_data['bid_id'];
            }

            $arr_result = [];
            if (!empty($array_result_ids)) {
                $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result WHERE id IN (' . implode(',', $array_result_ids) . ')');
                while ($result_row = $query->fetch()) {
                    $arr_result[$result_row['id']] = $result_row;
                }

                $result_query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result_business WHERE resultid IN (' . implode(',', $array_result_ids) . ')');
                while ($business_row = $result_query->fetch()) {
                    $arr_result[$business_row['resultid']]['business'][] = $business_row;
                }
            }

            foreach ($array_result_id as $result_data) {
                if ($result_data['filter_id'] > 0 and !empty($arr_result[$result_data['bid_id']])) {
                    $vip['filter'][$result_data['filter_id']]['list_result'][] = $arr_result[$result_data['bid_id']];
                    $vip['result'][] = $result_data['bid_id'];
                }
            }

            // Cập nhật lại lần cuối cùng gửi mail cho các tài khoản VIP, Phần này cần chạy trước beg-----inTransaction để nếu có lỗi thì bỏ qua 1 tài khảo.
            if (sizeof($array_result_id) < 50) {
                // Nếu 1 lần tổng hợp có > 50 thì không cập nhật ngay last_email để 1 phút sau sẽ chạy tiếp
                if ($vip['is_vip']) {
                    $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET last_email = ' . NV_CURRENTTIME . ' WHERE id =' . $vip['cusid'] . ' AND vip = 7');
                }
            } else {
                file_put_contents($create_mail_file, "Con chay nua: " . sizeof($array_result_id) . " / " . sizeof($arr_result) . "\n", FILE_APPEND);
            }

            $db->beginTransaction();
            file_put_contents($create_mail_file, 'BEGIN: ' . $vip_id . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
            try {

                $data_insert = [];
                $data_insert['addtime'] = NV_CURRENTTIME;
                $data_insert['send_time'] = 0;
                $data_insert['status'] = 0;
                // Gửi mail kết quả lựa chọn nhà thầu cho các VIP
                if (!empty($vip['filter'])) {
                    $data_insert['title'] = sprintf($lang_module['mail_title_kqlcnt'], date($date_format));
                    $data_insert['type'] = 0;
                    $data_insert['vip'] = 7;

                    if ($vip['split_email_filters'] == 1) {
                        // Phân chia email thông báo mời thầu riêng biệt cho từng bộ lọc
                        foreach ($vip['filter'] as $filterId => $filter) {
                            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filterId)->fetch();
                            $title = sprintf($lang_module['mail_title_kqlcnt_split'], date($date_format)) . (!empty($filterId) ? sprintf($lang_module['with_filter'], $filter_info['title']) : '');
                            $data_insert['content'] = nv_theme_result_mail([
                                $filterId => $filter
                            ], $vip_id);
                            $data_insert['content'] .= sprintf($lang_module['mail_footer_tbmt'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date($date_format, $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');

                            // Nội dung htm sẽ gửi cho từng khách.
                            try {
                                $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                                $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($row['cc_mail']));
                                $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                                $stmt->bindParam(':title', $title, PDO::PARAM_STR);
                                $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                                $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                                $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                                $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                                $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                                $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                                $exc = $stmt->execute();

                                $_mailid = $db->lastInsertId();
                                file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            } catch (PDOException $e) {
                                print_r('Lỗi thêm mail kết quả chọn thầu vào csdl');
                                file_put_contents($create_mail_file, "ERROR INSERT INTO mail: 148 " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);

                                trigger_error($e->getMessage());
                                echo ($e->getMessage()); // Remove this line after checks finished
                                break;
                            }
                        }
                    } else {
                        $data_insert['content'] = nv_theme_result_mail($vip['filter'], $vip_id);
                        $data_insert['content'] .= sprintf($lang_module['mail_footer_tbmt'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date($date_format, $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');

                        // Nội dung htm sẽ gửi cho từng khách.
                        try {
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($row['cc_mail']));
                            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (PDOException $e) {
                            print_r('Lỗi thêm mail kết quả chọn thầu vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail: 148 " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);

                            trigger_error($e->getMessage());
                            echo ($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }
                }

                echo "vip_id = " . $vip_id . "\n";

                // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                if (!empty($array_result_id)) {
                    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bidkqlc_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', array_keys($array_result_id)) . ') AND send_status = 0');
                }
                $db->commit();
                file_put_contents($create_mail_file, "number bid = " . sizeof($array_result_id) . ": " . implode(',', array_keys($array_result_id)) . "\n", FILE_APPEND);
                file_put_contents($create_mail_file, "END: " . $vip_id . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
                // print_r($array_result_id);
            } catch (PDOException $e) {
                file_put_contents($create_mail_file, 'rollBack: ' . $vip_id . "\n\n", FILE_APPEND);
                $db->rollBack();
                echo '<pre>';
                print_r($e);
            }
        }
    }

    unlink(NV_ROOTDIR . '/data/create_mail_kqlcnt_' . $prefix_lang . '.txt');
} catch (PDOException $e) {
    file_put_contents($create_mail_file, 'ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "kqlcnt.php ERROR: " . print_r($e, true) . "\n\n");
}
echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function nv_theme_result_mail($array_result, $vip_id)
{
    global $db, $create_mail_file, $lang_module, $mod_func, $site_lang, $prefix_lang, $date_format, $datetime_format;

    if (!empty($array_result)) {
        $xtpl = new XTemplate('template_email_kqlcnt.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);

        foreach ($array_result as $filter_id => $arr_list) {
            // Lấy tên bộ lọc
            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();
            // link sửa bộ lọc
            $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
            $xtpl->assign('FILTER_NAME', $filter_info['title']);
            $from = $filter_info['time_find'] > 0 ? nv_date($date_format, NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date($date_format, NV_CURRENTTIME - 86400 * 14);
            $filter_info['link_search'] = '';
            $filter_info['link_search_result'] = NV_MY_DOMAIN . '/' . $site_lang . '/listresult?type_info=3';
            if ($filter_info['key_search'] != '') {
                $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
            }
            if ($filter_info['key_search2'] != '') {
                $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
            }
            $filter_info['link_search'] .= '&sfrom=' . $from;
            $filter_info['link_search'] .= '&sto=' . nv_date($date_format, NV_CURRENTTIME);
            $filter_info['link_search'] .= '&is_advance=1';
            $filter_info['link_search'] .= '&userid=' . $vip_id;
            $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
            $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];
            $filter_info['link_search'] .= '&search_one_key=' . $filter_info['search_one_key'];

            if ($filter_info['cat'] > 0) {
                $filter_info['link_search'] .= '&cat=' . $filter_info['cat'];
            }
            if ($filter_info['field'] != '') {
                $array_field = explode(',', $filter_info['field']);
                foreach ($array_field as $field) {
                    $filter_info['link_search'] .= '&field[]=' . $field;
                }
            }
            if ($filter_info['without_key'] != '') {
                $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
            }

            $filter_info['link_search_result'] .= $filter_info['link_search'];
            $filter_info['link_result'] = '';
            if ($filter_info['type_bid'] > 0) {
                $filter_info['link_result'] .= '&type_bid=' . $filter_info['type_bid'];
            }
            if ($filter_info['type_choose_id'] > 0) {
                $filter_info['link_result'] .= '&type_choose_id=' . $filter_info['type_choose_id'];
            }
            if ($filter_info['type_kqlcnt'] > 0) {
                $filter_info['link_result'] .= '&type_kqlcnt=' . $filter_info['type_kqlcnt'];
            }
            if ($filter_info['win_price_from'] > 0) {
                $filter_info['link_result'] .= '&win_price_from=' . number_format($filter_info['win_price_from'], 0, ',', '.');
            }
            if ($filter_info['win_price_to'] > 0) {
                $filter_info['link_result'] .= '&win_price_to=' . number_format($filter_info['win_price_to'], 0, ',', '.');
            }
            if ($filter_info['price_plan_from'] > 0) {
                $filter_info['link_result'] .= '&price_plan_from=' . number_format($filter_info['price_plan_from'], 0, ',', '.');
            }
            if ($filter_info['price_plan_to'] > 0) {
                $filter_info['link_result'] .= '&price_plan_to=' . number_format($filter_info['price_plan_to'], 0, ',', '.');
            }
            $filter_info['link_search_result'] = $filter_info['link_search_result'] . $filter_info['link_result'];
            $xtpl->assign('LINK_FILTER', $filter_info['link_search_result']);

            // ghi log bộ lọc
            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);

            if (!empty($arr_list['list_result'])) {
                file_put_contents($create_mail_file, 'list_result: ' . sizeof($arr_list['list_result']) . "\n", FILE_APPEND);
                $i_break = 0;
                foreach ($arr_list['list_result'] as $array_data) {
                    ++$i_break;
                    if ($i_break > 100) {
                        break;
                    }
                    $array_data['title_a'] = nv_htmlspecialchars($array_data['title']);
                    // Bôi vàng những từ trùng với từ khóa tìm kiếm
                    if ($filter_info['search_type'] == 1) {
                        // Nếu tìm kiếm tuyệt đối
                        $arr_key = explode(',', $filter_info['key_search']);
                    } else {
                        // Nếu tìm kiếm tương đối
                        $filter_info['key_search'] = str_replace(',', ' ', $filter_info['key_search']);
                        $arr_key = explode(' ', trim($filter_info['key_search']));
                        $arr_key = array_unique($arr_key);
                    }

                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $array_data['title'] = BoldKeywordInStr(strip_tags($array_data['title']), $key);
                        $array_data['investor'] = strip_tags(BoldKeywordInStr($array_data['investor'], $key));
                    }
                    $array_data['post_time'] = $array_data['post_time'] > 0 ? nv_date(($prefix_lang == 1 ? 'H:i m/d/Y' : 'H:i d/m/Y'), $array_data['post_time']) : '';
                    $array_data['finish_time'] = $array_data['finish_time'] > 0 ? nv_date(($prefix_lang == 1 ? 'H:i m/d/Y' : 'H:i d/m/Y'), $array_data['finish_time']) : '';
                    $luachon_nt = getUrlByLanguage('kqlcnt');
                    if ($site_lang != 'vi') {
                        $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['result'] . '/' . $luachon_nt . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                    } else {
                        $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['result'] . '/' . $luachon_nt . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                    }
                    if ($array_data['tender_price'] > 0) {
                        $array_data['tender_price'] = number_format($array_data['tender_price'], 0, ',', '.') . ' VND';
                    }

                    $xtpl->assign('DATA', $array_data);
                    if (!empty($array_data['business'])) {
                        foreach ($array_data['business'] as $business) {
                            $business['bidwinningprice'] = number_format($business['bidwinningprice'], 0, ',', '.') . ' VND';
                            $xtpl->assign('BUSINESS', $business);
                            $xtpl->parse('main.result.loop.business_winning_price.business');
                        }
                        $xtpl->parse('main.result.loop.business_winning_price');
                    }
                    if (!empty($array_data['code'])) {
                        $xtpl->parse('main.result.loop.code');
                    }
                    if (!empty($array_data['title'])) {
                        $xtpl->parse('main.result.loop.title');
                    }
                    if (!empty($array_data['type_bid'])) {
                        $xtpl->parse('main.result.loop.type_bid');
                    }
                    if (!empty($array_data['project_name'])) {
                        $xtpl->parse('main.result.loop.ten_du_an');
                    }
                    if (!empty($array_data['investor'])) {
                        $xtpl->parse('main.result.loop.chu_dau_tu');
                    }
                    if (!empty($array_data['cat'])) {
                        $xtpl->parse('main.result.loop.cat');
                    }
                    if (!empty($array_data['bid_price'])) {
                        $xtpl->parse('main.result.loop.bid_price');
                    }
                    if (!empty($array_data['finish_time'])) {
                        $xtpl->parse('main.result.loop.finish_time');
                    }
                    if (!empty($array_data['post_time'])) {
                        $xtpl->parse('main.result.loop.post_time');
                    }
                    if (!empty($array_data['point'])) {
                        $xtpl->parse('main.result.loop.point');
                    }
                    if (!empty($array_data['evaluating_price'])) {
                        $xtpl->parse('main.result.loop.evaluating_price');
                    }
                    $xtpl->parse('main.result.loop');
                }
                $xtpl->parse('main.result');
            }
        }
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}
