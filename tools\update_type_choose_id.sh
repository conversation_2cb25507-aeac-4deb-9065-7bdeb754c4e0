#!/bin/bash

SOURCE="${BASH_SOURCE[0]}"
while [ -h "$SOURCE" ]; do
  TARGET="$(readlink "$SOURCE")"
  if [[ $TARGET == /* ]]; then
    SOURCE="$TARGET"
  else
    DIR="$( dirname "$SOURCE" )"
    SOURCE="$DIR/$TARGET"
  fi
done
DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
DIR_PATH=$PWD

echo '---------------- Bắt đầu chạy tool ----------------'
echo ''
while : ; do
  php "$DIR_PATH/update_type_choose_id.php"
  code=$?
  if [[ $code == 1 ]]; then
    echo "Đã hoàn tất cho ngôn ngữ vn"
    break
  fi
  sleep 1
done
