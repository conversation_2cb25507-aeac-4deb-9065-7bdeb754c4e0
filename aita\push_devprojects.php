<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/aita/config_aita.php';

$array_devproject_id = $array_filter_id = [];
$query = $db->query('SELECT devproject_id, filter_id FROM nv4_vi_aita_devproject_id WHERE send_status=0 ORDER BY `id` ASC LIMIT 100');
while ($_devproject = $query->fetch()) {
    if ($_devproject['devproject_id'] > 0) {
        $array_devproject_id[] = $_devproject['devproject_id'];
        $array_filter_id[$_devproject['devproject_id']] = $_devproject['filter_id'];
    }
}

$location = 'push_devprojects.php';

if (!empty($array_devproject_id)) {

    $db->query('UPDATE nv4_vi_aita_devproject_id SET send_status=-9 WHERE devproject_id IN (' . implode(',', $array_devproject_id) . ') AND send_status=0');
    $arr_filter = [];
    $query_filter = $db->query('SELECT * FROM `nv4_vi_aita_filter` WHERE status=1 AND id IN (' . implode(',', $array_filter_id) . ') ORDER BY weight ASC');
    while ($result = $query_filter->fetch()) {
        $arr_filter[$result['id']] = $result;
    }

    $sql = 'SELECT * FROM nv4_vi_project_investment WHERE id IN (' . implode(',', $array_devproject_id) . ')';
    $query = $db->query($sql);
    while ($project_investment = $query->fetch()) {
        $id = $project_investment['id'];
        echo "project id = " . $id . "\n";

        if ($project_investment['is_aita'] == -1) {
            // Bỏ qua các dữ liệu đánh đấu bằng -1
            $db->query('UPDATE nv4_vi_aita_devproject_id SET send_status=-1 WHERE devproject_id=' . $id);
            continue;
        }

        $filter_id = $array_filter_id[$id];
        $keyword = $arr_phanmuc = [];
        // xử lý xem từ khóa nào khớp
        if (isset($arr_filter[$filter_id])) {
            $filter = $arr_filter[$filter_id];
            $arr_phanmuc[$filter['phanmuc']] = $filter['phanmuc'];
            if (!empty($filter['key_search'])) {
                $arr_key = explode(',', $filter['key_search']);
                foreach ($arr_key as $key) {
                    $key = trim($key);
                    if (preg_match('/^(.*?)' . nv_preg_quote($key) . '/uis', $project_investment['content_full'], $matches)) {
                        $keyword[] = $key;
                    }
                }
            }
        }
        $keyword = implode(', ', $keyword);

        $request = [
            // Tham số bắt buộc
            'apikey' => $apikey,
            'language' => 'vi',
            'module' => 'bidding',
            'action' => 'DEVPROJECTS',
            'id' => $id,
            'filter_id' => $filter_id,
            'keyword' => $keyword,
            'phanmuc' => implode(',', $arr_phanmuc)
        ];

        if ($project_investment['solicitor_id'] > 0) {
            $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $project_investment['solicitor_id'];
            $request['bidding_solicitor'] = $db->query($sql)->fetch();

            $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $project_investment['solicitor_id'];
            $request['bidding_solicitor_detail'] = $db->query($sql)->fetch();

            $db->query('UPDATE nv4_bidding_solicitor SET update_aita=' . time() . ' WHERE id=' . $project_investment['solicitor_id']);
        }

        // tìm thêm theo tên chủ đầu tư
        $solicitor_id = get_solicitor_id($project_investment['investor']);
        if ($solicitor_id > 0) {
            if ($project_investment['solicitor_id'] == 0) {
                $project_investment['solicitor_id'] = $solicitor_id;
                $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $solicitor_id;
                $request['bidding_solicitor'] = $db->query($sql)->fetch();

                $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $solicitor_id;
                $request['bidding_solicitor_detail'] = $db->query($sql)->fetch();
            } else if ($project_investment['solicitor_id'] != $solicitor_id) {
                $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $solicitor_id;
                $request['bidding_investor'] = $db->query($sql)->fetch();

                $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $solicitor_id;
                $request['bidding_investor_detail'] = $db->query($sql)->fetch();
            }

            $db->query('UPDATE nv4_bidding_solicitor SET update_aita=' . time() . ' WHERE id=' . $solicitor_id);
        }

        $timestamp = time();
        $request['hashsecret'] = password_hash($apisecret . '_' . $timestamp, PASSWORD_DEFAULT);
        $request['timestamp'] = $timestamp;
        $request['project_investment'] = json_encode($project_investment);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_remote_url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        $safe_mode = (ini_get('safe_mode') == '1' || strtolower(ini_get('safe_mode')) == 'on') ? 1 : 0;
        $open_basedir = ini_get('open_basedir') ? true : false;
        if (!$safe_mode and !$open_basedir) {
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);

        curl_setopt($ch, CURLOPT_POST, sizeof($request));
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($request));
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $res = curl_exec($ch);
        curl_close($ch);

        $responsive = json_decode($res, true);

        if (isset($responsive['status']) and $responsive['status'] == 'success') {
            $location = 'push_devprojects id=' . $id;
            $db->query('UPDATE nv4_vi_project_investment SET is_aita = 1 WHERE id =' . $id);
            $db->query('UPDATE nv4_vi_aita_devproject_id SET send_status=' . $timestamp . ' WHERE devproject_id=' . $id . ' AND send_status<=0');

            $arr_id_plan = array_column($db->query('SELECT id FROM `nv4_vi_bidding_plans` WHERE id_project = ' . $id)->fetchAll(), 'id');
            if (!empty($arr_id_plan)) {
                // Kiểm tra xem KHLCNT đã có trong bảng nv4_vi_aita_plan_id chưa, nếu chưa có thì
                $arr_id_plan_exit = [];
                $_query = $db->query("SELECT DISTINCT plan_id FROM nv4_vi_aita_plan_id WHERE plan_id IN (" . implode(',', $arr_id_plan) . ")");
                while ($_r = $_query->fetch()) {
                    $arr_id_plan_exit[] = $_r['plan_id'];
                }

                foreach ($arr_id_plan as $_id) {
                    if (!in_array($_id, $arr_id_plan_exit)) {
                        $db->exec('INSERT INTO `nv4_vi_aita_plan_id`(filter_id, plan_id, location, send_status, addtime) VALUES (0, ' . $_id . ', ' . $db->quote($location) . ' , 0, ' . NV_CURRENTTIME . ')');
                    }
                }
            }
        } else {
            if (isset($responsive['status'])) {
                print_r($responsive);
            } else {
                echo $res . "\n";
            }
            $db->query('UPDATE nv4_vi_aita_devproject_id SET send_status=-' . $timestamp . ' WHERE devproject_id=' . $id . ' AND send_status<=0');
        }
    }
    echo " RunTime: " . (time() - NV_CURRENTTIME) . " \n";
} else {
    //$db->query('UPDATE nv4_vi_aita_plan_id SET send_status=0 WHERE send_status < -99');
    die("No Data");
}
