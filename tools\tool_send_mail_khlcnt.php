<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

require NV_ROOTDIR . '/vendor/autoload.php';
$site_timezone = 'Asia/Saigon';
date_default_timezone_set($site_timezone);

$waitTimeoutInSeconds = 2;
if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
}
fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setHosts($hosts)
        ->setRetries(0)
        ->build();
}

$start_plan_id = 0;
$info = $db->query('SELECT max(id) as to_id FROM `nv4_vi_bidding_plans`')->fetch();
$end_plan_id = $info['to_id']; // Lấy id mời thầu lớn nhất của dữ liệu hiện có
$end_plan_id = 500000;
$time = time();
$start_time = $time;
$arr_vip = array();
$time_start_thisday = mktime(date('H'), date('i'), date('s'), date('n'), date('j'), date('Y'));
$testUserid = 13;

// Test 1 tài khoản VIP
$query_vip = $db->query('SELECT * FROM `nv4_vi_bidding_customs` WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id =' . $testUserid . ' AND (vip = 2)');
while ($vip_info = $query_vip->fetch()) {
    $arr_vip[$vip_info['user_id']] = $vip_info;
}

$filter = array();
$filter['time_find'] = 200;
$filter['type_org'] = 0;
$filter['price_plan_from'] = 0;
$filter['price_plan_to'] = 0;
$filter['invest_from'] = 0;
$filter['invest_to'] = 0;
$filter['search_type'] = 0;
$filter['without_key'] = 'mua, mua sắm, nghiên cứu, tối ưu, bảo hiểm';
$filter['key_search2'] = '';
$filter['key_search'] = 'vận hành tòa nhà, vận hành nhà, vận hành trụ sở, vận hành khối nhà, quản lý tòa nhà';

$db->beginTransaction();
try {
    foreach ($arr_vip as $vip_id => $vip) {
        echo "Bắt đầu quét:" . $vip_id . "\n";
        //Từ bộ lọc chọn ra điều kiện lọc where tương ứng
        $search_elastic = array();
        $search_elastic['must'][]['range']['id'] = [
            "gt" => $start_plan_id,
            "lte" => $end_plan_id
        ];

        if ($filter['time_find'] > 0) {
            $time_find = mktime(0, 0, 0, date('n'), date('j'), date('Y')) - 86400 * $filter['time_find'];

            $search_elastic['must'][]['range']['addtime'] = [
                "gte" => $time_find
            ];
        }

        // từ khóa loại trừ
        if (!empty($filter['without_key'])) {
            $arr_key = explode(',', $filter['without_key']);
            foreach ($arr_key as $key) {
                if ($filter['search_type'] == 1) {
                    $key = trim($key);
                    $search_elastic['must_not'][] = [
                        "match_phrase" => [
                            "content_full" => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_elastic['must_not'][] = [
                        "match_phrase" => [
                            "content" => $key
                        ]
                    ];
                }
            }
        }

        if (!empty($filter['key_search2'])) {
            //Tìm theo từ khóa cùng
            $arr_key = explode(',', $filter['key_search2']);
            foreach ($arr_key as $key) {
                if ($filter['search_type'] == 1) {
                    $key = trim($key);
                    $search_elastic['must'][] = [
                        "match_phrase" => [
                            "content_full" => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_elastic['must'][] = [
                        "match_phrase" => [
                            "content" => $key
                        ]
                    ];
                }
            }
        }

        if (!empty($filter['key_search'])) {
            $arr_key = explode(',', $filter['key_search']);
            foreach ($arr_key as $key) {
                if ($filter['search_type'] == 1) {
                    $key = trim($key);
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "content_full" => $key
                        ]
                    ];
                } else {
                    $key = str_replace('-', ' ', change_alias($key));
                    $key = trim($key);
                    $search_elastic['should'][] = [
                        "match_phrase" => [
                            "content" => $key
                        ]
                    ];
                }
            }
        }
        //tìm theo tổng mức đầu tư
        if (($filter['invest_from'] > 0 or $filter['invest_to'] > 0) and ($filter['invest_from'] < $filter['invest_to'])) {
            $search_elastic['must'][]['range']['total_invest_number'] = [
                "gte" => $filter['invest_from'],
                "lte" => $filter['invest_to']
            ];
        }
        //tìm theo giá gói thầu
        if (($filter['price_plan_from'] > 0 or $filter['price_plan_to'] > 0) and ($filter['price_plan_from'] < $filter['price_plan_to'])) {
            $search_elastic['must'][]['range']['price_min'] = [
                "gte" => $filter['price_plan_from'],
                "lte" => $filter['price_plan_to']
            ];
            $search_elastic['must'][]['range']['price_max'] = [
                "gte" => $filter['price_plan_from'],
                "lte" => $filter['price_plan_to']
            ];
        }

        if (!empty($filter['key_search'])) {
            $search_elastic['minimum_should_match'] = '1';
            $search_elastic['boost'] = '1.0';
        }

        $array_query_elastic = array();
        if (!empty($search_elastic)) {
            $array_query_elastic['query']['bool'] = $search_elastic;
        }
        $array_query_elastic['size'] = 1000;
        $array_query_elastic['sort'] = [
            [
                "addtime" => [
                    "order" => "desc"
                ]
            ]
        ];

        $params = array();
        $params['index'] = 'dauthau_plans';
        $params['type'] = 'nv4_vi_bidding_plans';
        $params['body'] = $array_query_elastic;
        $response = $client->search($params)->asArray();

        $array_bidding = array();
        foreach ($response['hits']['hits'] as $value) {
            if (!empty($value['_source'])) {
                $bidding = $value['_source'];
                $array_bidding[$bidding['id']] = $bidding;
            }
        }

        if (!empty($array_bidding)) {
            $data_insert = array();
            $data_insert['addtime'] = NV_CURRENTTIME;
            $data_insert['send_time'] = 0;
            $data_insert['status'] = 0;

            $data_insert['title'] = 'Thông báo: Có Kế hoạch lựa chọn nhà thầu mới';
            $data_insert['type'] = 0;
            $data_insert['vip'] = 2;
            $data_insert['content'] = nv_theme_bidding_mail($filter, $array_bidding, $vip_id);

            $stmt = $db->prepare("INSERT INTO nv4_vi_bidding_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, `messageid`, `reject`, `bounce`, `complaint`, `click`, `open`, `failure`) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '')");
            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
            $exc = $stmt->execute();
        }
    }

    $db->commit();
} catch (PDOException $e) {
    $db->rollBack();
    print_r($e);
    die(); //Remove this line after checks finished
}

$time = time();
$this_time = $time - NV_CURRENTTIME;

$note = 'L420 Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
die($note . "\n");

function nv_theme_bidding_mail($array_filter, $array_bidding, $vip_id)
{
    global $db, $lang_module;

    if (!empty($array_filter)) {
        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        include NV_ROOTDIR . '/create_mail/vi.php';
        $xtpl->assign('LANG', $lang_module);
        //Lấy tên bộ lọc
        //print($filter_name);
        $xtpl->assign('FILTER_NAME', 'Bộ lọc gửi mail KHLCNT test');

        if (!empty($array_bidding)) {
            $i_break = 0;
            // hiển thị
            if (!empty($array_bidding)) {
                $i_break = 0;
                foreach ($array_bidding as $array_data) {
                    ++$i_break;
                    if ($i_break > 100) {
                        break;
                    }
                    $array_data['title_a'] = nv_htmlspecialchars($array_data['title']);
                    //Bôi vàng những từ trùng với từ khóa tìm kiếm
                    if ($array_filter['search_type'] == 1) {
                        //Nếu tìm kiếm tuyệt đối
                        $arr_key = explode(',', $array_filter['key_search']);
                    } else {
                        //Nếu tìm kiếm tương đối
                        $array_filter['key_search'] = str_replace(',', ' ', $array_filter['key_search']);
                        $arr_key = explode(' ', trim($array_filter['key_search']));
                        $arr_key = array_unique($arr_key);
                    }

                    foreach ($arr_key as $key) {
                        $key = trim($key);
                        $array_data['title'] = BoldKeywordInStr(strip_tags($array_data['title']), $key);
                        $array_data['investor'] = strip_tags(BoldKeywordInStr($array_data['investor'], $key));
                    }
                    $array_data['addtime'] = $array_data['addtime'] > 0 ? nv_date('H:i d/m/Y', $array_data['addtime']) : '';
                    $array_data['approval_date'] = nv_date('d/m/Y', $array_data['approval_date']);
                    $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/plans?id=' . $array_data['id'];

                    $so_tbmt = explode('-', $array_data['code']);
                    $check = md5($vip_id . $so_tbmt[0]);
                    $array_data['link_follow'] = NV_MY_DOMAIN . '/' . $site_lang . '/bidding/followplans?plans_id=' . $array_data['id'] . '&plans_code=' . $so_tbmt[0] . '&vipid=' . $vip_id . '&check=' . $check;

                    $xtpl->assign('DATA', $array_data);
                    $xtpl->parse('main.filter.content_plan');
                }
            }
        }
        $xtpl->parse('main.filter');
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}
