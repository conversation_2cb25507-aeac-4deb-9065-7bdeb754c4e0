<!-- BEGIN: main -->
<style>
.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 18px;
}

.table-bordered {
    border-spacing: 0px;
}

.table-bordered>tbody>tr>td {
    border: 1px solid #ddd;
    padding: 5px;
}
</style>
<!-- BEGIN: filter -->
<h3>
    {LANG.filter_ycbg}: <a href="{LINK_FILTER}">{FILTER_NAME}</a> <a href="{LINK_EDIT}">({LANG.edit})</a>
</h3>
<!-- BEGIN: number -->
<div style="color: #a94442; background-color: yellow;">{FILTER_NUMBER}</div>
<!-- END: number -->

<!-- BEGIN: content_ycbg -->
<div class="table-responsive">
    <p class="bidding-name">
        {LANG.ycbg}:&nbsp;<a href="{DATA.link_view}" title="{DATA.title_a}" style="color: #2989de; font-size: 16px;">{DATA.request_quote_name}</a>
    </p>

    <p class="title-bidding" style="font-size: 15px; font-weight: 700">{LANG.info}:</p>

    <!-- BEGIN: notify_old -->
    <div class="alert alert-danger" style="color: red; line-height: 22px;">{LANG.notify_old}</div>
    <!-- END: notify_old -->

    <table class="table table-bidding table-bordered table-hover" style="width: 100%; border: 1px solid #F5F5F5;">
        <tbody>
            <tr>
                <td style="width: 30%; font-weight: 700">{LANG.request_quote_code}:</td>
                <td>{DATA.request_quote_code}</td>
            </tr>
            <tr>
                <td style="width: 30%; font-weight: 700">{LANG.public_date}:</td>
                <td>{DATA.public_date}</td>
            </tr>

            <!-- BEGIN: project_owner -->
            <tr>
                <td style="width: 30%; font-weight: 700">{LANG.project_owner}:</td>
                <td>{DATA.investor_name}</td>
            </tr>
            <!-- END: project_owner -->
            <tr>
                <td style="width: 30%; font-weight: 700">{LANG.goi_thau}:</td>
                <td class="title-strong" style="color: #2989de; font-weight: 700;">{DATA.bid_name}</td>
            </tr>
            <!-- BEGIN: p_name -->
            <tr>
                <td style="width: 30%; font-weight: 700">{LANG.p_name}:</td>
                <td>{DATA.p_name}</td>
            </tr>
            <!-- END: p_name -->
            <tr>
                <td style="width:30%;font-weight:700">{LANG.request_quote_type}:</td>
                <td>{DATA.request_quote_type_title}</td>
            </tr>
            <tr>
                <td style="width:30%;font-weight:700">{LANG.request_quote_form}:</td>
                <td>{DATA.request_quote_form_title}</td>
            </tr>
            <tr>
                <td style="width:30%;font-weight:700">{LANG.reception_date_to}:</td>
                <td>{DATA.reception_date}</td>
            </tr>
            <tr>
                <td style="width:30%;font-weight:700">{LANG.rq_validity_period}:</td>
                <td>{DATA.rq_validity_period_unit_title}</td>
            </tr>
            <tr>
                <td colspan="2" style="text-align: center"><a href="{DATA.link_view}"><button style="text-align: center; margin: 10px; background-color: #428bca; color: #fff; border-color: #357ebd">{LANG.view_detail}</button></a> <!-- BEGIN: follow --> <a href="{DATA.link_follow}"><button style="text-align: center; margin: 10px; background-color: #428bca; color: #fff; border-color: #357ebd">{LANG.follow}</button></a> <!-- END: follow --></td>
            </tr>
        </tbody>
    </table>
</div>
<hr style="height: 2px; background: #94052d"></hr>
<!-- END: content_ycbg -->
<!-- END: filter -->
 <!-- BEGIN: change -->
<h2>{LANG.ycbg_change}</h2>
<!-- BEGIN: loop -->
<div class="table-responsive">
    <p class="bidding-name">
        {LANG.ycbg}: <a href="{DATA.link_view}" title="{DATA.title_a}" style="color: #2989de; font-size: 16px;">{DATA.title}</a>
    </p>
    <p class="bidding-name">{LANG.request_quote_code}: {DATA.request_quote_code}</p>
    <p class="bidding-name">{LANG.noi_dung_ycbg}: {DATA.content}</p>
    <p class="bidding-name">{LANG.public_date}: {DATA.public_date}</p>
    <p class="bidding-name">
        <a href="{DATA.link_view}"><button style="text-align: center; margin: 10px; background-color: #428bca; color: #fff; border-color: #357ebd">{LANG.view_detail}</button></a> <a href="{DATA.link_follow}"><button style="text-align: center; margin: 10px; background-color: #428bca; color: #fff; border-color: #357ebd">{LANG.follow_rq}</button></a>
    </p>
</div>
<hr style="height: 2px; background: #94052d"></hr>
<!-- END: loop -->
<!-- END: change -->
<!-- END: main -->
