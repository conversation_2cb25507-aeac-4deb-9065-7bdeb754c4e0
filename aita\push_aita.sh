#!/bin/bash

push_checkall=1
while [ $push_checkall -gt 0 ] ; do
	push_checkall=0
	whileccheck=1
	while [ $whileccheck -gt 0 ] ; do
		ruledetail=$(php /home/<USER>/private/aita/push_devprojects.php)
		echo $ruledetail
		if [[ "$ruledetail" == "No Data" ]]; then
			whileccheck=0
			break
		fi
		push_checkall=$((push_checkall + 1))
	done
	
	whileccheck=1
	while [ $whileccheck -gt 0 ] ; do
		ruledetail=$(php /home/<USER>/private/aita/push_khlcnt.php)
		echo $ruledetail
		if [[ "$ruledetail" == "No Data" ]]; then
			whileccheck=0
			break
		fi
		push_checkall=$((push_checkall + 1))
	done
	
	whileccheck=1
	while [ $whileccheck -gt 0 ] ; do
		ruledetail=$(php /home/<USER>/private/aita/push_tbmt.php)
		echo $ruledetail
		if [[ "$ruledetail" == "No Data" ]]; then
			whileccheck=0
			break
		fi
		push_checkall=$((push_checkall + 1))
	done
	
	whileccheck=1
	while [ $whileccheck -gt 0 ] ; do
		ruledetail=$(php /home/<USER>/private/aita/push_result.php)
		echo $ruledetail
		if [[ "$ruledetail" == "No Data" ]]; then
			whileccheck=0
			break
		fi
		push_checkall=$((push_checkall + 1))
	done
	echo "push_checkall = $push_checkall"
	sleep 1
done