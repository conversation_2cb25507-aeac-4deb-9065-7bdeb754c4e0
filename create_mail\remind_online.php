<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */

// Gửi mail nhắc nhở nộp HSDT đối với người dùng bình thường follow bằng điểm
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';
require NV_ROOTDIR . '/functions/remind_theme.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$create_mail_file = NV_ROOTDIR . '/data/create_mail_remind_online_' . $prefix_lang . '_' . date('Ymd') . '.txt';
if (file_exists(NV_ROOTDIR . '/data/create_mail_remind_online_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "remind_online.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit();
}

if (file_exists(NV_ROOTDIR . '/data/create_mail_remind_online_' . $prefix_lang . '.txt')) {
    file_put_contents(NV_ROOTDIR . '/data/create_mail_remind_online_' . $prefix_lang . '.txt', NV_CURRENTTIME);
}

// Khởi tạo biến lưu thông tin thông báo
$arrInform = $list_info = [];

try {
    $time_start_thisday = mktime(0, 0, 0, date('n'), date('j'), date('Y'));
    $user = array();
    // Lấy danh sách follow bằng điểm, chỉ tính các bid_id có remind_hsdt = 1
    $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE userid IN (SELECT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE use_point = 2) AND send_status = 0 AND remind_hsdt = 1 LIMIT 20');
    $info_user = [];
    while ($row = $query->fetch()) {
        $user[] = $row['userid'];
    }

    // Kiểm tra xem có user theo dõi mua bằng điểm không
    if (!empty($user)) {
        // lấy thông tin user
        $arr_user = $db->query('SELECT userid, username, email, first_name, last_name FROM nv4_users WHERE userid IN (' . implode(',', $user) . ')');

        while ($row = $arr_user->fetch()) {
            $info_user[$row['userid']] = $row;
            $info_user[$row['userid']]['list_remind_bid'] = [];
        }
        
        if (!empty($info_user)) {
            
            // Lấy ra tất cả tin đấu thầu trong danh sách gửi đi
            foreach ($info_user as $userid => $v) {
                
                // Thử nghiệm gửi mail cho nội bộ công ty trước
                // if (!empty($v)) {
                    if (file_exists($create_mail_file)) {
                        file_put_contents($create_mail_file, 'BEGIN: ' . $userid . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
                    }
                    
                    $arr_bid = array();
                    $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row a INNER JOIN ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_detail b ON a.id=b.id WHERE a.id IN (SELECT bid_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE send_status = 0 AND bid_id>0 AND userid=' . $userid . ')');
                    while ($bid_row = $query->fetch()) {
                        $arr_bid[$bid_row['id']] = $bid_row;
                    }
                    
                    $array_bid_id = array();
                    $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE send_status = 0 AND userid=' . $userid . ' AND filter_id = 0 LIMIT 50');
    
                    while ($bid_data = $query->fetch()) {
                        $array_bid_id[$bid_data['id']] = $bid_data['id'];
                        $info_user[$userid]['list_remind_bid'][] = $arr_bid[$bid_data['bid_id']]; // danh sách các tbmt cần nhắc nhở của mỗi user
                    }
                // }

                $db->beginTransaction();
                $v['sub_email'] = $v['phone'] = '';

                try {
                    $remind_bids = $info_user[$userid]['list_remind_bid'];
                    $data_insert = array();
                    $data_insert['addtime'] = NV_CURRENTTIME;
                    $data_insert['send_time'] = 0;
                    $data_insert['status'] = 0;

                    if (!empty($remind_bids)) {
                        $data_insert['title'] = $lang_module['title_remind'];
                        $data_insert['type'] = 0;
                        $data_insert['vip'] = 0;
                        $arrInform = []; // khai báo để tránh lỗi
                        $data_insert['content'] = nv_theme_remind_mail($remind_bids, $arrInform, $v['username']);

                        if ($config_bidding['footer_email'] != '') {
                            $data_insert['content'] .= "<br/>" . $config_bidding['footer_email'];
                        }
                        try {
                            $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt1->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt1->bindParam(':main_mail', $v['email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':cc_mail', $v['sub_email'], PDO::PARAM_STR, strlen($v['sub_email']));
                            $stmt1->bindParam(':number_phone', $v['phone'], PDO::PARAM_STR);
                            $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc1 = $stmt1->execute();

                            $_mailid = $db->lastInsertId();
                            
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (Exception $e) {
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "remind_online.php ERROR INSERT INTO mail 175: " . $userid . "; " . print_r($e, true) . "\n\n");
                        }
                    }

                    // Thực hiện lưu biến thông báo tạm
                    foreach ($arrInform as $k => $v) {
                        foreach ($v as $v1) {
                            $list_info[] = $v1;
                        }
                    }

                    // Reset lại biến này về rỗng để tiến hành lưu mảng dữ liệu tiếp theo
                    $arrInform = [];

                    echo "userid = " . $userid . "\n";

                    // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                    if (!empty($array_bid_id) and !empty($_mailid)) {
                        $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_bid_id) . ') AND send_status = 0');
                    }
                    $db->commit();
                    file_put_contents($create_mail_file, "number bid = " . sizeof($array_bid_id) . ": " . implode(',', $array_bid_id) . "\n", FILE_APPEND);
                    file_put_contents($create_mail_file, "END: " . $userid . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
                } catch (PDOException $e) {
                    file_put_contents($create_mail_file, 'rollBack: ' . $userid . "\n\n", FILE_APPEND);
                    $db->rollBack();
                    echo '<pre>';
                    print_r($e);
                }
            }
        }
    }

    if (file_exists(NV_ROOTDIR . '/data/create_mail_tbmt_online' . $prefix_lang . '.txt')) {
        unlink(NV_ROOTDIR . '/data/create_mail_tbmt_online' . $prefix_lang . '.txt');
    }
} catch (PDOException $e) {
    file_put_contents($create_mail_file, 'ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "tbmt_follow_online.php ERROR: " . print_r($e, true) . "\n\n");
}

// Thực hiện lưu thông báo
if (!empty($list_info)) {
    echo "Có tổng " . sizeof($list_info) . ' Thông báo Inform';
    file_put_contents(NV_ROOTDIR . '/data/inform/inform_tbmt_follow_online' . uniqid('', true) . '.txt', json_encode($list_info));
}

echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
