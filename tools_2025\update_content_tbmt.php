<?php

/**
 * @Project DauThau
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License commercial
 * @Createdate 06/11/2023, 15:23
 * Tool cập nhật lại dữ liệu trường content_goods cho bảng nv4_vi_bidding_row
 * php update_content_goods.php --site_lang=en
 */


define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

set_time_limit(0);

$seperator = ' あ ';
$seperator_replace = ' SpaceKey '; // Do change_alias làm mất ký tự phân cách nên dùng cái này rồi replace lại sau

// Them nguon von vao content tbmt

if (file_exists(NV_ROOTDIR . '/tools_2025/update_content_tbmt_' . NV_LANG_DATA . '.txt')) {
    list($minid, $maxid) = array_map('intval', explode(',', file_get_contents(NV_ROOTDIR . '/tools_2025/update_content_tbmt_' . NV_LANG_DATA . '.txt')));
} else {
    list($minid, $maxid) = $db->query("SELECT MIN(id), MAX(id) FROM " . NV_PREFIXLANG . "_bidding_row")->fetch(3);
    $minid -= 1;
}
$config_elastic = $module_config['bidding'];

$id2 = $minid + 5000;
$run = 0;
$arr_row = [];
// so_tbmt,goi_thau,ten_du_an,ben_moi_thau,chu_dau_tu,dia_diem,notify_no,khlcnt_code,nguon_von
$_result = $db->query("SELECT id, so_tbmt, goi_thau, ben_moi_thau, notify_no FROM " . NV_PREFIXLANG . "_bidding_row WHERE id > $minid AND id <= $id2 AND investor_id <= 0 AND is_new_msc = 0 ORDER BY id ASC LIMIT 100");
while ($row = $_result->fetch()) {
    $arr_row[$row['id']] = $row;
    $minid = $row['id'];
}
$_result->closeCursor();
$arr_update = $params = [];

if (!empty($arr_row)) {
    $_sql = "SELECT id, khlcnt_code, ten_du_an, chu_dau_tu, dia_diem, nguon_von FROM " . NV_PREFIXLANG . "_bidding_detail WHERE id IN (" . implode(',', array_keys($arr_row)) . ")";
    $_result = $db->query($_sql);
    while ($tmp = $_result->fetch()) {
        $run++;
        $arr_row[$tmp['id']] = array_merge($arr_row[$tmp['id']], $tmp);
        $content = $content_full = [];
        $content_fields = 'so_tbmt,goi_thau,ten_du_an,ben_moi_thau,chu_dau_tu,dia_diem,notify_no,khlcnt_code,nguon_von';
        $content_fields = explode(',', $content_fields);
        foreach ($content_fields as $f) {
            if (!empty($arr_row[$tmp['id']][$f])) {
                $content_full[] = $arr_row[$tmp['id']][$f];
            }
        }
        $content_full = implode($seperator_replace, $content_full);
        $content = change_alias($content_full);
        $content = str_replace('-', ' ', $content);
        $content = str_replace($seperator_replace, $seperator, $content);
        $content_full = str_replace($seperator_replace, $seperator, $content_full);

        echo $arr_row[$tmp['id']]['so_tbmt'] . PHP_EOL;

        $params['body'][] = [
            'update' => [
                '_index' => 'dauthau_bidding',
                '_id' => $tmp['id']
            ]
        ];
        $params['body'][] = [
            'doc' => [
                'content' => $content,
                'content_full' => $content_full
            ]
        ];

        $arr_update[$tmp['id']] = [
            'content' => $content,
            'content_full' => $content_full
        ];
    }
    $_result->closeCursor();
}
if (!empty($params['body']) and !empty($params_en['body'])) {
    $hosts = array(
        $config_elastic['elas_host'] . ':' . $config_elastic['elas_port']
    );

    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_elastic['elas_user'], $config_elastic['elas_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
    $responses = $client->bulk($params);
    if (!empty($responses['items'])) {
        foreach ($responses['items'] as $value) {
            if ($value['update']['status'] == 200) {
                $_id = $value['update']['_id'];
                $db->exec('UPDATE ' . NV_PREFIXLANG . '_bidding_row SET content = ' . $db->quote($arr_update[$_id]['content']) . ', content_full = ' . $db->quote($arr_update[$_id]['content_full']) . ' WHERE id = ' . $_id);
            } else {
                $_id = $value['update']['_id'];
                $db->exec('UPDATE ' . NV_PREFIXLANG . '_bidding_row SET content = ' . $db->quote($arr_update[$_id]['content']) . ', content_full = ' . $db->quote($arr_update[$_id]['content_full']) . ', elasticsearch = 19 WHERE id = ' . $_id);
            }
        }
    }
}
$run == 0 && $minid = $id2;
file_put_contents(NV_ROOTDIR . '/tools_2025/update_content_tbmt_' . NV_LANG_DATA . '.txt', $minid . ',' . $maxid, LOCK_EX);
if ($run == 0 && $id2 > $maxid) {
    echo("đã chạy xong!!!");
    exit(1);
}

echo 'Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
die('Goodbye!!!');
