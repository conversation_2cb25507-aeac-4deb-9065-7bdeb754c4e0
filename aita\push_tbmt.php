<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/aita/config_aita.php';

$array_bid_id = [];
//$query = $db->query('SELECT bid_id, location FROM nv4_vi_aita_bid_id WHERE send_status=0 AND `location` LIKE \'push_khlcnt id=%\' ORDER BY `id` ASC LIMIT 100'); // send_status=0
$query = $db->query('SELECT bid_id FROM nv4_vi_aita_bid_id WHERE send_status=0 ORDER BY `id` ASC LIMIT 100'); // send_status=0
while ($_row = $query->fetch()) {
    $array_bid_id[] = $_row['bid_id'];
    /*
    $plan_id = substr($_row['location'], 15);
    $_count = $db->query('SELECT count(*) FROM nv4_vi_aita_plan_id WHERE plan_id=' . intval($plan_id) . ' AND send_status> 0 LIMIT 1')->fetchColumn(); // send_status=0
    if ($_count > 0) {
        $array_bid_id[] = $_row['bid_id'];
    } else {
        $db->query('UPDATE nv4_vi_aita_bid_id SET send_status=-3 WHERE bid_id=' . $_row['bid_id'] . ' AND send_status=0');
    }
    */
}
$query->closeCursor();

$location = 'push_tbmt.php';
if (!empty($array_bid_id)) {
    $db->query('UPDATE nv4_vi_aita_bid_id SET send_status=-9 WHERE bid_id IN (' . implode(',', $array_bid_id) . ') AND send_status=0');

    $sql = 'SELECT * FROM nv4_vi_bidding_row WHERE id IN (' . implode(',', $array_bid_id) . ')';
    $query = $db->query($sql);
    while ($bidding_row = $query->fetch()) {
        $id = $bidding_row['id'];
        echo "tbmt id = " . $id . "\n";
        if ($bidding_row['is_aita'] == -1) {
            // Bỏ qua các dữ liệu đánh đấu bằng -1
            continue;
        }

        $request = [
            // Tham số bắt buộc
            'apikey' => $apikey,
            'language' => 'vi',
            'module' => 'bidding',
            'action' => 'TBMT',
            'id' => $id
        ];

        $sql = 'SELECT * FROM nv4_vi_bidding_detail WHERE id=' . $id;
        $bidding_detail = $db->query($sql)->fetch();
        $request['bidding_detail'] = json_encode($bidding_detail);

        $sql = 'SELECT * FROM nv4_bidding_goods WHERE id_goi_thau=' . $id;
        $_bidding_goods = $db->query($sql)->fetchAll();
        $bid_cntt = 0;
        if (!empty($_bidding_goods)) {
            foreach ($_bidding_goods as $key => $value) {
                $phanmuc = goods_detail($value['name'] . ' ' . $value['description']);
                $is_cntt = 0;
                $_phanmuc = '';
                if (!empty($phanmuc)) {
                    $is_cntt = 1;
                    $bid_cntt = 1;
                    $_phanmuc = implode(',', array_unique($phanmuc));
                }
                $_bidding_goods[$key]['phanmuc'] = $_phanmuc;
                $_bidding_goods[$key]['is_cntt'] = $is_cntt;
            }
        }
        // nếu có hàng hóa là CNTT đánh dấu lại gói thầu đó là cntt ở nv4_vi_bidding_plans_contract
        if ($bid_cntt == 1) {
            $db->query('UPDATE nv4_vi_bidding_plans_contract SET is_aita = 1 WHERE tbmt_id=' . $id);
        }

        $request['bidding_goods'] = json_encode($_bidding_goods);

        $sql = 'SELECT * FROM nv4_vi_bidding_update WHERE bid_id=' . $id;
        $request['bidding_update'] = $db->query($sql)->fetchAll();

        $sql = 'SELECT * FROM nv4_vi_bidding_row_reason WHERE bid_id=' . $id;
        $request['bidding_row_reason'] = $db->query($sql)->fetchAll();
        if ($bidding_row['solicitor_id'] > 0) {
            $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $bidding_row['solicitor_id'];
            $request['bidding_solicitor'] = $db->query($sql)->fetch();

            $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $bidding_row['solicitor_id'];
            $request['bidding_solicitor_detail'] = $db->query($sql)->fetch();

            $db->query('UPDATE nv4_bidding_solicitor SET update_aita=' . time() . ' WHERE id=' . $bidding_row['solicitor_id']);
        }

        // tìm thêm theo tên chủ đầu tư
        $solicitor_id = get_solicitor_id($bidding_detail['chu_dau_tu']);
        if ($solicitor_id > 0) {
            if ($bidding_row['solicitor_id'] == 0) {
                $bidding_row['solicitor_id'] = $solicitor_id;
                $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $solicitor_id;
                $request['bidding_solicitor'] = $db->query($sql)->fetch();

                $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $solicitor_id;
                $request['bidding_solicitor_detail'] = $db->query($sql)->fetch();
            } else if ($bidding_row['solicitor_id'] != $solicitor_id) {
                $sql = 'SELECT * FROM nv4_bidding_solicitor WHERE id=' . $solicitor_id;
                $request['bidding_investor'] = $db->query($sql)->fetch();

                $sql = 'SELECT * FROM nv4_bidding_solicitor_detail WHERE id=' . $solicitor_id;
                $request['bidding_investor_detail'] = $db->query($sql)->fetch();
            }
            $db->query('UPDATE nv4_bidding_solicitor SET update_aita=' . time() . ' WHERE id=' . $solicitor_id);
        }

        //Phần lô
        $sql = 'SELECT * FROM nv4_bidding_detail_subdivision WHERE id_detail=' . $id;
        $detail_subdivision = $db->query($sql)->fetchAll();
        $request['detail_subdivision'] = json_encode($detail_subdivision);

        $timestamp = time();
        $request['hashsecret'] = password_hash($apisecret . '_' . $timestamp, PASSWORD_DEFAULT);
        $request['timestamp'] = $timestamp;
        $request['bidding_row'] = $bidding_row;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_remote_url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        $safe_mode = (ini_get('safe_mode') == '1' || strtolower(ini_get('safe_mode')) == 'on') ? 1 : 0;
        $open_basedir = ini_get('open_basedir') ? true : false;
        if (!$safe_mode and !$open_basedir) {
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERAGENT, $agent);

        curl_setopt($ch, CURLOPT_POST, sizeof($request));
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($request));
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $res = curl_exec($ch);
        curl_close($ch);

        $responsive = json_decode($res, true);
        if (isset($responsive['status']) and $responsive['status'] == 'success') {
            $location = 'push_tbmt id=' . $id;

            $db->query('UPDATE nv4_vi_bidding_row SET is_aita = 1 WHERE id =' . $id);
            $db->query('UPDATE nv4_vi_aita_bid_id SET send_status=' . $timestamp . ' WHERE bid_id=' . $id . ' AND send_status<=0');
            if (strlen($bidding_detail['khlcnt_code']) > 5) {
                // Kiểm tra xem đã có KHLCNT chưa, nếu chưa có cần bổ sung
                $arr_id_plan = [];
                $_query = $db->query("SELECT id FROM nv4_vi_bidding_plans WHERE code LIKE '" . $bidding_detail['khlcnt_code'] . "%'");
                while ($_r = $_query->fetch()) {
                    $arr_id_plan[] = $_r['id'];
                }
                $_query->closeCursor();
                if (!empty($arr_id_plan)) {
                    // Kiểm tra xem KHLCNT đã có trong bảng nv4_vi_aita_plan_id chưa, nếu chưa có thì
                    $arr_id_plan_exit = [];
                    $_query = $db->query("SELECT DISTINCT plan_id FROM nv4_vi_aita_plan_id WHERE plan_id IN (" . implode(',', $arr_id_plan) . ")");
                    while ($_r = $_query->fetch()) {
                        $arr_id_plan_exit[] = $_r['plan_id'];
                    }
                    $_query->closeCursor();

                    foreach ($arr_id_plan as $_id) {
                        if (!in_array($_id, $arr_id_plan_exit)) {
                            $db->exec('INSERT INTO `nv4_vi_aita_plan_id` (filter_id, plan_id, location, send_status, addtime) VALUES (0, ' . $_id . ',' . $db->quote($location) . ' , 0, ' . NV_CURRENTTIME . ')');
                        }
                    }
                }
            }

            // Kiểm tra bảng result, nếu có thì cập nhật sang aita
            $sql = "SELECT id FROM nv4_vi_bidding_result WHERE code=" . $db->quote($bidding_row['so_tbmt']);
            $result_id = $db->query($sql)->fetchColumn();
            if (!empty($result_id)) {
                $_count = $db->query("SELECT COUNT(*) FROM nv4_vi_aita_result_id WHERE result_id=" . $result_id)->fetchColumn();
                if (empty($_count)) {
                    $db->exec('INSERT INTO `nv4_vi_aita_result_id`(filter_id, result_id, location, send_status, addtime) VALUES (0, ' . $result_id . ',' . $db->quote($location) . ' , 0, ' . NV_CURRENTTIME . ')');
                }
            }
            echo "OK\n";
        } else {
            if (isset($responsive['status'])) {
                $error_info = print_r($responsive, true);
                print_r($responsive);
            } else {
                $error_info = $res;
                echo $res . "\n";
            }
            $db->query('UPDATE nv4_vi_aita_bid_id SET send_status=-' . $timestamp . ', error_info= ' . $db->quote($error_info) . ' WHERE bid_id=' . $id . ' AND send_status<=0');
        }
    }
    $query->closeCursor();
} else {
    die("No Data");
}
