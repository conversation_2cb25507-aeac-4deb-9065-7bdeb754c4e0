<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__FILE__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$hosts = array($config_bidding['elas_host'] . ':' . $config_bidding['elas_port']);
$file_run = NV_ROOTDIR . '/tools/update_type_choose_id.txt';

if (file_exists($file_run)) {
    list($id, $id_max) = explode('_', file_get_contents($file_run));
    $id = intval($id);
    $id_max = intval($id_max);
} else {
    $id = $db->query("SELECT MIN(id) FROM nv4_vi_bidding_row WHERE type_choose_id = 0")->fetchColumn();
    $id_max = $db->query("SELECT MAX(id) FROM nv4_vi_bidding_row WHERE type_choose_id = 0")->fetchColumn();
    if (empty($id) || empty($id_max)) {
        echo "Không có bản ghi nào cần cập nhật\n";
        exit(1);
    }
}

$client = Elastic\Elasticsearch\ClientBuilder::create()
    ->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
    ->setHosts($hosts)
    ->setRetries(0)
    ->build();

$DM_LHDLCNT = [];
statusPTLCNT($DM_LHDLCNT);

echo "========= BEGIN =========\n";

try {
    $sql = $db->query("SELECT id, hinh_thuc_lua_chon, type_choose_id FROM nv4_vi_bidding_row WHERE id >= " . $id . " AND id <= " . $id_max . " AND type_choose_id = 0 ORDER BY id ASC LIMIT 100");
    $params = [];
    $num_rows = 0;
    $last_processed_id = $id;
    while ($row = $sql->fetch()) {
        echo "BEGIN: id: " . number_format($row['id']) . "\n";
        $type_choose_id = $row['type_choose_id'];

        if ($row['type_choose_id'] == 0) {
            $type_choose_id = get_htdauthau($DM_LHDLCNT[$row['hinh_thuc_lua_chon']]['name']);
            if ($type_choose_id) {
                // Cập nhật vào cơ sở dữ liệu
                $sqlUpdate_vi = "UPDATE nv4_vi_bidding_row SET type_choose_id = " . $type_choose_id . " WHERE id = " . $row['id'];
                $db->exec($sqlUpdate_vi);
                $sqlUpdate_en = "UPDATE nv4_en_bidding_row SET type_choose_id = " . $type_choose_id . " WHERE id = " . $row['id'];
                $db->exec($sqlUpdate_en);
            }
        }

        $params['body'][] = [
            'update' => [
                '_index' => 'dauthau_bidding',
                '_id' => $row['id']
            ]
        ];
        $params['body'][] = [
            'doc' => [
                'type_choose_id' => $type_choose_id
            ]
        ];
        
        $params['body'][] = [
            'update' => [
                '_index' => 'en_dauthau_bidding',
                '_id' => $row['id']
            ]
        ];
        
        $params['body'][] = [
            'doc' => [
                'type_choose_id' => $type_choose_id
            ]
        ];

        $last_processed_id = $row['id'];
        $num_rows++;
    }

    $sql->closeCursor();

    if (!empty($params)) {
        $response = $client->bulk($params);
        file_put_contents($file_run, $last_processed_id . '_' . $id_max);
    }
} catch (PDOException $e) {
    trigger_error($e);
    exit(1);
}

if ($num_rows == 0 || $id >= $id_max) {
    echo "Hoàn thành cập nhật\n";
    exit(1);
}

echo "\n========= END =========\n";
echo "\nThời gian thực hiện = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . " giây\n";

function get_htdauthau($hinhthuc)
{
    $array_htdauthau = [
        'trong_nuoc' => 1,
        'quoc_te' => 2,
        'khong_so_tuyen' => 3,
        'so_tuyen' => 4,
        'khong_qua_mang' => 5,
        'qua_mang' => 6,
        'mot_giai_doan_mot_tui_ho_so' => 7,
        'mot_giai_doan_hai_tui_ho_so' => 8,
        'hai_giai_doan_mot_tui_ho_so' => 9,
        'hai_giai_doan_hai_tui_ho_so' => 10,
        'chao_hang_canh_tranh_rut_gon' => 11,
        'chao_hang_canh_tranh' => 12,
        'chi_dinh_thau_rut_gon' => 13,
        'chi_dinh_thau' => 14,
        'dam_phan_gia' => 15,
        'dau_thau_han_che' => 16,
        'dau_thau_rong_rai' => 17,
        'lua_chon_theo_muc_ngan_sach_co_dinh' => 18,
        'mua_sam_truc_tiep' => 19,
        'tham_gia_thuc_hien_cong_dong' => 20,
        'thu_moi_bay_to_quan_tam' => 21,
        'trong_truong_hop_dac_biet' => 22,
        'tu_thuc_hien' => 23,
        'tuyen_chon_dua_tren_nang_luc' => 24,
        'tuyen_chon_tren_co_so_chat_luong_va_chi_phi' => 25,
        'tuyen_chon_tren_co_so_chat_luong' => 26,
        'tuyen_chon_tu_mot_nguon_duy_nhat' => 27,
        'tuyen_chon_tu_van_ca_nhan' => 28,
        'tuyen_chon_tu_van_co_chi_phi_thap' => 29,
        'lua_chon_nha_thau_trong_truong_hop_dac_biet' => 30,
        'tu_van_ca_nhan' => 31,
        'dam_phan_canh_tranh' => 32,
        'tuyen_chon_chat_luong_va_chi_phi' => 33,
        'tuyen_chon_chat_luong' => 34,
        'lua_chon_ngan_sach_co_dinh' => 35,
        'tuyen_chon_chi_phi_thap' => 36,
        'tuyen_chon_nang_luc' => 37,
        'tuyen_chon_nguon_duy_nhat' => 38,
        'tuyen_chon_tu_van_trong_khoan_vay_the_che' => 39,
        'tuyen_chon_tu_van_trong_khoan_vay_bao_dam' => 40,
        'tuyen_chon_tu_van_dac_biet' => 41
    ];
    $hinhthuc = change_alias(trim($hinhthuc));
    $hinhthuc = strtolower(str_replace('-', '_', $hinhthuc));
    $id_hinhthuc = 0;
    if (isset($array_htdauthau[$hinhthuc])) {
        $id_hinhthuc = $array_htdauthau[$hinhthuc];
    }
    return $id_hinhthuc;
}
function statusPTLCNT(&$DM_LHDLCNT)
{
    $status = '{"DM_LHDLCNT": [
        {
        "code": "DGCD",
        "name": "Theo đơn giá cố định",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "DGDC",
        "name": "Theo đơn giá điều chỉnh",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TTG",
        "name": "Theo thời gian",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TG_DGCD",
        "name": "Trọn gói và Theo đơn giá cố định",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TG_DGDC",
        "name": "Trọn gói và Theo đơn giá điều chỉnh",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "DGCD_DC",
        "name": "Theo đơn giá cố định và Theo đơn giá điều chỉnh",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TG_CD_DC",
        "name": "Trọn gói, Theo đơn giá cố định và Theo đơn giá điều chỉnh",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TG_CD_TTG",
        "name": "Trọn gói, Theo đơn giá cố định và Theo thời gian",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TG_DC_TTG",
        "name": "Trọn gói, Theo đơn giá điều chỉnh và Theo thời gian",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "CD_DC_TTG",
        "name": "Theo đơn giá cố định, Theo đơn giá điều chỉnh và Theo thời gian",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TG_CD_DC_TTG",
        "name": "Trọn gói, Theo đơn giá cố định, Theo đơn giá điều chỉnh và Theo thời gian",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "CD_TTG",
        "name": "Theo đơn giá cố định và Theo thời gian",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "DC_TTG",
        "name": "Theo đơn giá điều chỉnh và Theo thời gian",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TG_TTG",
        "name": "Trọn gói và Theo thời gian",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TGG_CD_DC",
        "name": "Theo thời gian, Theo đơn giá cố định và đơn giá điều chỉnh",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TGG_DGCD",
        "name": "Theo thời gian và Theo đơn giá cố định",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TGG_DGDC",
        "name": "Theo thời gian và Theo đơn giá điều chỉnh",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "TG",
        "name": "Trọn gói",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "KHAC",
        "name": "Khác",
        "categoryTypeCode": "DM_LHDLCNT"
      },
      {
        "code": "DTRR",
        "name": "Đấu thầu rộng rãi",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "CHCT",
        "name": "Chào hàng cạnh tranh",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "CHCTRG",
        "name": "Chào hàng cạnh tranh rút gọn",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "DTHC",
        "name": "Đấu thầu hạn chế",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "MSTT",
        "name": "Mua sắm trực tiếp",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "CDT",
        "name": "Chỉ định thầu",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "CDTRG",
        "name": "Chỉ định thầu rút gọn",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "TTH",
        "name": "Tự thực hiện",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "LCNT_DB",
        "name": "Lựa chọn nhà thầu trong trường hợp đặc biệt",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "TVCN",
        "name": "Tư vấn cá nhân",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "TCTVCN",
        "name": "Tuyển chọn tư vấn cá nhân",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "DPCT",
        "name": "Đàm phán cạnh tranh",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "QCBS",
        "name": "Tuyển chọn trên cơ sở Chất lượng và Chi phí (QCBS)",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "DPG",
        "name": "Đàm phán giá",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "QBS",
        "name": "Tuyển chọn tư vấn dựa trên cơ sở chất lượng (QBS)",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "FBS",
        "name": "Lựa chọn theo mức ngân sách cố định (FBS)",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "LCS",
        "name": "Tuyển chọn tư vấn có chi phí thấp nhất (LCS)",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "CQS",
        "name": "Tuyển chọn dựa trên năng lực (CQS)",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "SSS",
        "name": "Tuyển chọn từ một nguồn duy nhất (SSS)",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "TGTC",
        "name": "Tuyển chọn tư vấn trong các khoản vay cho các thể chế hoặc tổ chức trung gian tài chính",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "NHBD",
        "name": "Tuyển chọn tư vấn trong các khoản vay được Ngân hàng bảo đảm",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "TVCT",
        "name": "Tuyển chọn một số loại hình tư vấn cụ thể",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "TGTHCD",
        "name": "Tham gia thực hiện cộng đồng",
        "categoryTypeCode": "DM_HTLCNT"
      },
      {
        "code": "1_MTHS",
        "name": "Một giai đoạn một túi hồ sơ",
        "categoryTypeCode": "DM_PTLCNT"
      },
      {
        "code": "1_HTHS",
        "name": "Một giai đoạn hai túi hồ sơ",
        "categoryTypeCode": "DM_PTLCNT"
      },
      {
        "code": "2_MTHS",
        "name": "Hai giai đoạn một túi hồ sơ",
        "categoryTypeCode": "DM_PTLCNT"
      },
      {
        "code": "2_HTHS",
        "name": "Hai giai đoạn hai túi hồ sơ",
        "categoryTypeCode": "DM_PTLCNT"
      },
      {
        "code": "Other",
        "name": "Khác",
        "categoryTypeCode": "DM_LVLCNT"
      },
      {
        "code": "HH",
        "name": "Hàng hóa",
        "categoryTypeCode": "DM_LVLCNT"
      },
      {
        "code": "XL",
        "name": "Xây lắp",
        "categoryTypeCode": "DM_LVLCNT"
      },
      {
        "code": "PTV",
        "name": "Phi tư vấn",
        "categoryTypeCode": "DM_LVLCNT"
      },
      {
        "code": "TV",
        "name": "Tư vấn",
        "categoryTypeCode": "DM_LVLCNT"
      },
      {
        "code": "HON_HOP",
        "name": "Hỗn hợp",
        "categoryTypeCode": "DM_LVLCNT"
      }
    ]}';
    $status = json_decode($status, true)['DM_LHDLCNT'];
    foreach ($status as $k => $v) {
        $DM_LHDLCNT[$v['code']] = $v;
    }
}
