<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

// Bóc lại plans_contract có code bị rỗng

$crconfig = array();
$crconfig['dbhost'] = 'db_dauthau';
$crconfig['dbport'] = '';
$crconfig['dbname'] = 'dauthau_crawls';
$crconfig['dbuname'] = 'root';
$crconfig['dbpass'] = '';
$crconfig['dbtype'] = 'mysql';
$crconfig['collation'] = 'utf8_unicode_ci';
$crconfig['charset'] = 'utf8';
$crconfig['persistent'] = false;
$crconfig['prefix'] = 'nv4';
$dsn = $crconfig['dbtype'] . ':dbname=' . $crconfig['dbname'] . ';host=' . $crconfig['dbhost'] . ';charset=' . $crconfig['charset'];
if (!empty($crconfig['dbport'])) {
    $dsn .= ';port=' . $crconfig['dbport'];
}
$driver_options[PDO::ATTR_ERRMODE] = PDO::ERRMODE_EXCEPTION;
try {
    $dbcr = new PDO($dsn, $crconfig['dbuname'], $crconfig['dbpass'], $driver_options);
    $dbcr->exec("SET SESSION time_zone='" . $_time_zone_db . "'");
} catch (PDOException $e) {
    echo 'PDOException CR';
    print_r($e);
    die();
}


$last_id = 0;
do {
    $query = $db->query('SELECT t1.* FROM nv4_vi_bidding_plans t1 INNER JOIN nv4_vi_bidding_plans_contract t2 ON t1.id=t2.id_plan WHERE t1.is_new_msc=1 AND t2.code="" AND t1.id > ' . $last_id . ' GROUP BY t1.id LIMIT 1000');
    $last_id = 0;
    while ($_row = $query->fetch()) {
        $last_id = $_row['id'];
        echo $last_id . "\t";
        $dbcr->query('UPDATE nv22_khlcnt_url SET url_run=0 WHERE planno = ' . $dbcr->quote(explode('-', $_row['code'])[0]));
        echo "\n";
    }

    $query->closeCursor();
} while ($last_id > 0);
DIE('end');
