<?php

/**
 * @Project DauThau
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License commercial
 * @Createdate 06/11/2023, 15:23
 * Tool cập nhật lại dữ liệu trường content_goods cho bảng nv4_vi_bidding_row
 * php update_content_goods.php --site_lang=en
 */


define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$filename = NV_ROOTDIR . '/tools/update_content_goods_new_' . NV_PREFIXLANG . '.txt';
if (file_exists($filename)) {
    $_last_id = intval(file_get_contents($filename));
} else {
    $_last_id = 0;
}

$sql = "SELECT max(id) FROM `" . NV_PREFIXLANG . "_bidding_row`";
$max_id = $db->query($sql)->fetchColumn();

do {
    echo "_last_id_" . NV_PREFIXLANG . ": " . $_last_id . "\n";
    $_last_id2 = $_last_id + 1000;
    $params = [];
    $sql = "SELECT id, content_goods FROM `" . NV_PREFIXLANG . "_bidding_row` WHERE id > " . $_last_id . " AND id < " . $_last_id2 . " ORDER BY id ASC LIMIT 100";
    $_query = $db->query($sql);
    $array_tbmt = [];
    $_last_id = 0;
    while ($row = $_query->fetch()) {
        $_last_id = $row['id'];
        if ($_last_id > $max_id) {
            break;
        }
        if (!empty($row['content_goods'])) {
            $row['content_goods'] = strtolower($row['content_goods']);
            $params['body'][] = [
                'update' => [
                    '_index' => NV_LANG_ELASTIC . 'dauthau_bidding',
                    '_id' => $row['id']
                ]
            ];
            $params['body'][] = [
                'doc' => [
                    'content_goods' => $row['content_goods']
                ]
            ];
            $array_tbmt[$row['id']] = $row['content_goods'];
        }
    }
    $_query->closeCursor();

    if (!empty($params['body'])) {
        $hosts = array(
            $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
        );

        $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
            ->setHosts($hosts)
            ->setRetries(0)
            ->build();
        $responses = $client->bulk($params)->asArray();
        if (!empty($responses['items'])) {
            foreach ($responses['items'] as $value) {
                if ($value['update']['status'] == 200) {
                    $_id = $value['update']['_id'];
                    $exec = $db->exec("UPDATE " . NV_PREFIXLANG . "_bidding_row SET content_goods = " . $db->quote($array_tbmt[$_id]) . " WHERE id=" . $_id);
                }
            }
        }
        file_put_contents($filename, $_last_id);
    }

    if ($_last_id > $max_id) {
        echo ('Thực hiện xong');
        break;
    } elseif (empty($_last_id)) {
        $_last_id = $_last_id2;
        if ($_last_id > $max_id) {
            echo ('Thực hiện xong');
            break;
        }
    }
} while ($_last_id > 0);
die("Thực hiện xong");

