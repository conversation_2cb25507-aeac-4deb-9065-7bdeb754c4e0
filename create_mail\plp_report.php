<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$create_mail_file = NV_ROOTDIR . '/data/create_mail_plp_report_' . $prefix_lang . '_' . date('Ymd') . '.txt';
if (file_exists(NV_ROOTDIR . '/data/create_mail_plp_report_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "plp_report.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit();
}

$this_month = new DateTime('first day of last month');
$this_month->setTime(0, 0, 0);
$this_month = $this_month->getTimestamp();

// Kiểm tra xem file tháng này đã xuất ra chưa
$is_done = false;
if ($db->query('SELECT config_value FROM nv4_config WHERE config_name = "plp_export_month" AND module = "businesslistings" AND lang = "' . NV_LANG_DATA . '"')->fetchColumn() == $this_month) {
    $is_done = true;
}

if (!$is_done) {
    echo "Chưa xuất ra file tháng này\n";
    exit();
}

$name_show = $db->query("SELECT config_value FROM nv4_config WHERE config_name='name_show' AND lang='" . NV_LANG_DATA . "' AND module='global'")->fetchColumn();

file_put_contents(NV_ROOTDIR . '/data/create_mail_plp_report_' . $prefix_lang . '.txt', NV_CURRENTTIME);
try {
    // Đọc bảng nv4_bidding_customs lấy ra các tài khoản vip = 32, status = 1, last_email < $this_month
    $arr_vip = $db->query('SELECT id as cusid, phone, user_id as userid, email, sub_email, time_send, last_email, from_time FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE vip=32 AND status = 1 AND prefix_lang = ' . $prefix_lang . ' AND last_email < ' . $this_month . ' LIMIT 10');
    while ($vip_data = $arr_vip->fetch()) {
        $vips[$vip_data['userid']] = $vip_data;
    }
    if (!empty($vips)) {
        // lấy username
        $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid IN ( ' . implode(',', array_keys($vips)) . ')');
        while ($user = $arr_user->fetch()) {
            $vips[$user['userid']]['fullname'] = nv_show_name_user($user['first_name'], $user['last_name'], $user['username']);
            $vips[$user['userid']]['username'] = $user['username'];
        }

        // Lưu nội dung mail vào csdl
        foreach ($vips as $vip_id => $vip) {
            // Cập nhật lại lần cuối cùng gửi mail cho các tài khoản VIP, Phần này cần chạy trước beg-----inTransaction để nếu có lỗi thì bỏ qua 1 tài khảo.
            $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET last_email = ' . $this_month . ' WHERE id =' . $vip['cusid'] . ' AND vip = 32');
            $db->beginTransaction();
            file_put_contents($create_mail_file, 'BEGIN: ' . $vip_id . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
            try {
                $data_insert = [];
                $data_insert['addtime'] = NV_CURRENTTIME;
                $data_insert['send_time'] = 0;
                $data_insert['status'] = 0;
                // Gửi mail kết quả lựa chọn nhà thầu cho các VIP
                $data_insert['title'] = sprintf($lang_module['title_plp_report'], nv_date('m/Y', $this_month));
                $data_insert['type'] = 0;
                $data_insert['vip'] = 32;
                    
                $data_insert['content'] = nv_theme_plp_report_mail($vip);
                $data_insert['content'] .= sprintf($lang_module['mail_footer_plp'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date('d/m/Y', $vip['from_time']));

                // Nội dung htm sẽ gửi cho từng khách.
                try {
                    $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                    $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                    $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                    $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($row['cc_mail']));
                    $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                    $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                    $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                    $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                    $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                    $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                    $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                    $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                    $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                    $exc = $stmt->execute();

                    $_mailid = $db->lastInsertId();
                    file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                } catch (PDOException $e) {
                    print_r('Lỗi thêm mail kết quả chọn thầu vào csdl');
                    file_put_contents($create_mail_file, "ERROR INSERT INTO mail: 99 " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);

                    trigger_error($e);
                    echo($e->getMessage()); // Remove this line after checks finished
                    break;
                }

                echo "vip_id = " . $vip_id . "\n";
                $db->commit();
                file_put_contents($create_mail_file, "END: " . $vip_id . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
                // print_r($array_result_id);
            } catch (PDOException $e) {
                file_put_contents($create_mail_file, 'rollBack: ' . $vip_id . "\n\n", FILE_APPEND);
                $db->rollBack();
                echo '<pre>';
                print_r($e);
            }
        }
    }

    unlink(NV_ROOTDIR . '/data/create_mail_plp_report_' . $prefix_lang . '.txt');
} catch (PDOException $e) {
    file_put_contents($create_mail_file, 'ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "plp_report.php ERROR: " . print_r($e, true) . "\n\n");
}
echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function nv_theme_plp_report_mail($vip)
{
    global $db, $create_mail_file, $this_month;

    if (!empty($vip)) {
        $xtpl = new XTemplate('plp_report.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('USER', $vip);
        $link_download = 'https://dauthau.asia/businesslistings/plpreport/';
        $xtpl->assign('LINK_DOWNLOAD', $link_download);
        $xtpl->assign('CUR_MONTH', nv_date('m/Y', $this_month));
        $xtpl->assign('CUR_MONTH_F', nv_date('m-Y', $this_month));
        $date = new DateTime("last day of next month");
        $xtpl->assign('LAST_DAY_MONTH', $date->format('d/m/Y'));
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_show_name_user($first_name, $last_name, $user_name = '', $lang = '')
{
    global $name_show, $db;

    $full_name = $name_show ? $first_name . ' ' . $last_name : $last_name . ' ' . $first_name;
    $full_name = trim($full_name);

    return empty($full_name) ? $user_name : $full_name;
}
