#!/bin/bash

SOURCE="${BASH_SOURCE[0]}"
while [ -h "$SOURCE" ]; do
  TARGET="$(readlink "$SOURCE")"
  if [[ $TARGET == /* ]]; then
    SOURCE="$TARGET"
  else
    DIR="$( dirname "$SOURCE" )"
    SOURCE="$DIR/$TARGET"
  fi
done
DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
cd "$DIR/"
DIR_PATH=$PWD

echo '---------------- B<PERSON>t đầu chạy tool active profile dtnet ----------------'
echo ''

while : ; do
  php $DIR_PATH/active_profile_dtnet.php
  code=$?
  if [[ $code == 1 ]]; then
    echo "Đã hoàn tất"
    break
  fi
  sleep 1
done 