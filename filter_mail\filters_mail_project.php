<?php
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$filters_mail_log = NV_ROOTDIR . '/data/filters_mail_project_log_' . $prefix_lang . '_' . date('Ymd') . '.txt';
$new_loop = 0;
$last_row_id = array();
$logs_info = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_logs WHERE status=0 limit 1')->fetch();
if (!empty($logs_info)) {
    $last_row_id = $logs_info;
    $last_userid = $logs_info['userid'];
} else {
    // lấy id mới nhất
    $row_tbmt = $db->query('SELECT max(id) as to_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_row')->fetch(); // TBMT
    $row_cbdmda = $db->query('SELECT max(id) as to_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_proposal')->fetch(); // CBDMDA
    $row_plans = $db->query('SELECT max(id) as to_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_plans_project')->fetch(); // plans
    $row_result = $db->query('SELECT max(id) as to_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result_project')->fetch(); // result
    $row_tbmst = $db->query('SELECT max(id) as to_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_investor_prequalification')->fetch(); // tbmst
    $row_kqst = $db->query('SELECT max(id) as to_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqst_project')->fetch(); // kqst
                                                                                                                               // $row_hd = $db->query('SELECT max(id) as to_id FROM ')->fetch(); //hợp đồng
    $row_hd = array();
    $row_hd['to_id'] = 0;

    $last_logs = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_logs ORDER BY id DESC limit 1')->fetch();
    if (empty($last_logs)) { // lần đầu
        $logs_info = $last_row_id;

        $last_row_id['from_id'] = 0;
        $last_row_id['from_plan'] = 0;
        $last_row_id['from_pq'] = 0;
        $last_row_id['begin_cbdmda'] = 0;
        $last_row_id['begin_kqlcndt'] = 0;
        $last_row_id['begin_kqst'] = 0;
        $last_row_id['begin_hd'] = 0;

        $last_row_id['to_id'] = $row_tbmt['to_id'];
        $last_row_id['to_plan'] = $row_plans['to_id'];
        $last_row_id['to_cbdmda'] = $row_cbdmda['to_id'];
        $last_row_id['to_kqlcndt'] = $row_result['to_id'];
        $last_row_id['to_kqst'] = $row_kqst['to_id'];
        $last_row_id['to_pq'] = $row_tbmst['to_id'];
        $last_row_id['to_hd'] = $row_hd['to_id'];
        $last_userid = 0;
        $new_loop = 1;
    } else {
        if ($last_logs['to_id'] < $row_tbmt['to_id'] or $last_logs['to_plan'] < $row_plans['to_id'] or $last_logs['to_cbdmda'] < $row_cbdmda['to_id'] or $last_logs['to_kqlcndt'] < $row_result['to_id'] or $last_logs['to_kqst'] < $row_kqst['to_id'] or $last_logs['to_pq'] < $row_tbmst['to_id']) {
            $new_loop = 1;
            $last_userid = 0;
            $last_row_id['from_id'] = $last_logs['to_id'];
            $last_row_id['from_plan'] = $last_logs['to_plan'];
            $last_row_id['from_pq'] = $last_logs['to_pq'];
            $last_row_id['begin_cbdmda'] = $last_logs['to_cbdmda'];
            $last_row_id['begin_kqlcndt'] = $last_logs['to_kqlcndt'];
            $last_row_id['begin_kqst'] = $last_logs['to_kqst'];
            $last_row_id['begin_hd'] = $last_logs['to_hd'];

            $last_row_id['to_id'] = $row_tbmt['to_id'];
            $last_row_id['to_plan'] = $row_plans['to_id'];
            $last_row_id['to_cbdmda'] = $row_cbdmda['to_id'];
            $last_row_id['to_kqlcndt'] = $row_result['to_id'];
            $last_row_id['to_kqst'] = $row_kqst['to_id'];
            $last_row_id['to_pq'] = $row_tbmst['to_id'];
            $last_row_id['to_hd'] = $row_hd['to_id'];
        } else {
            file_put_contents($filters_mail_log, "Không có tin mới trong csdl. Hãy chạy tiếp khi có bài đăng mới\n", FILE_APPEND);
            die('Không có tin mới');
        }
    }
}

$time = time();
$start_time = $time;
if ($new_loop) {
    // thêm logs mới vào csdl
    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_logs (from_id, to_id, from_plan, to_plan, from_pq, to_pq, begin_cbdmda, to_cbdmda, begin_kqlcndt, to_kqlcndt, begin_kqst, to_kqst, begin_hd, to_hd, from_time, to_time, total_time, userid, status) VALUES (:from_id, :to_id, :from_plan, :to_plan, :from_pq, :to_pq, :begin_cbdmda, :to_cbdmda, :begin_kqlcndt, :to_kqlcndt, :begin_kqst, :to_kqst, :begin_hd, :to_hd, :from_time, :to_time, 0, 0, 0)');
    $stmt->bindParam(':from_id', $last_row_id['from_id'], PDO::PARAM_INT);
    $stmt->bindParam(':to_id', $last_row_id['to_id'], PDO::PARAM_INT);

    $stmt->bindParam(':from_plan', $last_row_id['from_plan'], PDO::PARAM_INT);
    $stmt->bindParam(':to_plan', $last_row_id['to_plan'], PDO::PARAM_INT);

    $stmt->bindParam(':from_pq', $last_row_id['from_pq'], PDO::PARAM_INT);
    $stmt->bindParam(':to_pq', $last_row_id['to_pq'], PDO::PARAM_INT);

    $stmt->bindParam(':begin_cbdmda', $last_row_id['begin_cbdmda'], PDO::PARAM_INT);
    $stmt->bindParam(':to_cbdmda', $last_row_id['to_cbdmda'], PDO::PARAM_INT);

    $stmt->bindParam(':begin_kqlcndt', $last_row_id['begin_kqlcndt'], PDO::PARAM_INT);
    $stmt->bindParam(':to_kqlcndt', $last_row_id['to_kqlcndt'], PDO::PARAM_INT);

    $stmt->bindParam(':begin_kqst', $last_row_id['begin_kqst'], PDO::PARAM_INT);
    $stmt->bindParam(':to_kqst', $last_row_id['to_kqst'], PDO::PARAM_INT);

    $stmt->bindParam(':begin_hd', $last_row_id['begin_hd'], PDO::PARAM_INT);
    $stmt->bindParam(':to_hd', $last_row_id['to_hd'], PDO::PARAM_INT);

    $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
    $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
    $exc = $stmt->execute();
    $logs_id = $db->lastInsertId();
    $run_time = 0;
} else {
    $logs_id = $logs_info['id'];
    $run_time = $logs_info['total_time'];
}

// lấy vip
$number_user_scan_all = 50;
$time_start_thisday = mktime(date('H'), date('i'), date('s'), date('n'), date('j'), date('Y'));
$query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id >' . $last_userid . ' AND vip=4 AND prefix_lang = ' . $prefix_lang . ' ORDER BY user_id ASC limit ' . $number_user_scan_all);
$number_user_scan = 0;
$user_id_end = 0;
$arr_vip = $arr_id_vip = array();
while ($vip_info = $query_vip->fetch()) {
    ++$number_user_scan;
    $user_id_end = $vip_info['user_id'];
    $arr_vip[$vip_info['user_id']] = $vip_info;
    $arr_vip[$vip_info['user_id']]['filter'] = array();

    $arr_id_vip[$vip_info['user_id']] = $vip_info['user_id'];
}

// bộ lọc
if (!empty($arr_id_vip)) {
    $query_filter = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE userid IN (' . implode(',', $arr_id_vip) . ') AND status=1 AND vip_use=4 AND prefix_lang = ' . $prefix_lang . '');
    while ($result = $query_filter->fetch()) {
        $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
    }
}

// lọc tin -> chuyển bảng tmp trước khi chuyển thành mail
if (empty($arr_vip)) {
    // Nếu không lấy được VIP nào thì coi như đã chạy xong một vòng và update lại status là đã chạy xong
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_logs SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_log, "Đã quét xong một lượt các vip. Tổng thời gian cho một lượt:" . $total_time . "\n", FILE_APPEND);
    die();
} else {
    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_logs SET userid =' . $user_id_end . ' WHERE id =' . $logs_id);
    $db->beginTransaction();
    try {
        foreach ($arr_vip as $vip_id => $vip) {
            echo "Bắt đầu quét:" . $vip_id . "\n";
            file_put_contents($filters_mail_log, "Bắt đầu quét:" . $vip_id . "\n", FILE_APPEND);
            if (!empty($vip['filter'])) {
                foreach ($vip['filter'] as $filter) {
                    if (!empty($filter)) {
                        $where = ' 1=1 ';
                        if (!empty($filter['key_search'])) {
                            $where .= ' AND (';
                            $num = 0;
                            $arr_key = explode(',', $filter['key_search']);
                            foreach ($arr_key as $key) {
                                if ($filter['vip_use2'] == 1 or $filter['vip_use2'] == 5) {
                                    $key = str_replace('-', ' ', change_alias($key));
                                }
                                $key = trim($key);
                                if ($num == 0) {
                                    $where .= "content LIKE " . $db->quote('%' . $key . '%');
                                } else {
                                    $where .= " OR content LIKE " . $db->quote('%' . $key . '%');
                                }
                                $num++;
                            }
                            $where .= ')';
                        }
                        if (!empty($filter['without_key'])) {
                            // Tìm theo từ khóa loại trừ
                            $where .= ' AND (';
                            $num = 0;
                            $arr_key = explode(',', $filter['without_key']);
                            foreach ($arr_key as $key) {
                                if ($filter['vip_use2'] == 1 or $filter['vip_use2'] == 5) {
                                    $key = str_replace('-', ' ', change_alias($key));
                                }
                                $key = trim($key);
                                if ($num == 0) {
                                    $where .= "content NOT LIKE " . $db->quote('%' . $key . '%');
                                } else {
                                    $where .= " AND content NOT LIKE " . $db->quote('%' . $key . '%');
                                }
                                $num++;
                            }
                            $where .= ')';
                        }
                        if (!empty($filter['key_search2'])) {
                            // Tìm theo từ khóa cùng
                            $where .= ' AND (';
                            $num = 0;
                            $arr_key = explode(',', $filter['key_search2']);
                            $operators = !empty($filter['search_one_key']) ? ' OR ' : ' AND ';
                            foreach ($arr_key as $key) {
                                if ($filter['vip_use2'] == 1 or $filter['vip_use2'] == 5) {
                                    $key = str_replace('-', ' ', change_alias($key));
                                }
                                $key = trim($key);
                                if ($num == 0) {
                                    $where .= "content LIKE " . $db->quote('%' . $key . '%');
                                } else {
                                    $where .= $operators . "content LIKE " . $db->quote('%' . $key . '%');
                                }
                                $num++;
                            }
                            $where .= ')';
                        }

                        $num_row = 0;
                        if ($filter['vip_use2'] == 2) { // TBMT
                            $where .= ' AND id>' . $last_row_id['from_id'] . ' AND id<=' . $last_row_id['to_id'];
                            if ($filter['time_find'] > 0) {
                                $time_find = $time_start_thisday - 86400 * $filter['time_find'];
                                $where .= ($filter['type'] == 0) ? ' AND ngay_dang_tai>=' . $time_find : ' AND den_ngay>=' . $time_find;
                            }
                            if ($filter['money_from'] > 0) {
                                $where .= " AND money_bid >= " . $filter['money_from'];
                            }
                            if ($filter['money_to'] > 0) {
                                $where .= " AND money_bid <= " . $filter['money_to'];
                            }
                            if ($filter['price_from'] > 0) {
                                $where .= " AND price >= " . $filter['price_from'];
                            }
                            if ($filter['price_to'] > 0) {
                                $where .= " AND price <= " . $filter['price_to'];
                            }
                            if ($filter['field'] != '') {
                                $where .= " AND linh_vuc_thong_bao IN (" . $filter['field'] . ")";
                            }
                            $where .= ' AND is_new_msc=1';
                            $sql = 'SELECT id from ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_row WHERE ' . $where;
                            $result = $db->query($sql);
                            while ($bidding = $result->fetch()) {
                                $num_row++;
                                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id(userid, filter_id, bid_id, addtime, send_status) VALUES (:userid, :filter_id, :bid_id, :addtime, 0)');
                                $time = NV_CURRENTTIME;
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':bid_id', $bidding['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                            }
                        } else if ($filter['vip_use2'] == 1) { // DMDA
                            $where .= ' AND id>' . $last_row_id['begin_cbdmda'] . ' AND id<=' . $last_row_id['to_cbdmda'];
                            if ($filter['time_find'] > 0) {
                                $time_find = $time_start_thisday - 86400 * $filter['time_find'];
                                $where .= ' AND time_post>=' . $time_find;
                                $where .= ' AND is_msc_new<2';
                            }
                            $sql = 'SELECT id from ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_proposal WHERE ' . $where;
                            $result = $db->query($sql);
                            while ($cbdmda = $result->fetch()) {
                                $num_row++;
                                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id(userid, filter_id, cbdmda_id, addtime, send_status) VALUES (:userid, :filter_id, :cbdmda_id, :addtime, 0)');
                                $time = NV_CURRENTTIME;
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':cbdmda_id', $cbdmda['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                            }
                        } else if ($filter['vip_use2'] == 4) { // KH
                            $where .= ' AND id>' . $last_row_id['from_plan'] . ' AND id<=' . $last_row_id['to_plan'];
                            if ($filter['time_find'] > 0) {
                                $time_find = $time_start_thisday - 86400 * $filter['time_find'];
                                $where .= ' AND addtime>=' . $time_find;
                            }
                            if ($filter['money_from'] > 0) {
                                $where .= " AND total_cost_num >= " . $filter['invest_from'];
                            }
                            if ($filter['money_to'] > 0) {
                                $where .= " AND total_cost_num <= " . $filter['invest_to'];
                            }
                            $sql = 'SELECT id from ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_plans_project WHERE ' . $where;
                            $result = $db->query($sql);
                            while ($plans_project = $result->fetch()) {
                                $num_row++;
                                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id(userid, filter_id, plan_id, addtime, send_status) VALUES (:userid, :filter_id, :plan_id, :addtime, 0)');
                                $time = NV_CURRENTTIME;
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':plan_id', $plans_project['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                            }
                        } else if ($filter['vip_use2'] == 5) { // KQ
                            $where .= ' AND id>' . $last_row_id['begin_kqlcndt'] . ' AND id<=' . $last_row_id['to_kqlcndt'] . ' and is_msc_new = 1';
                            if ($filter['time_find'] > 0) {
                                $time_find = $time_start_thisday - 86400 * $filter['time_find'];
                                $where .= ' AND post_time>=' . $time_find;
                            }

                            $sql = 'SELECT id from ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result_project WHERE ' . $where;
                            $result = $db->query($sql);
                            while ($result_project = $result->fetch()) {
                                $num_row++;
                                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id(userid, filter_id, result_id, addtime, send_status) VALUES (:userid, :filter_id, :result_id, :addtime, 0)');
                                $time = NV_CURRENTTIME;
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':result_id', $result_project['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                            }
                        } else if ($filter['vip_use2'] == 3) { // TBMST
                            $where .= ' AND id>' . $last_row_id['from_pq'] . ' AND id<=' . $last_row_id['to_pq'];
                            if ($filter['time_find'] > 0) {
                                $time_find = $time_start_thisday - 86400 * $filter['time_find'];
                                $where .= ' AND ngay_dang_tai>=' . $time_find;
                            }
                            $where .= ' AND is_msc_new=1';
                            $sql = 'SELECT id from ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_investor_prequalification WHERE ' . $where;
                            $result = $db->query($sql);
                            while ($result_tbmst = $result->fetch()) {
                                $num_row++;
                                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id(userid, filter_id, pq_id, addtime, send_status) VALUES (:userid, :filter_id, :pq_id, :addtime, 0)');
                                $time = NV_CURRENTTIME;
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':pq_id', $result_tbmst['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                            }
                        } else if ($filter['vip_use2'] == 6) { // KQST
                            $where .= ' AND id>' . $last_row_id['begin_kqst'] . ' AND id<=' . $last_row_id['to_kqst'];
                            if ($filter['time_find'] > 0) {
                                $time_find = $time_start_thisday - 86400 * $filter['time_find'];
                                $where .= ' AND post_time>=' . $time_find;
                            }
                            $sql = 'SELECT id from ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqst_project WHERE ' . $where;
                            $result = $db->query($sql);
                            while ($result_kqst = $result->fetch()) {
                                $num_row++;
                                $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_id(userid, filter_id, kqst_id, addtime, send_status) VALUES (:userid, :filter_id, :kqst_id, :addtime, 0)');
                                $time = NV_CURRENTTIME;
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':kqst_id', $result_kqst['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                            }
                        }

                        // ghi log bộ lọc
                        if ($num_row > 0) {
                            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastresult = ' . NV_CURRENTTIME . ', stat_totalresult = stat_totalresult + ' . $num_row . ' WHERE id=' . $filter['id']);
                        }
                    }
                }
            }
            // ghi lại logs cho quá trình tìm kiếm tin cho mỗi vip
            $time = NV_CURRENTTIME;
            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_logs SET to_time = :to_time, userid = :userid WHERE id =' . $logs_id);
            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
            $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
            $exc = $stmt->execute();
        }
        $db->commit();
    } catch (PDOException $e) {
        print_r($e);
        file_put_contents($filters_mail_log, "Lỗi INSERT:\n" . print_r($e, true) . "\n", FILE_APPEND);
        notify_to_slack($filters_mail_log, "filters_mail_project.php Lỗi INSERT:\n" . print_r($e, true) . "\n");
        $db->rollBack();
        die(); // Remove this line after checks finished
    }

    // Sau khi chạy xong một vòng 50 VIP thì thông báo chạy xong và thời gian chạy cho một vòng
    $time = time();
    $this_time = $time - NV_CURRENTTIME;
    $total_time = intval($run_time + $this_time);
    try {
        if ($number_user_scan < $number_user_scan_all) {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ', status=1 where id =' . $logs_id);
        } else {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_project_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ' where id =' . $logs_id);
        }
        if (sizeof($arr_id_vip)) {
            $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET filters_mail = ' . NV_CURRENTTIME . ' WHERE user_id IN (' . implode(',', $arr_id_vip) . ') AND vip=4 AND prefix_lang = ' . $prefix_lang . '');
        }
    } catch (PDOException $e) {
        file_put_contents($filters_mail_log, "Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP :\n" . print_r($e, true) . "\n", FILE_APPEND);
        notify_to_slack($filters_mail_log, "filters_mail_project.php Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP :\n" . print_r($e, true) . "\n");
        die();
    }
    $note = 'Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
    file_put_contents($filters_mail_log, $note . "\n\n", FILE_APPEND);
    die($note . "\n");
}
