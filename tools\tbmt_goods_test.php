<?php

/**
 * @Project DauThau
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2021 VINADES.,JSC. All rights reserved
 * @License commercial
 * @Createdate 26/03/2021, 10:36
 */
define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';
// Tạo content tìm kiếm hàng hóa cho TBMT
$id = 0;
$separator = ' あ ';

try {
    do {
        $run = 0;
        $array_id = $params = [];
        $result = $db->query('SELECT `id_goi_thau`, count(*) as nbc FROM `nv4_bidding_goods` GROUP by id_goi_thau HAVING nbc > 1000 ORDER BY nbc DESC LIMIT 50');
        while ($row = $result->fetch()) {
            $run++;
            $row['id'] = $row['id_goi_thau'];
            $array_id[$row['id']] = [
                'id' => $row['id'],
                'content_goods' => ''
            ];
            $id = $row['id'];
        }
        $result->closeCursor();
        if (empty($array_id)) {
            exit("\nDONE\n");
        }

        $result = $db->query('SELECT id_goi_thau, GROUP_CONCAT(DISTINCT name SEPARATOR "' . $separator . '") AS content_goods FROM nv4_bidding_goods WHERE id_goi_thau IN (' . implode(',', array_keys($array_id)) . ') GROUP BY id_goi_thau');
        while ($row = $result->fetch()) {
            $array_id[$row['id_goi_thau']]['content_goods'] = $row['content_goods'];
        }
        foreach ($array_id as $k => $v) {
            $params['body'][] = [
                'update' => [
                    '_index' => NV_LANG_ELASTIC . 'dauthau_bidding',
                    '_id' => $k
                ]
            ];
            $params['body'][] = [
                'doc' => [
                    'content_goods' => $v['content_goods']
                ]
            ];
        }
        $result->closeCursor();

        if (!empty($params['body'])) {
            $hosts = array(
                $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
            );

            $client = Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();
            $responses = $client->bulk($params);
            if (!empty($responses['items'])) {
                foreach ($responses['items'] as $value) {
                    print_r("Xử lý id: \033[33m" . $value['update']['_id']);
                    if ($value['update']['status'] != 200) {
                        print_r("\033[31m ERROR\033[0m\n");
                        file_put_contents(NV_ROOTDIR . '/tools/tbmt_goods_content_error.log', NV_LANG_DATA . ' ' . $value['update']['_id'] . PHP_EOL, FILE_APPEND);
                    } else {
                        $db->exec('UPDATE ' . NV_PREFIXLANG .'_bidding_row SET content_goods = ' . $db->quote($array_id[$value['update']['_id']]['content_goods']) . ' WHERE id = ' . $value['update']['_id']);
                        print_r("\033[32m OK\033[0m\n");
                    }
                }
            }
        }
        die('Thực hiện xong');
    } while ($run > 0);
} catch (Exception $e) {
    print_r($e);
    exit();
}
