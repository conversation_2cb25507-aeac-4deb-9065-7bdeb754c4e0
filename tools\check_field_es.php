<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$hosts = array(
    $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
);

$client = Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
    ->setHosts($hosts)
    ->setRetries(0)
    ->build();

$array_query_elastic = array();
$array_query_elastic['size'] = 5000;
$array_query_elastic['sort'] = [
    [
        "id" => [
            "order" => "asc"
        ]
    ]
];
$array_query_elastic['query']['bool']['must_not'] = [
    'exists' => [
        'field' => 'alias'
    ]
];
$array_query_elastic['_source'] = [
    'id',
    'code'
];

$params_ = array();
$params_['index'] = NV_LANG_ELASTIC . 'dauthau_result';
$params_['body'] = $array_query_elastic;
$response = $client->search($params_);

$array_id = [];
$num_row = $response['hits']['total']['value'];

echo "num_row = " . $num_row . "\n";

foreach ($response['hits']['hits'] as $value) {
    if (!empty($value['_source'])) {
        $bidding = $value['_source'];
        $array_id[] = $bidding['id'];
    }
}

if (!empty($array_id)) {
    $table = (NV_LANG_DATA == 'vi') ? 'nv4_vi_bidding_result' : 'nv4_en_bidding_result';
    $db->query("UPDATE " . $table . " SET `elasticsearch`=9 WHERE id IN (" . implode(',', $array_id) . ")");
    die("Cần thực hiện tiếp");
} else {
    die('đã thực hiện xong');
}
