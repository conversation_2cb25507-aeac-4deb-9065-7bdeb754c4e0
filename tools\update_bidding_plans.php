<?php

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', pathinfo(str_replace(DIRECTORY_SEPARATOR, '/', __file__), PATHINFO_DIRNAME));
define('NV_SYSTEM', true);
require NV_ROOTDIR . '/mainfile.php';

define('NV_PREFIXLANG', 'nv4_vi');
die('Tool nay ko chay, Chi khi nao kiem tra thấy có tin bị trùng thì mới chạy để xóa dữ liệu trùng');
$crconfig = array();
$crconfig['dbhost'] = '127.0.0.1';
$crconfig['dbport'] = '';
$crconfig['dbname'] = 'dauthau_crawls';
$crconfig['dbuname'] = 'dauthau_crawls';
$crconfig['dbpass'] = '';
$crconfig['dbtype'] = 'mysql';
$crconfig['collation'] = 'utf8_unicode_ci';
$crconfig['charset'] = 'utf8';
$crconfig['persistent'] = false;
$crconfig['prefix'] = 'nv4';

$_time_zone_db = preg_replace('/^([\+|\-]{1}\d{2})(\d{2})$/', '$1:$2', date('O'));
$driver_options = array(
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::ATTR_PERSISTENT => $crconfig['persistent'],
    PDO::ATTR_CASE => PDO::CASE_LOWER,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
);

$dsn = $crconfig['dbtype'] . ':dbname=' . $crconfig['dbname'] . ';host=' . $crconfig['dbhost'] . ';charset=' . $crconfig['charset'];
if (!empty($crconfig['dbport'])) {
    $dsn .= ';port=' . $crconfig['dbport'];
}
$driver_options[PDO::ATTR_ERRMODE] = PDO::ERRMODE_EXCEPTION;
try {
    $dbcr = new PDO($dsn, $crconfig['dbuname'], $crconfig['dbpass'], $driver_options);
    $dbcr->exec("SET SESSION time_zone='" . $_time_zone_db . "'");
} catch (PDOException $e) {
    echo 'PDOException CR';
    print_r($e);
    die();
}
$elastic_config = [];
$elastic_config['host'] = '************';
$elastic_config['port'] = '9200';

$waitTimeoutInSeconds = 2;
if ($fp = fsockopen($elastic_config['host'], $elastic_config['port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
    die();
}
fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $elastic_config['host'] . ':' . $elastic_config['port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setHosts($hosts)
        ->setRetries(0)
        ->build();

    //xóa các file trùng
    try {
        $a = 0;
        $_sql = "SELECT `code`, count(*) as sum FROM `nv4_vi_bidding_plans` GROUP BY `code` HAVING sum > 1";
        $_result = $db->query($_sql);
        $db->beginTransaction();
        $dbcr->beginTransaction();
        while ($tmp = $_result->fetch()) {
            $a++;
            $_plans = $db->query("SELECT id, code, title, investor, addtime, total_invest_number, plan_id_msc FROM nv4_vi_bidding_plans WHERE code = '" . $tmp['code'] . "' ORDER BY id DESC");
            $array_plans_delete = $array_plans = array();
            while ($_row = $_plans->fetch()) {
                $array_plans_delete[$_row['id']] = $_row;
                $array_plans[$_row['id']] = $_row;
            }

            foreach ($array_plans_delete as $plans_id => $plans) {
                if ($plans['total_invest_number'] > 0) {
                    unset($array_plans_delete[$plans_id]);
                    break;
                }
            }
            // tất cả total_invest_number = 0 => lấy giá trị đầu
            $plans_recrawl = 0;
            if ($tmp['sum'] == sizeof($array_plans_delete)) {
                list ($k) = array_keys($array_plans_delete);
                $r = array(
                    $k => $array_plans_delete[$k]
                );
                $plans_id = $k;
                unset($array_plans_delete[$plans_id]);
                $plans_recrawl = $plans_id;
            }

            if (!empty($array_plans_delete)) {
                // xóa giá trị trùng
                $db->exec("DELETE FROM `nv4_vi_bidding_plans` WHERE `id` IN ( " . implode(',', array_keys($array_plans_delete)) . ")");
                $db->exec("DELETE FROM `nv4_vi_bidding_plans_contract` WHERE `id_plan` IN ( " . implode(',', array_keys($array_plans_delete)) . ")");
                $db->exec("DELETE FROM `nv4_vi_bidding_follow_plans` WHERE `plans_id` IN ( " . implode(',', array_keys($array_plans_delete)) . ")");

                // xóa trên Elastic
                foreach ($array_plans_delete as $_row) {
                    $params = array();
                    $params['index'] = 'dauthau_plans';
                    $params['type'] = 'nv4_vi_bidding_plans';
                    $params['id'] = $_row['id'];
                    $response = $client->delete($params)->asArray();
                    if ($response['deleted'] > 0) {
                        echo "xóa trên ES id: " . $_row['id'];
                    } else {
                        echo "Lỗi xóa ES id: " . $_row['id'];
                    }
                }
            }
            if ($plans_recrawl > 0) {
                // bóc lại kế hoạch được lấy
                $plan_id_msc = $array_plans[$plans_recrawl]['plan_id_msc'];
                $check = $dbcr->query('SELECT * FROM `nv4_vi_bidding_plans_url` WHERE `plan_id_msc` = ' . $plan_id_msc)->fetch();
                $array_plans[$plans_recrawl]['code'] = preg_replace('/[^0-9-]/', '', $array_plans[$plans_recrawl]['code']);
                if (intval(substr($array_plans[$plans_recrawl]['code'], 0, 6)) < 201803) {
                    $type_url = 1;
                } else {
                    $type_url = 2;
                }
                if (!empty($check)) {
                    $exc = $dbcr->exec("UPDATE nv4_vi_bidding_plans_url SET run_url = 0, type_url =" . $type_url . " WHERE plan_id_msc = " . $plan_id_msc);
                } else {
                    $exc = $dbcr->exec("INSERT INTO `nv4_vi_bidding_plans_url` (`code`, `title`, `owner`, tongmucdautu, `addtime`, `plan_id_msc`, type_url ) VALUES (" . $db->quote($array_plans[$plans_recrawl]['code']) . "," . $db->quote($array_plans[$plans_recrawl]['title']) . "," . $db->quote($array_plans[$plans_recrawl]['investor']) . ",'', " . $array_plans[$plans_recrawl]['addtime'] . "," . $db->quote($plan_id_msc) . ", " . $type_url . ")");
                }

                if ($exc) {
                    echo 'update KHLCNT:' . $array_plans[$plans_recrawl]['code'];
                    echo "\n<br>";
                }
            } else {
            }
        }
        $db->commit();
        $dbcr->commit();
    } catch (PDOException $e) {
        $db->rollBack();
        $dbcr->rollBack();
        print_r($e);
        die($e->getMessage());
    }
}
echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '');

