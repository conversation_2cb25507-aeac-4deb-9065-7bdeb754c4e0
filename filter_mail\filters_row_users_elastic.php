#!/usr/bin/env php
<?php

/**
 *
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

// Kiểm tra sử dụng elastic search hay mysql
if (empty($config_bidding['elas_use'])) {
    die('Hệ thống chưa bật tính năng tìm kiếm theo Elastic Search');
}

// Kiểm tra tiến trình đang chạy, quá 30 phút thì xóa log chạy lại không thì chờ
$fileCheck = NV_ROOTDIR . '/filter_mail/filters_row_users_elastic_' . $prefix_lang . '.txt';
if (file_exists($fileCheck)) {
    // File tồn tại kiểm tra thời gian
    $filemtime = filemtime($fileCheck);
    if ($filemtime < (NV_CURRENTTIME - (30 * 60))) {
        // Quá 30 phút coi như chạy xong, xóa chạy lại
        unlink($fileCheck);
    } else {
        die("Tiến trình đang chạy, không chạy đè\n");
    }
}
// Tạo file check để biết tiến trình đang chạy
file_put_contents($fileCheck, 'Run...', LOCK_EX);

$waitTimeoutInSeconds = 2;
if ($fp = fsockopen($config_bidding['elas_host'], $config_bidding['elas_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
    fclose($fp);
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
    die();
}

if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
} else {
    // Dừng chạy khi ES die
    unlink($fileCheck);
    die();
}
$filters_row_user_log = NV_ROOTDIR . '/data/filters_row_user_log_' . $prefix_lang . '_' . date('Ymd') . '.txt'; // log lỗi quá trình chạy

/*
 * Vào ngày cuối cùng của tháng, kiểm tra nếu không có vòng lặp nào chạy
 * thì bắt đầu khởi chạy vòng lặp mới
 */
$current_month = date('n', NV_CURRENTTIME);
$current_year = date('Y', NV_CURRENTTIME);
$next_month = $current_month + 1;
if ($next_month > 12) {
    $next_month = 1;
    $current_year++;
}
$current_day = date('d_m_Y', NV_CURRENTTIME);
$last_monthday = date('d_m_Y', (mktime(0, 0, 0, $next_month, 1, $current_year) - 1));
$month_current = date('m_Y');

if ($month_current != $config_bidding['stat_ufilter_month'] and $current_day == $last_monthday and $config_bidding['stat_ufilter_current'] < 0) {
    $config_bidding['stat_ufilter_month'] = $month_current;
    $config_bidding['stat_ufilter_current'] = 0;
    $db->exec('UPDATE nv4_config SET config_value = ' . $db->quote($month_current) . ' WHERE module=' . $db->quote('bidding') . ' AND config_name = ' . $db->quote('stat_ufilter_month') . ' AND lang=' . $db->quote($site_lang));
    $db->exec('UPDATE nv4_config SET config_value = 0 WHERE module=' . $db->quote('bidding') . ' AND config_name = ' . $db->quote('stat_ufilter_current') . ' AND lang=' . $db->quote($site_lang));
}

$last_userid = $config_bidding['stat_ufilter_current'];

$time = time();
$start_time = $time;
$arr_id = array();
$arr_user = array();
// Khách đã đăng ký VIEWEB 20 ngày trở lên mới gửi, tránh tình trạng mới đăng ký xong gửi luôn
$since_time = NV_CURRENTTIME - (20 * 86400);

/*
 * Mỗi vòng lặp quét 400 khách VIPWEB
 */
$number_user_scan_all = 400;
if ($last_userid >= 0) {
    $sql = "SELECT tb1.* FROM nv4_users tb1 INNER JOIN " . BID_PREFIX_GLOBAL . "_customs tb2
    ON tb1.userid=tb2.user_id
    WHERE
        tb2.status=1 AND tb2.vip=99 AND tb1.active=1 AND tb1.userid>" . $last_userid . " AND
        from_time<" . $since_time . " AND tb2.prefix_lang = ' . $prefix_lang .'
    ORDER BY tb1.userid ASC LIMIT " . $number_user_scan_all;
    $query = $db->query($sql);

    while ($info = $query->fetch()) {
        $arr_user[$info['userid']] = $info;
        $arr_id[$info['userid']] = $info['userid'];
    }
} else {
    unlink($fileCheck);
    die('Hệ thống đã chạy xong, chờ tháng tiếp theo');
}

// Lấy ra ds các bộ lọc của users đc chọn
if (!empty($arr_id)) {
    $query_filter = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE userid IN (' . implode(',', $arr_id) . ') AND status=1 AND (vip_use=0 OR vip_use=1 OR vip_use=2) AND prefix_lang = ' . $prefix_lang . '');
    while ($result = $query_filter->fetch()) {
        $arr_user[$result['userid']]['filter'][$result['id']] = $result;
    }
}

// Lấy ra các tin trong ds tin mới mà thỏa mãn bộ lọc tương ứng với từng user
if (empty($arr_user)) {
    // Nếu không lấy được user nào thì coi như đã chạy xong, update stat_ufilter_current = -1
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $db->exec('UPDATE nv4_config SET config_value = \'-1\' WHERE module=' . $db->quote('bidding') . ' AND config_name = ' . $db->quote('stat_ufilter_current') . ' AND lang=' . $db->quote($site_lang));
    file_put_contents($filters_row_user_log, "Đã quét xong tất cả user. Tổng thời gian cho một lượt:" . $total_time . "\n", FILE_APPEND);
    unlink($fileCheck);
    die("Đã kết thúc quét trong tháng này\n");
} else {
    $db->beginTransaction();
    try {
        foreach ($arr_user as $userid => $user) {
            echo "Bắt đầu quét:" . $userid . "\n";
            file_put_contents($filters_row_user_log, "Bắt đầu quét:" . $userid . "\n", FILE_APPEND);
            // Lấy ra danh sách các tin mời thầu, KHLCNT, TBMST thỏa mãn điều kiện tìm kiếm từ các bộ lọc
            if (!empty($user['filter'])) {
                foreach ($user['filter'] as $filter) {
                    if (!empty($filter)) {
                        // Từ bộ lọc chọn ra điều kiện lọc where tương ứng
                        $search_elastic = array();
                        // tìm kiếm theo khoảng thời gian
                        $ufilter_month = explode('_', $config_bidding['stat_ufilter_month']);
                        $time_find = mktime(0, 0, 0, $ufilter_month[0], 01, $ufilter_month[1]);
                        if ($filter['vip_use'] == 1) { // bộ lọc TBMT
                            if ($filter['type'] == 0) {
                                $search_elastic['must'][]['range']['ngay_dang_tai'] = [
                                    "gte" => $time_find
                                ];
                            } else {
                                $search_elastic['must'][]['range']['den_ngay'] = [
                                    "gte" => $time_find
                                ];
                            }
                            if ($filter['cat'] > 0) {
                                $search_elastic['must'][] = [
                                    'match' => [
                                        'type_bid' => [
                                            'query' => $filter['cat'] == 1 ? "1" : "0"
                                        ]
                                    ]
                                ];
                            }
                            if ($filter['type_org'] == 2) {
                                $search_elastic['must'][] = [
                                    'match' => [
                                        'type_org' => [
                                            'query' => "1"
                                        ]
                                    ]
                                ];
                            }
                            if (!empty($filter['phanmuc'])) {
                                $filter['phanmuc'] = explode(',', $filter['phanmuc']);
                                $_phanmucterm = '~[0-9](' . implode('|', $filter['phanmuc']) . ')~[0-9]';
                                $search_elastic['must'][] = [
                                    'regexp' => [
                                        'phanmuc' => [
                                            'value' => $_phanmucterm
                                        ]
                                    ]
                                ];
                            }
                            if (!empty($filter['vsic'])) {
                                $filter['vsic'] = explode(',', $filter['vsic']);
                                foreach ($filter['vsic'] as $v) {
                                    if ($v != 0) {
                                        $vsic_regex[] = $v;
                                    }
                                }
                                if (!empty($vsic_regex)) {
                                    $vsic_regex = '.*(' . implode('|', $vsic_regex) . ').*';
                                    $vsic_query['bool']['should'][] = [
                                        'regexp' => [
                                            'vsic.keyword' => [
                                                'value' => $vsic_regex
                                            ]
                                        ]
                                    ];
                                }
                                if (in_array(0, $filter['vsic'])) {
                                    $vsic_query['bool']['should'][] = [
                                        'terms' => [
                                            'vsic.keyword' => ['', '0']
                                        ]
                                    ];
                                }
                                $search_elastic['must'][] = $vsic_query;
                            }
                            // tim kiếm theo phương thức
                            if (!empty($filter['phuongthuc'])) {
                                $search_field = array();
                                $filter['phuongthuc'] = explode(',', $filter['phuongthuc']);
                                foreach ($filter['phuongthuc'] as $key) {
                                    $search_field[] = [
                                        'term' => [
                                            'phuong_thuc_num' => $key
                                        ]
                                    ];
                                }
                                $search_elastic['must'][]['bool']['should'] = $search_field;
                            }

                            if (!empty($filter['type_choose_id'])) {
                                $search_elastic['must'][] = [
                                    'match' => [
                                        'type_choose_id' => [
                                            'query' => $filter['type_choose_id']
                                        ]
                                    ]
                                ];
                            }

                            // Tìm kiếm theo tỉnh thành
                            if (!empty($filter['idprovince'])) {
                                $filter['idprovince'] = explode(',', $filter['idprovince']);
                                $search_idprovince = array();
                                foreach ($filter['idprovince'] as $key_idp) {
                                    $search_idprovince[] = [
                                        'regexp' => [
                                            'province_id' => [
                                                'value' => '~[0-9]' . $key_idp . '~[0-9]',
                                                'flags' => 'ALL'
                                            ]
                                        ]
                                    ];
                                }
                                $search_elastic['must'][]['bool']['should'] = $search_idprovince;
                            }

                            // tìm theo tiền bảo đảm
                            if ($filter['money_from'] > 0 and $filter['money_to']) {
                                $search_elastic['must'][]['range']['money_bid'] = [
                                    "gte" => $filter['money_from'],
                                    "lte" => $filter['money_to']
                                ];
                            } else {
                                if ($filter['money_from'] > 0) {
                                    $search_elastic['must'][]['range']['money_bid'] = [
                                        "gte" => $filter['money_from']
                                    ];
                                }
                                if ($filter['money_to'] > 0) {
                                    $search_elastic['must'][]['range']['money_bid'] = [
                                        "lte" => $filter['money_to']
                                    ];
                                }
                            }

                            // tìm theo giá mời thầu
                            if ($filter['price_from'] > 0 and $filter['price_to']) {
                                $search_elastic['must'][]['range']['price'] = [
                                    "gte" => $filter['price_from'],
                                    "lte" => $filter['price_to']
                                ];
                            } else {
                                if ($filter['price_from'] > 0) {
                                    $search_elastic['must'][]['range']['price'] = [
                                        "gte" => $filter['price_from']
                                    ];
                                }
                                if ($filter['price_to'] > 0) {
                                    $search_elastic['must'][]['range']['price'] = [
                                        "lte" => $filter['price_to']
                                    ];
                                }
                            }

                            // tim kiếm theo lĩnh vực thông báo
                            if ($filter['vip_use'] == 5) {
                                if ($filter['vip5_open'] == 1) {
                                    // mở rộng: tìm theo is_vip5: được lọc theo từ khóa có liên quan đến vốn khác
                                    $search_elastic['must'][] = [
                                        'term' => [
                                            'is_vip5' => 1
                                        ]
                                    ];
                                } else {
                                    $search_elastic['must'][] = [
                                        'term' => [
                                            'pham_vi' => 1
                                        ]
                                    ];
                                    // phạm vi =1: ngoài lĩnh vực điều chỉnh của luật, vốn khác
                                }

                                if (!empty($filter['field'])) {
                                    $search_field = array();
                                    $filter['field'] = explode(',', $filter['field']);
                                    foreach ($filter['field'] as $key) {
                                        $search_field[] = [
                                            'term' => [
                                                'linh_vuc_thong_bao' => $key
                                            ]
                                        ];
                                    }
                                    $search_elastic['must'][]['bool']['should'] = $search_field;
                                }
                            } else {
                                if (!empty($filter['field'])) {
                                    $search_field = array();
                                    $filter['field'] = explode(',', $filter['field']);
                                    foreach ($filter['field'] as $key) {
                                        $search_field[] = [
                                            'term' => [
                                                'linh_vuc_thong_bao' => $key
                                            ]
                                        ];
                                    }
                                    $search_elastic['must'][]['bool']['should'] = $search_field;
                                }

                                // phạm vi !=1: trong lĩnh vực điều chỉnh của luật, gửi mail VIP1, 1QT,
                                $search_elastic['must_not'][] = [
                                    'term' => [
                                        'pham_vi' => 1
                                    ]
                                ];
                            }

                            if ($filter['search_type'] == 1) {
                                if ($filter['par_search']) {
                                    $search_fields = [
                                        'so_tbmt',
                                        'goi_thau'
                                    ];
                                } else {
                                    $search_fields = [
                                        'content_full'
                                    ];
                                }
                            } else {
                                if ($filter['par_search']) {
                                    $search_fields = [
                                        'so_tbmt',
                                        'goi_thau_search'
                                    ];
                                } else {
                                    $search_fields = [
                                        'content'
                                    ];
                                }
                            }
                            if ($filter['goods_search'] == 1) {
                                $search_fields[] = 'content_goods';
                            } elseif ($filter['goods_search'] == 2) {
                                $search_fields = [
                                    'content_goods'
                                ];
                            }
                        } else if ($filter['vip_use'] == 2) { // bộ lọc KHLCNT
                            $search_elastic['must'][]['range']['addtime'] = [
                                "gte" => $time_find
                            ];
                            // tìm theo tổng mức đầu tư
                            if ($filter['invest_from'] > 0) {
                                $search_elastic['must'][]['range']['total_invest_number'] = [
                                    "gte" => $filter['invest_from']
                                ];
                            }
                            if ($filter['invest_to'] > 0) {
                                $search_elastic['must'][]['range']['total_invest_number'] = [
                                    "lte" => $filter['invest_to']
                                ];
                            }
                            // tìm theo giá gói thầu
                            if ($filter['price_plan_from'] > 0) {
                                $search_elastic['must'][]['range']['price_min'] = [
                                    "gte" => $filter['price_plan_from']
                                ];
                                $search_elastic['must'][]['range']['price_max'] = [
                                    "gte" => $filter['price_plan_from']
                                ];
                            }
                            if ($filter['price_plan_to'] > 0) {
                                $search_elastic['must'][]['range']['price_min'] = [
                                    "gte" => $filter['price_plan_from'],
                                    "lte" => $filter['price_plan_to']
                                ];
                                $search_elastic['must'][]['range']['price_max'] = [
                                    "gte" => $filter['price_plan_from'],
                                    "lte" => $filter['price_plan_to']
                                ];
                            }
                            if ($filter['search_type'] == 1) {
                                if ($filter['par_search']) {
                                    $search_fields = [
                                        'code',
                                        'title'
                                    ];
                                } else {
                                    $search_fields = [
                                        'content_full'
                                    ];
                                }
                            } else {
                                if ($filter['par_search']) {
                                    $search_fields = [
                                        'code',
                                        'title_search'
                                    ];
                                } else {
                                    $search_fields = [
                                        'content'
                                    ];
                                }
                            }
                        }

                        // từ khóa loại trừ
                        if (!empty($filter['without_key'])) {
                            $arr_key = explode(',', $filter['without_key']);
                            foreach ($search_fields as $f) {
                                foreach ($arr_key as $key) {
                                    if (!preg_match('/[0-9]/', $key) && ($f == 'so_tbmt' || $f == 'code')) {
                                        continue;
                                    }
                                    if (empty($key)) {
                                        continue;
                                    }
                                    if ($filter['search_type'] == 1) {
                                        $key = trim($key);
                                        $search_elastic['must_not'][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    } else {
                                        $f != 'content_goods' && $key = str_replace('-', ' ', change_alias($key));
                                        $f == 'content_goods' && $key = strtolower($key);
                                        $key = trim($key);
                                        $search_elastic['must_not'][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    }
                                }
                            }
                        }

                        if (!empty($filter['key_search2'])) {
                            // Tìm theo từ khóa cùng
                            $arr_key = explode(',', $filter['key_search2']);
                            $search_bosung = [];
                            if (sizeof($search_fields) > 1) {
                                // Nếu số lượng trường tìm kiếm > 1 thì sẽ dùng bool must từng từ khóa
                                // mỗi từ khóa thì lại should trong từng trường                                $
                                foreach ($arr_key as $key) {
                                    if (empty($key)) {
                                        continue;
                                    }
                                    $search_bosung_should = [];
                                    foreach ($search_fields as $f) {
                                        if (!preg_match('/[0-9]/', $key) && ($f == 'so_tbmt' || $f == 'code')) {
                                            continue;
                                        }
                                        if ($filter['search_type'] == 1) {
                                            $key = trim($key);
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                            $search_bosung[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        } else {
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => ($f != 'content_goods' ? trim(str_replace('-', ' ', change_alias($key))) : trim(strtolower($key)))
                                                ]
                                            ];
                                            $search_bosung[] = [
                                                "match_phrase" => [
                                                    $f => ($f != 'content_goods' ? trim(str_replace('-', ' ', change_alias($key))) : trim(strtolower($key)))
                                                ]
                                            ];
                                        }
                                    }
                                    if (empty($filter['search_one_key'])) {
                                        $search_elastic['must'][]['bool'] = [
                                            'should' => $search_bosung_should,
                                            'minimum_should_match' => 1,
                                        ];
                                    }
                                }
                                if (!empty($filter['search_one_key']) and !empty($search_bosung)) {
                                    //Một trong các từ khóa là điều kiện bắt buộc
                                    $search_elastic['must'][] = [
                                        "bool" => [
                                            "should" => $search_bosung,
                                            "minimum_should_match" => 1
                                        ]
                                    ];
                                }
                            } else {
                                foreach ($search_fields as $f) {
                                    foreach ($arr_key as $key) {
                                        if (empty($key)) {
                                            continue;
                                        }
                                        if (!preg_match('/[0-9]/', $key) && ($f == 'so_tbmt' || $f == 'code')) {
                                            continue;
                                        }
                                        if ($filter['search_type'] == 1) {
                                            $key = trim($key);
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        } else {
                                            $f != 'content_goods' && $key = str_replace('-', ' ', change_alias($key));
                                            $f == 'content_goods' && $key = strtolower($key);
                                            $key = trim($key);
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        }
                                    }
                                }
                                if (!empty($filter['search_one_key'])) {
                                    $bool_should = [
                                        'bool' => [
                                            'should' => $search_bosung_should,
                                            'minimum_should_match' => 1
                                        ]
                                    ];
                                    $search_elastic['must'][] = $bool_should;
                                } else {
                                    $search_elastic['must'] = array_merge($search_elastic['must'], $search_bosung_should);
                                }
                            }
                        }

                        if (!empty($filter['key_search'])) {
                            $arr_key = explode(',', $filter['key_search']);
                            if (!empty($filter['searchkind'])) {
                                $arr_key = array_map(function ($a) {
                                    return explode(' ', $a);
                                }, $arr_key);
                                $arr_key = new RecursiveIteratorIterator(new RecursiveArrayIterator($arr_key));
                            }
                            $keyword_type = $filter['searchkind'] <= 1 ? 'should' : 'must';
                            foreach ($search_fields as $f) {
                                foreach ($arr_key as $key) {
                                    if (!preg_match('/[0-9]/', $key) && ($f == 'so_tbmt' || $f == 'code')) {
                                        continue;
                                    }
                                    if (empty($key)) {
                                        continue;
                                    }
                                    if ($filter['search_type'] == 0 && $f != 'content_goods') {
                                        $key = str_replace('-', ' ', change_alias($key));
                                    } else if ($filter['search_type'] == 0 && $f == 'content_goods') {
                                        $key = strtolower($key);
                                    }
                                    $key = trim($key);
                                    if (preg_match('/^[0-9]+?$/', $key)) { // Nếu keyword chỉ là dãy số thì tìm wildcard
                                        $search_elastic[$keyword_type][] = [
                                            "wildcard" => [
                                                $f => [
                                                    "value" => '*' . $key . '*'
                                                ]
                                            ]
                                        ];
                                    } else {
                                        $search_elastic[$keyword_type][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    }
                                }
                            }
                        }

                        if (!empty($search_elastic['should'])) {
                            $search_elastic['minimum_should_match'] = '1';
                            $search_elastic['boost'] = '1.0';
                        }

                        $array_query_elastic = array();
                        if (!empty($search_elastic)) {
                            $array_query_elastic['query']['bool'] = $search_elastic;
                        }
                        $array_query_elastic['_source'] = array(
                            'id'
                        );
                        $array_query_elastic['size'] = 10;
                        if ($filter['vip_use'] == 1) {
                            $array_query_elastic['sort'] = [
                                [
                                    "ngay_dang_tai" => [
                                        "order" => "desc"
                                    ]
                                ]
                            ];
                            $params = array();
                            $params['index'] = NV_LANG_ELASTIC . 'dauthau_bidding';
                            // $params['type'] = 'nv4_vi_bidding_row';
                            $params['body'] = $array_query_elastic;
                        } else if ($filter['vip_use'] == 2) {
                            $array_query_elastic['sort'] = [
                                [
                                    "addtime" => [
                                        "order" => "desc"
                                    ]
                                ]
                            ];
                            $params = array();
                            $params['index'] = NV_LANG_ELASTIC . 'dauthau_plans';
                            // $params['type'] = 'nv4_vi_bidding_plans';
                            $params['body'] = $array_query_elastic;
                        }
                        $response = $client->search($params)->asArray();

                        $arr_data = array();
                        foreach ($response['hits']['hits'] as $value) {
                            if (!empty($value['_source'])) {
                                $bidding = $value['_source'];
                                $arr_data[$bidding['id']] = $bidding['id'];
                            }
                        }
                        if (!empty($arr_data)) {
                            $arr_data = implode(',', $arr_data);
                            // lưu bảng nv4_vi_bidding_filter_ubymonth
                            $stmt = $db->prepare('INSERT INTO ' . BID_PREFIX_GLOBAL . '_filter_ubymonth(
                            in_month, userid, filterid, vip_use, data, prefix_lang
                        ) VALUES (
                            :in_month, :userid, :filterid, :vip_use, :data, :prefix_lang
                        )');
                            $stmt->bindParam(':in_month', $config_bidding['stat_ufilter_month'], PDO::PARAM_STR);
                            $stmt->bindParam(':userid', $userid, PDO::PARAM_INT);
                            $stmt->bindParam(':filterid', $filter['id'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip_use', $filter['vip_use'], PDO::PARAM_INT);
                            $stmt->bindParam(':data', $arr_data, PDO::PARAM_STR);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_STR);
                            $exc = $stmt->execute();
                            if ($exc) {
                                break; // mỗi user chỉ cần có giá trị 1 bộ lọc thì break, k cần biết bộ lọc cho gói vip nào
                            }
                        }
                    }
                }
            }

            // chạy xong mỗi user thì cập nhật lại stat_ufilter_current
            $db->exec('UPDATE nv4_config SET config_value = ' . $userid . ' WHERE module=' . $db->quote('bidding') . ' AND config_name = ' . $db->quote('stat_ufilter_current') . ' AND lang=' . $db->quote($site_lang));
        }
        $db->commit();
    } catch (PDOException $e) {
        file_put_contents($filters_row_user_log, "Lỗi INSERT :\n" . print_r($e, true) . "\n", FILE_APPEND);
        $db->rollBack();
        notify_to_slack($filters_row_user_log, "filters_row_users_elastic.php Lỗi INSERT :\n" . print_r($e, true) . "\n");
        unlink($fileCheck);
        die();
    }

    // Sau khi chạy xong một vòng 100 user thì thông báo chạy xong
    $time = time();
    $this_time = $time - NV_CURRENTTIME;

    $note = 'Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
    file_put_contents($filters_row_user_log, $note . "\n", FILE_APPEND);
    unlink($fileCheck);
    die($note . "\n");
}
