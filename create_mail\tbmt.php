<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */

// Gửi mail VIP1
// - <PERSON><PERSON><PERSON> tra nếu cả VIP1 và PRO 1 đều còn hạn thì gửi theo VIP1
// - Nếu còn mỗi VIP 1 thì lấy những bộ lọc của PRO 1 gửi kèm VIP 1
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
require NV_ROOTDIR . '/functions/call_api.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$date_format = ($prefix_lang == 1) ? 'm/d/Y' : 'd/m/Y';
$datetime_format = ($prefix_lang == 1) ? 'm/d/Y H:i' : 'd/m/Y H:i';
$create_mail_file = NV_ROOTDIR . '/data/create_mail_tbmt_' . $prefix_lang . '_' . date('Ymd') . '.txt';

register_shutdown_function("fatal_handler");

function fatal_handler()
{
    global $create_mail_file;
    $error = error_get_last();
    if ($error !== NULL) {
        echo ('<pre><code>' . print_r($error, true) . '</code></pre>');
        file_put_contents($create_mail_file, '<pre><code>' . print_r($error, true) . '</code></pre>', FILE_APPEND);
    }
}

if (file_exists(NV_ROOTDIR . '/data/create_mail_tbmt_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "tbmt.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit("Chay trung nhau");
}

file_put_contents(NV_ROOTDIR . '/data/create_mail_tbmt_' . $prefix_lang . '.txt', NV_CURRENTTIME);
file_put_contents($create_mail_file, "======== Bắt đầu chạy: " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);

// Khởi tạo biến lưu thông tin thông báo
$arrInform = $list_info = [];
try {

    // Từ bảng tmp tổng hợp lại mail gửi đi cho từng VIP
    // Lấy danh sách các mail chưa gửi trong csdl để tiến hành gửi đi gom theo userid
    $vips = [];
    $list_vipid = [];

    if (file_exists(NV_ROOTDIR . '/data/create_mail_tbmt_' . $prefix_lang . '_log.txt')) {
        $log_customsid = file_get_contents(NV_ROOTDIR . '/data/create_mail_tbmt_' . $prefix_lang . '_log.txt');
        $log_customsid = explode('_', $log_customsid);
        $last_customsid = intval($log_customsid[0]);
        $last_customspoint = intval($log_customsid[1]);
    } else {
        $last_customsid = $last_customspoint = 0;
    }

    // các userid vip đến hạn gửi email
    $array_vip_userid = [];
    $query = $db->query('SELECT user_id FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE ((' . NV_CURRENTTIME . ' - last_email) >= time_send*3600) AND vip=1 AND status = 1 AND prefix_lang = ' . $prefix_lang . ' AND user_id > ' . $last_customsid . ' ORDER BY user_id ASC LIMIT 50');
    while ($row = $query->fetch()) {
        $array_vip_userid[$row['user_id']] = $row['user_id'];
        $last_customsid = $row['user_id'];
    }

    if (!empty($array_vip_userid)) {
        $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE userid IN (' . implode(',', $array_vip_userid) . ') AND send_status = 0 AND remind_hsdt = 0 LIMIT 20');
        while ($row = $query->fetch()) {
            $vips[$row['userid']] = array();
            $list_vipid[$row['userid']] = $row['userid'];
        }
    } else {
        $last_customsid = 0;
    }

    $list_vipid_all = [];
    // lấy các tài khoản mua bằng điểm, chu kỳ mail 6h
    $config_bidding['use_reward_points'] = 1;
    if ($config_bidding['use_reward_points']) {
        $arr_customs_points_email = [];
        $query = $db->query('SELECT userid FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE ((' . NV_CURRENTTIME . ' - lastmail) >= (6*3600)) AND vip=1 AND prefix_lang = ' . $prefix_lang . ' AND userid > ' . $last_customspoint . ' ORDER BY userid ASC LIMIT 50');
        while ($row = $query->fetch()) {
            $arr_customs_points_email[$row['userid']] = $row['userid'];
            $last_customspoint = $row['userid'];
        }

        $array_id_user = [];
        if (!empty($arr_customs_points_email)) {
            $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE userid IN (' . implode(',', $arr_customs_points_email) . ') AND send_status = 0 AND remind_hsdt = 0 LIMIT 10');
            while ($row = $query->fetch()) {
                $vips[$row['userid']] = [];
                $array_id_user[$row['userid']] = $row['userid'];
            }
        } else {
            $last_customspoint = 0;
        }

        ksort($vips);
        $list_vipid_all = array_merge($list_vipid, $array_id_user);
    } else {
        $list_vipid_all = $list_vipid;
    }
    file_put_contents(NV_ROOTDIR . '/data/create_mail_tbmt_' . $prefix_lang . '_log.txt', $last_customsid . '_' . $last_customspoint);

    $list_vipid_all = implode(',', $list_vipid_all);
    if (!empty($list_vipid_all)) {
        $arr_vip = $db->query('SELECT id as cusid, phone, user_id as userid, email, sub_email, time_send, split_email_filters, last_email, active_user, from_time FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id IN ( ' . $list_vipid_all . ') AND vip=1 AND status = 1 AND prefix_lang = ' . $prefix_lang . '');

        while ($vip_data = $arr_vip->fetch()) {
            // nếu vip này bật chế độ gửi email theo tài khoản
            if ($vip_data['active_user'] == 1) {
                $arr_subemail = [];
                $query_permission = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_permission WHERE vipid=' . $vip_data['cusid'] . ' AND totime >' . NV_CURRENTTIME . ' AND send_mail = 1 AND prefix_lang = ' . $prefix_lang . '');
                while ($permission = $query_permission->fetch()) {
                    $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid = ' . $permission['userid'] . '');
                    while ($user = $arr_user->fetch()) {
                        $arr_subemail[$user['email']] = $user['email'];
                    }
                }
                $vip_data['sub_email'] = implode(',', $arr_subemail);
            }

            $vips[$vip_data['userid']] = $vip_data;
            $vips[$vip_data['userid']]['filter'] = array();
            $vips[$vip_data['userid']]['notify'] = array();
            $vips[$vip_data['userid']]['bid'] = array();
            $vips[$vip_data['userid']]['result'] = array();
            $vips[$vip_data['userid']]['result_open'] = array();
            $vips[$vip_data['userid']]['is_vip'] = 1;
        }

        if ($config_bidding['use_reward_points'] and !empty($array_id_user)) {
            $arr_user = $db->query('SELECT t1.userid, t1.email, t2.id as cusid, t2.lastmail, t2.datecreate as from_time FROM nv4_users as t1 INNER JOIN ' . BID_PREFIX_GLOBAL . '_customs_points_email as t2 ON t1.userid= t2.userid WHERE t1.userid IN ( ' . implode(',', $array_id_user) . ') AND t2.prefix_lang = ' . $prefix_lang . '');
            while ($user_data = $arr_user->fetch()) {
                if (!isset($vips[$user_data['userid']]['email'])) {
                    $vips[$user_data['userid']] = $user_data;
                    $vips[$user_data['userid']]['filter'] = [];
                    $vips[$user_data['userid']]['notify'] = '';
                    $vips[$user_data['userid']]['plan'] = [];
                    $vips[$user_data['userid']]['time_send'] = 6;
                    $vips[$user_data['userid']]['sub_email'] = '';
                    $vips[$user_data['userid']]['is_vip'] = 0;
                }
            }
        }
    }

    // Nếu có dữ liệu danh sách user thì vào đây
    if (!empty($vips)) {
        // lấy username
        $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid IN ( ' . implode(',', array_keys($vips)) . ')');
        while ($user = $arr_user->fetch()) {
            $vips[$user['userid']]['username'] = $user['username'];
        }

        // Lấy ra tất cả tin đấu thầu trong danh sách gửi đi của tất cả các VIP
        // Lưu nội dung mail vào csdl
        foreach ($vips as $vip_id => $vip) {
            file_put_contents($create_mail_file, 'BEGIN: ' . $vip_id . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
            $arr_bid = array();
            $query = $db->query('SELECT a.id, a.price, a.goi_thau, a.so_tbmt, a.alias, a.ngay_dang_tai, a.ben_moi_thau, b.chu_dau_tu, b.ten_du_an, b.dia_diem FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row a INNER JOIN ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_detail b ON a.id=b.id WHERE a.id IN (SELECT bid_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE send_status = 0 AND remind_hsdt = 0 AND bid_id>0 AND userid=' . $vip_id . ')');
            while ($bid_row = $query->fetch()) {
                $arr_bid[$bid_row['id']] = $bid_row;
            }
            $query->closeCursor();

            // Gộp nhóm những mail chung chủ đề lại theo từng VIP
            $array_bid_id = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id WHERE send_status = 0 AND remind_hsdt = 0 AND userid=' . $vip_id . ' LIMIT 50');
            while ($bid_data = $query->fetch()) {
                $array_bid_id[$bid_data['id']] = $bid_data['id'];
                if ($bid_data['filter_id'] > 0) {
                    $vip['filter'][$bid_data['filter_id']]['list_bid'][] = $arr_bid[$bid_data['bid_id']];
                    $vip['bid'][] = $bid_data['bid_id'];
                }

                if ($bid_data['result_id'] > 0) {
                    $vip['result'][] = $bid_data['result_id'];
                    $vip['result_filter'][$bid_data['filter_id']][] = $bid_data['result_id'];
                }

                if ($bid_data['kqmt_id'] > 0) {
                    $vip['result_open'][] = $bid_data['kqmt_id'];
                    $vip['kqmt_filter'][$bid_data['filter_id']][] = $bid_data['kqmt_id'];
                }

                if ($bid_data['notify_vip'] != '') {
                    $vip['notify'][$bid_data['bid_id']] = $bid_data;
                    $vip['bid'][] = 0;
                }

                if (isset($bid_data['reasonid']) and $bid_data['reasonid'] > 0) {
                    $vip['reason'][$bid_data['bid_id']] = $bid_data;
                    $vip['bid'][] = 0;
                }
            }
            $query->closeCursor();
            // Cập nhật lại lần cuối cùng gửi mail cho các tài khoản VIP, Phần này cần chạy trước beg-----inTransaction để nếu có lỗi thì bỏ qua 1 tài khảo.
            if (sizeof($array_bid_id) < 50) {
                // Nếu 1 lần tổng hợp có > 50 thì không cập nhật ngay last_email để 1 phút sau sẽ chạy tiếp

                if ($vip['is_vip']) {
                    $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET last_email = ' . NV_CURRENTTIME . ' WHERE id =' . $vip['cusid'] . ' AND vip = 1');
                } else {
                    // do mua theo bộ lọc nên 1 tài khoản có nhiều row, dẫn tới cần ghi lastmail theo userid
                    $stmt = $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs_points_email SET lastmail=' . NV_CURRENTTIME . ' WHERE userid =' . $vip_id . '');
                }
            } else {
                file_put_contents($create_mail_file, "Con chay nua: " . sizeof($array_bid_id) . " / " . sizeof($arr_bid) . "\n", FILE_APPEND);
            }

            $db->beginTransaction();

            try {
                $data_insert = array();
                $data_insert['addtime'] = NV_CURRENTTIME;
                $data_insert['send_time'] = 0;
                $data_insert['status'] = 0;
                // Gửi mail thông báo mới thầu, kế hoạch chọn nhà thầu, thông báo sơ tuyển nhóm theo từng bộ lọc
                if (!empty($vip['filter']) and !empty($vip['email'])) {
                    $data_insert['title'] = sprintf($lang_module['mail_title_tbmt'], date($date_format));
                    $data_insert['type'] = 0;
                    $data_insert['vip'] = 1;

                    file_put_contents($create_mail_file, "Begin nv_theme_bidding_mail\n", FILE_APPEND);

                    if ($vip['split_email_filters'] == 1) {
                        // Phân chia email thông báo mời thầu riêng biệt cho từng bộ lọc
                        foreach ($vip['filter'] as $filterId => $filter) {
                            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filterId)->fetch();
                            $title = sprintf($lang_module['mail_title_tbmt_split'], date($date_format)) . sprintf($lang_module['with_filter'], $filter_info['title']);
                            $data_insert['content'] = nv_theme_bidding_mail([
                                $filterId => $filter
                            ], $vip_id, $arrInform);
                            $data_insert['content'] .= sprintf($lang_module['mail_footer_tbmt'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date($date_format, $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');

                            file_put_contents($create_mail_file, "END nv_theme_bidding_mail filter number $count\n", FILE_APPEND);
                            // Nội dung htm sẽ gửi cho từng khách
                            try {
                                file_put_contents($create_mail_file, "BIGIN INSERT INTO nv4_vi_bidding_mail\n", FILE_APPEND);
                                $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                                $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
                                $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                                $stmt->bindParam(':title', $title, PDO::PARAM_STR);
                                $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                                $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                                $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                                $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                                $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                                $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                                $_mailid = $db->lastInsertId();
                                file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            } catch (Exception $e) {
                                print_r('Lỗi thêm mail tin mới vào csdl');
                                file_put_contents($create_mail_file, "ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                                notify_to_slack($create_mail_file, "tbmt.php ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                                trigger_error($e->getMessage());
                                echo ($e->getMessage()); // Remove this line after checks finished
                                break;
                            }
                        }
                    } else {
                        $data_insert['content'] = nv_theme_bidding_mail($vip['filter'], $vip_id, $arrInform);
                        $data_insert['content'] .= sprintf($lang_module['mail_footer_tbmt'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date($date_format, $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');
                        file_put_contents($create_mail_file, "END nv_theme_bidding_mail\n", FILE_APPEND);
                        // Nội dung htm sẽ gửi cho từng khách
                        try {
                            file_put_contents($create_mail_file, "BEGIN INSERT INTO nv4_vi_bidding_mail\n", FILE_APPEND);
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
                            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();
                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (Exception $e) {
                            print_r('Lỗi thêm mail tin mới vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt.php ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo ($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }
                }

                // Gửi mail kết quả đấu thầu ra riêng một mail cho các VIP
                if (!empty($vip['result'])) {
                    $data_insert['title'] = sprintf($lang_module['mail_title_kqlcnt'], date($date_format));

                    $data_insert['type'] = 0;
                    $data_insert['vip'] = 1;

                    if ($vip['split_email_filters'] == 1) {
                        // Phân chia email kết quả đấu thầu riêng biệt cho từng bộ lọc
                        foreach ($vip['result_filter'] as $filterId => $result) {
                            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filterId)->fetch();
                            $title = sprintf($lang_module['mail_title_kqlcnt_split'], date($date_format)) . (!empty($filterId) ? sprintf($lang_module['with_filter'], $filter_info['title']) : '');
                            $data_insert['content'] = nv_theme_result_mail($result, $vip_id, $arrInform, $vip['username']);

                            // Nội dung htm sẽ gửi cho từng khách.
                            try {
                                $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                                $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($vip['sub_email']));
                                $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                                $stmt->bindParam(':title', $title, PDO::PARAM_STR);
                                $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                                $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                                $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                                $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                                $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                                $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                                $exc = $stmt->execute();

                                $_mailid = $db->lastInsertId();
                                file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            } catch (Exception $e) {
                                print_r('Lỗi thêm mail kết quả chọn thầu vào csdl');
                                file_put_contents($create_mail_file, "ERROR INSERT INTO mail: 148 " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                                notify_to_slack($create_mail_file, "tbmt.php ERROR INSERT INTO mail: 148 " . $vip_id . "; " . print_r($e, true) . "\n\n");
                                trigger_error($e->getMessage());
                                echo ($e->getMessage()); // Remove this line after checks finished
                                break;
                            }
                        }
                    } else {
                        $data_insert['content'] = nv_theme_result_mail($vip['result'], $vip_id, $arrInform, $vip['username']);

                        // Nội dung htm sẽ gửi cho từng khách.
                        try {
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($vip['sub_email']));
                            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (Exception $e) {
                            print_r('Lỗi thêm mail kết quả chọn thầu vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail: 148 " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt.php ERROR INSERT INTO mail: 148 " . $vip_id . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo ($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }
                }

                // Gửi mail kết quả mở thầu ra riêng một mail cho các VIP
                if (!empty($vip['result_open'])) {
                    $data_insert['title'] = sprintf($lang_module['mail_title_kqmt'], date($date_format));

                    $data_insert['type'] = 0;
                    $data_insert['vip'] = 1;

                    if ($vip['split_email_filters'] == 1) {
                        // Phân chia email kết quả mở thầu riêng biệt cho từng bộ lọc
                        foreach ($vip['kqmt_filter'] as $filterId => $kqmt) {
                            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filterId)->fetch();
                            $title = sprintf($lang_module['mail_title_kqmt_split'], date($date_format)) . (!empty($filterId) ? sprintf($lang_module['with_filter'], $filter_info['title']) : '');

                            $data_insert['content'] = nv_theme_result_open_mail($kqmt, $vip_id, $arrInform, $vip['username']);

                            try {
                                $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                                $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($vip['sub_email']));
                                $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                                $stmt->bindParam(':title', $title, PDO::PARAM_STR);
                                $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                                $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                                $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                                $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                                $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                                $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                                $exc = $stmt->execute();

                                $_mailid = $db->lastInsertId();
                                file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            } catch (Exception $e) {
                                print_r('Lỗi thêm mail kết quả chọn thầu vào csdl');
                                file_put_contents($create_mail_file, "ERROR INSERT INTO mail: 148 " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                                notify_to_slack($create_mail_file, "tbmt.php ERROR INSERT INTO mail: 148 " . $vip_id . "; " . print_r($e, true) . "\n\n");
                                trigger_error($e->getMessage());
                                echo ($e->getMessage()); // Remove this line after checks finished
                                break;
                            }
                        }
                    } else {
                        $data_insert['content'] = nv_theme_result_open_mail($vip['result_open'], $vip_id, $arrInform, $vip['username']);

                        try {
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($vip['sub_email']));
                            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (Exception $e) {
                            print_r('Lỗi thêm mail kết quả chọn thầu vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail: 148 " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt.php ERROR INSERT INTO mail: 148 " . $vip_id . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo ($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }
                }

                if (!empty($vip['notify'])) {
                    $data_insert['title'] = sprintf($lang_module['mail_title_tbmt_update'], date($date_format));
                    $data_insert['type'] = 1;
                    $data_insert['vip'] = 1;

                    if ($vip['split_email_filters'] == 1) {
                        // Phân chia email thông báo thay đổi gói thầu riêng biệt cho từng bộ lọc
                        foreach ($vip['notify'] as $notify) {
                            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $notify['filter_id'])->fetch();
                            $title = $data_insert['title'] . (!empty($notify['filter_id']) ? sprintf($lang_module['with_filter'], $filter_info['title']) : '');
                            $data_insert['content'] = nv_theme_change_mail([$notify], $vip_id, $arrInform, $vip['username']);

                            try {
                                $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                                $stmt1->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt1->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                                $stmt1->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($vip['sub_email']));
                                $stmt1->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                                $stmt1->bindParam(':title', $title, PDO::PARAM_STR);
                                $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                                $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                                $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                                $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                                $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                                $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                                $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                                $exc1 = $stmt1->execute();

                                $_mailid = $db->lastInsertId();
                                file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            } catch (Exception $e) {
                                file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                                notify_to_slack($create_mail_file, "tbmt.php ERROR INSERT INTO mail 175: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                            }
                        }
                    } else {
                        $data_insert['content'] = nv_theme_change_mail($vip['notify'], $vip_id, $arrInform, $vip['username']);

                        try {
                            $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt1->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt1->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt1->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($vip['sub_email']));
                            $stmt1->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc1 = $stmt1->execute();

                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (Exception $e) {
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt.php ERROR INSERT INTO mail 175: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                        }
                    }
                }

                if (!empty($vip['reason'])) {
                    $data_insert['title'] = sprintf($lang_module['mail_title_tbmt_cancel'], date($date_format));
                    $data_insert['type'] = 1;
                    $data_insert['vip'] = 1;
                    $data_insert['content'] = nv_theme_reason_mail($vip['reason'], $vip_id, $arrInform, $vip['username']);

                    try {
                        $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                        $stmt1->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                        $stmt1->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                        $stmt1->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($vip['sub_email']));
                        $stmt1->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                        $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                        $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                        $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                        $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                        $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                        $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                        $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                        $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                        $exc1 = $stmt1->execute();

                        $_mailid = $db->lastInsertId();
                        file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                    } catch (Exception $e) {
                        file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                        notify_to_slack($create_mail_file, "tbmt.php ERROR INSERT INTO mail 175: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                    }
                }

                echo "vip_id = " . $vip_id . "\n";
                // Thực hiện lưu thông báo vào biến tạm
                foreach ($arrInform as $k => $v) {
                    foreach ($v as $v1) {
                        $list_info[] = $v1;
                    }
                }

                // Reset lại biến này về rỗng để tiến hành lưu mảng dữ liệu tiếp theo
                $arrInform = [];

                // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                if (!empty($array_bid_id) and !empty($_mailid)) {
                    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bid_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_bid_id) . ') AND send_status = 0 AND remind_hsdt = 0');
                }
                $db->commit();

                file_put_contents($create_mail_file, "number bid = " . sizeof($array_bid_id) . ": " . implode(',', $array_bid_id) . "\n", FILE_APPEND);
                file_put_contents($create_mail_file, "END: " . $vip_id . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
                // print_r($array_bid_id);
            } catch (Exception $e) {
                file_put_contents($create_mail_file, 'rollBack ' . $vip_id . "\n\n", FILE_APPEND);
                file_put_contents($create_mail_file, 'ERROR 390: ' . print_r($e, true) . "\n\n", FILE_APPEND);
                $db->rollBack();
                echo '<pre>';
                print_r($e);
            }
        }
    }

    unlink(NV_ROOTDIR . '/data/create_mail_tbmt_' . $prefix_lang . '.txt');
} catch (Exception $e) {
    file_put_contents($create_mail_file, 'ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "tbmt.php ERROR: " . print_r($e, true) . "\n\n");
}

// Thực hiện lưu thông báo
if (!empty($list_info)) {
    echo "Có tổng " . sizeof($list_info) . ' Thông báo Inform';
    file_put_contents(NV_ROOTDIR . '/data/inform/inform_tbmt' . uniqid('', true) . '.txt', json_encode($list_info));
}

echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";
file_put_contents($create_mail_file, "Chạy xong: " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);

function nv_theme_result_mail($array_result, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func, $prefix_lang, $date_format, $datetime_format;
    if (!empty($array_result)) {
        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);

        // Lấy thông tin các kêt quả chọn nhà thầu
        $list_result = array();
        $array_result = implode(',', $array_result);
        $array_codetbmt = '';
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result WHERE id IN (' . $array_result . ')');
        while ($row = $result->fetch()) {
            $list_result[$row['id']] = $row;
            $tbmt = explode('-', $row['code']);
            $array_codetbmt = $tbmt[0];
        }
        $array_result_ids = array_keys($list_result);
        if (!empty($array_result_ids)) {
            $result_query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result_business WHERE resultid IN (' . implode(',', $array_result_ids) . ')');
            while ($business_row = $result_query->fetch()) {
                $list_result[$business_row['resultid']]['business'][] = $business_row;
            }
        }

        // chỉ lấy đại diện 1 tin theo dõi
        $mail_footer_follow = '';
        if (!empty($array_codetbmt)) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
            if ($row = $result->fetch()) {
                $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date($date_format, $row['date_follow']), NV_SERVER_NAME, $username);
            }
        }

        if (!empty($list_result)) {
            foreach ($list_result as $data) {
                $data['title_a'] = nv_htmlspecialchars($data['title']);
                $data['post_time'] = $data['post_time'] > 0 ? nv_date(($prefix_lang == 1 ? 'H:i m/d/Y' : 'H:i d/m/Y'), $data['post_time']) : '';
                $data['finish_time'] = $data['finish_time'] > 0 ? nv_date($date_format, $data['finish_time']) : '';
                $luachon_t = getUrlByLanguage('kqlcnt');
                if ($site_lang != 'vi') {
                    $data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['result'] . '/' . $luachon_t . '/' . $data['alias'] . '-' . $data['id'] . '.html';
                } else {
                    $data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['result'] . '/' . $luachon_t . '/' . $data['alias'] . '-' . $data['id'] . '.html';
                }

                $arrMess = [
                    'vi' => sprintf(get_lang('vi', 'title_search_by_result'), $data['title']),
                    'en' => sprintf(get_lang('en', 'title_search_by_result'), $data['title'])
                ];

                $arrLink = [
                    'vi' => URL_RE . '?kq=' . $data['id'],
                    'en' => URL_RE . 'en/?kq=' . $data['id']
                ];

                // Thông báo Push Notification cho người dùng
                // insertInform($vip_id, $arrMess, $data['link_view']);
                $arrInform['nv_theme_result_mail'][] = [
                    'vip_id' => $vip_id,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];
                if ($data['tender_price'] > 0) {
                    $data['tender_price'] = number_format($data['tender_price'], 0, ',', '.') . ' VND';
                }
                if ($data['bid_price'] > 0 and is_numeric($data['bid_price'])) {
                    $data['bid_price'] = number_format($data['bid_price'], 0, ',', '.') . ' VND';
                }
                $xtpl->assign('DATA', $data);
                if (!empty($data['business'])) {
                    foreach ($data['business'] as $business) {
                        $business['bidwinningprice'] = number_format($business['bidwinningprice'], 0, '.', ',') . ' VND';
                        $xtpl->assign('BUSINESS', $business);
                        $xtpl->parse('main.result.loop.business_winning_price.business');
                    }
                    $xtpl->parse('main.result.loop.business_winning_price');
                }
                if (!empty($data['code'])) {
                    $xtpl->parse('main.result.loop.code');
                }
                if (!empty($data['investor'])) {
                    $xtpl->parse('main.result.loop.chu_dau_tu');
                }
                if (!empty($data['type_bid'])) {
                    $xtpl->parse('main.result.loop.type_bid');
                }
                if (!empty($data['cat'])) {
                    $xtpl->parse('main.result.loop.cat');
                }
                if (!empty($data['title'])) {
                    $xtpl->parse('main.result.loop.title');
                }
                if (!empty($data['bid_price'])) {
                    $xtpl->parse('main.result.loop.bid_price');
                }
                if (!empty($data['finish_time'])) {
                    $xtpl->parse('main.result.loop.finish_time');
                }
                if (!empty($data['post_time'])) {
                    $xtpl->parse('main.result.loop.post_time');
                }
                if (!empty($data['point'])) {
                    $xtpl->parse('main.result.loop.point');
                }
                if (!empty($data['evaluating_price'])) {
                    $xtpl->parse('main.result.loop.evaluating_price');
                }
                $xtpl->parse('main.result.loop');
            }
            $xtpl->parse('main.result');
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_result_open_mail($array_result, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func, $prefix_lang, $date_format, $datetime_format;
    if (!empty($array_result)) {
        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);

        // Lấy thông tin các kêt quả chọn nhà thầu
        $list_result = $list_empty_chu_dau_tu = $array_codetbmt = [];
        $array_result = implode("','", $array_result);
        $result = $db->query("SELECT * FROM " . NV_PREFIXLANG . "_" . BIDDING_MODULE . "_open WHERE so_tbmt IN ('" . $array_result . "')");
        while ($row = $result->fetch()) {
            $list_result[$row['so_tbmt']] = $row;
            $tbmt = explode('-', $row['so_tbmt']);
            $array_codetbmt = $tbmt[0];
            if (empty($row['chu_dau_tu'])) {
                $list_empty_chu_dau_tu[] = '"' . $row['so_tbmt'] . '"';
            }
        }

        // chỉ lấy đại diện 1 tin theo dõi
        $mail_footer_follow = '';
        if (!empty($array_codetbmt)) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
            if ($row = $result->fetch()) {
                $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date($date_format, $row['date_follow']), NV_SERVER_NAME, $username);
            }
        }

        // Lấy tên chủ đầu tư trường hợp emty trong nv4_bidding_solicitor
        if (!empty($list_empty_chu_dau_tu)) {
            $list_solicitor_id = [];
            $result = $db->query('SELECT t1.solicitor_id, t1.so_tbmt FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row as t1 INNER JOIN ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_detail as t2 ON t1.id=t2.id WHERE t1.so_tbmt IN (' . implode(',', $list_empty_chu_dau_tu) . ')');
            while ($row = $result->fetch()) {
                if (!empty($row['solicitor_id'])) {
                    $list_solicitor_id[(int) $row['solicitor_id']] = $row['so_tbmt'];
                }
            }
            if (!empty($list_solicitor_id)) {
                $result = $db->query('SELECT id, title FROM ' . BID_PREFIX_GLOBAL . '_solicitor WHERE id IN (' . implode(',', array_keys($list_solicitor_id)) . ')');
                while ($row = $result->fetch()) {
                    $list_result[$list_solicitor_id[$row['id']]]['chu_dau_tu'] = $row['title'];
                }
            }
        }
        /*
         * $result_detail = $db->query("SELECT * FROM nv4_vi_bidding_open_detail WHERE so_tbmt IN ('" . $array_result . "')");
         * while ($row = $result_detail->fetch()) {
         * $list_result_detail[] = $row;
         * }
         */

        if (!empty($list_result)) {
            foreach ($list_result as $data) {
                $data['title_a'] = nv_htmlspecialchars($data['goi_thau']);
                $data['thoi_diem_mo_thau'] = $data['thoi_diem_mo_thau'] > 0 ? nv_date(($prefix_lang == 1 ? 'H:i m/d/Y' : 'H:i d/m/Y'), $data['thoi_diem_mo_thau']) : '';
                $data['thoi_diem_hoan_thanh'] = $data['thoi_diem_hoan_thanh'] > 0 ? nv_date($date_format, $data['thoi_diem_hoan_thanh']) : '';                $data['trang_thai'] = $lang_module['kqmt_status' . $data['trang_thai']];
                $data['gia_goi_thau'] = number_format($data['gia_goi_thau']);
                /*
                 * foreach ($list_result_detail as $result_detail) {
                 * $xtpl->assign('DETAIL', $result_detail);
                 * $xtpl->parse('main.result_open.loop.detail');
                 * }
                 */
                if ($site_lang != 'vi') {
                    $data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['viewopen'] . '/' . $data['alias'] . '-' . $data['so_tbmt'] . '.html';
                } else {
                    $data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['viewopen'] . '/' . $data['alias'] . '-' . $data['so_tbmt'] . '.html';
                }

                $xtpl->assign('DATA', $data);

                $arrMess = [
                    'vi' => sprintf(get_lang('vi', 'title_search_by_result_open'), $data['title']),
                    'en' => sprintf(get_lang('en', 'title_search_by_result_open'), $data['title'])
                ];

                $arrLink = [
                    'vi' => URL_RE . '?mt=' . $data['so_tbmt'],
                    'en' => URL_RE . 'en/?mt=' . $data['so_tbmt']
                ];

                // Thông báo Push Notification cho người dùng
                // insertInform($vip_id, $arrMess, $data['link_view']);
                $arrInform['nv_theme_result_open_mail'][] = [
                    'vip_id' => $vip_id,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];

                if (!empty($data['chu_dau_tu'])) {
                    $xtpl->parse('main.result_open.loop.chu_dau_tu');
                }
                if (!empty($data['ben_moi_thau'])) {
                    $xtpl->parse('main.result_open.loop.ben_moi_thau');
                }
                if (!empty($data['trang_thai'])) {
                    $xtpl->parse('main.result_open.loop.trang_thai');
                }
                if (!empty($data['thoi_diem_mo_thau'])) {
                    $xtpl->parse('main.result_open.loop.thoi_diem_mo_thau');
                }
                if (!empty($data['thoi_diem_hoan_thanh'])) {
                    $xtpl->parse('main.result_open.loop.thoi_diem_hoan_thanh');
                }
                if (!empty($data['gia_goi_thau'])) {
                    $xtpl->parse('main.result_open.loop.gia_goi_thau');
                }
                if (!empty($data['so_luong_nha_thau'])) {
                    $xtpl->parse('main.result_open.loop.so_luong_nha_thau');
                }
                $xtpl->parse('main.result_open.loop');
            }
            $xtpl->parse('main.result_open');
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_bidding_mail($array_filter, $vip_id, &$arrInform)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func, $prefix_lang, $date_format, $datetime_format;

    $arr_field = array(
        1 => 'Hàng hóa',
        2 => 'Xây lắp',
        3 => 'Tư vấn',
        4 => 'Phi tư vấn',
        5 => 'Hỗn hợp'
    );

    if (!empty($array_filter)) {
        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        foreach ($array_filter as $filter_id => $arr_list) {
            // Lấy tên bộ lọc
            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();

            // link sửa bộ lọc
            $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
            $from = $filter_info['time_find'] > 0 ? nv_date($date_format, NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date($date_format, NV_CURRENTTIME - 86400 * 14);
            $filter_info['link_search'] = '';
            $filter_info['link_search_detail'] = NV_MY_DOMAIN . '/' . $site_lang . '/detail?';
            if ($filter_info['key_search'] != '') {
                $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
            }
            if ($filter_info['key_search2'] != '') {
                $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
            }
            $filter_info['link_search'] .= '&sfrom=' . $from;
            $filter_info['link_search'] .= '&sto=' . nv_date($date_format, NV_CURRENTTIME);
            $filter_info['link_search'] .= '&is_advance=1';
            $filter_info['link_search'] .= '&userid=' . $vip_id;
            $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
            $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];
            $filter_info['link_search'] .= '&search_one_key=' . $filter_info['search_one_key'];

            if ($filter_info['cat'] > 0) {
                $filter_info['link_search'] .= '&cat=' . $filter_info['cat'];
            }
            if ($filter_info['field'] != '') {
                $array_field = explode(',', $filter_info['field']);
                foreach ($array_field as $field) {
                    $filter_info['link_search'] .= '&field[]=' . $field;
                }
            }
            if ($filter_info['without_key'] != '') {
                $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
            }
            if ($filter_info['type_org'] > 0) {
                $filter_info['link_search'] .= '&type_org=' . $filter_info['type_org'];
            }
            if ($filter_info['money_from'] > 0) {
                $filter_info['link_search'] .= '&money_from=' . number_format($filter_info['money_from'], 0, '.', ',');
            }
            if ($filter_info['money_to'] > 0) {
                $filter_info['link_search'] .= '&money_to=' . number_format($filter_info['money_to'], 0, '.', ',');
            }
            if ($filter_info['price_from'] > 0) {
                $filter_info['link_search'] .= '&price_from=' . number_format($filter_info['price_from'], 0, '.', ',');
            }
            if ($filter_info['price_to'] > 0) {
                $filter_info['link_search'] .= '&price_to=' . number_format($filter_info['price_to'], 0, '.', ',');
            }

            $filter_info['link_search_detail'] = $filter_info['link_search_detail'] . $filter_info['link_search'];
            $xtpl->assign('LINK_FILTER', $filter_info['link_search_detail']);

            // ghi log bộ lọc
            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);

            $xtpl->assign('FILTER_NAME', $filter_info['title']);

            if (!empty($arr_list['list_bid'])) {
                if (sizeof($arr_list['list_bid']) > 50) {
                    $xtpl->assign('FILTER_NUMBER', sprintf($lang_module['limit_filter_bid'], number_format(sizeof($arr_list['list_bid']))));
                    $xtpl->parse('main.filter.number');
                }

                file_put_contents($create_mail_file, 'list_bid: ' . sizeof($arr_list['list_bid']) . "\n", FILE_APPEND);
                $i_break = 0;

                // lọc bỏ tin trùng nhau
                $arr_stbmt = array();
                foreach ($arr_list['list_bid'] as $array_data) {
                    $array_data['so_tbmt'] = explode('-', $array_data['so_tbmt']);
                    if (!isset($arr_stbmt[$array_data['so_tbmt'][0]])) {
                        $arr_stbmt[$array_data['so_tbmt'][0]] = $array_data['so_tbmt'][1];
                    } else {
                        $array_data['so_tbmt'][1] = intval($array_data['so_tbmt'][1]);
                        $old = intval($arr_stbmt[$array_data['so_tbmt'][0]]);
                        if ($old < $array_data['so_tbmt'][1]) {
                            $arr_stbmt[$array_data['so_tbmt'][0]] = '0' . $array_data['so_tbmt'][1];
                        }
                    }
                }
                // hiển thị
                foreach ($arr_list['list_bid'] as $array_data) {
                    $so_tbmt = explode('-', $array_data['so_tbmt']);
                    if (isset($arr_stbmt[$so_tbmt[0]]) && intval($arr_stbmt[$so_tbmt[0]]) == intval($so_tbmt[1])) {
                        ++$i_break;
                        if ($i_break > 50) {
                            break;
                        }
                        $array_data['title'] = $array_data['goi_thau'];
                        $array_data['title_a'] = nv_htmlspecialchars($array_data['goi_thau']);

                        // Bôi vàng những từ trùng với từ khóa tìm kiếm, tìm kiếm tuyệt đối
                        $arr_key = explode(',', $filter_info['key_search']);

                        foreach ($arr_key as $key) {
                            $key = trim($key);
                            $array_data['goi_thau'] = BoldKeywordInStr(strip_tags($array_data['goi_thau']), $key);
                            $array_data['ben_moi_thau'] = strip_tags(BoldKeywordInStr($array_data['ben_moi_thau'], $key));
                        }
                        $array_data['ngay_dang_tai'] = nv_date(($prefix_lang == 1 ? 'H:i m/d/y' : 'H:i d/m/y'), $array_data['ngay_dang_tai']);
                        $tbmt_t = getUrlByLanguage('tbmt');
                        if ($site_lang != 'vi') {
                            $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                        } else {
                            $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                        }

                        $check = md5($vip_id . $so_tbmt[0]);
                        $array_data['link_follow'] = NV_MY_DOMAIN . ($site_lang != 'vi' ? '/' . $site_lang : '') . '/follow?bid_id=' . $array_data['id'] . '&bid_code=' . $so_tbmt[0] . '&vipid=' . $vip_id . '&check=' . $check;

                        $xtpl->assign('DATA', $array_data);
                        $arrMess = [
                            'vi' => sprintf(get_lang('vi', 'title_search_by_filter'), $array_data['title']),
                            'en' => sprintf(get_lang('en', 'title_search_by_filter'), $array_data['title'])
                        ];

                        $arrLink = [
                            'vi' => URL_RE . '?tb=' . $array_data['id'],
                            'en' => URL_RE . 'en/?tb=' . $array_data['id']
                        ];

                        // Thông báo Push Notification cho người dùng
                        $arrInform['nv_theme_bidding_mail'][] = [
                            'vip_id' => $vip_id,
                            'mess' => $arrMess,
                            'link' => $arrLink
                        ];
                        // insertInform($vip_id, $arrMess, $array_data['link_view']);

                        if ($array_data['chu_dau_tu'] != '') {
                            $xtpl->parse('main.filter.content.chu_dau_tu');
                        }

                        if ($array_data['ten_du_an'] != '') {
                            $xtpl->parse('main.filter.content.ten_du_an');
                        }

                        // Kiểm tra xem có giá gói thầu k
                        if ($array_data['price'] > 0) {
                            $xtpl->assign('PRICE_FM', number_format($array_data['price'], 0, ',', '.') . ' VND');
                            $xtpl->parse('main.filter.content.price');
                        }

                        if ($array_data['notify_chance_time'] == 1) {
                            $content_chance = $db->query('SELECT content_chance FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_update WHERE bid_id=' . $array_data['id'] . ' ORDER BY addtime DESC')->fetchColumn();
                            $xtpl->assign('CONTENT_CHANCE', $content_chance);
                            $xtpl->parse('main.filter.content.notify_chance_time');
                        } else {
                            // Tạo link theo dõi tin thầu
                            $xtpl->parse('main.filter.content.follow');
                        }

                        if (intval($so_tbmt[1]) > 0) {
                            $xtpl->parse('main.filter.content.notify_old');
                        }

                        if ($array_data['note'] != '') {
                            $xtpl->parse('main.filter.content.note');
                        }

                        $xtpl->parse('main.filter.content');
                    }
                }
            }
            $xtpl->parse('main.filter');
        }
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_change_mail($array_change, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func, $prefix_lang, $date_format, $datetime_format;
    if (!empty($array_change)) {

        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        $array_codetbmt = '';
        foreach ($array_change as $change) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id = ' . $change['bid_id'] . '');
            if ($row = $result->fetch()) {
                $tbmt = explode('-', $row['so_tbmt']);
                $array_codetbmt = $tbmt[0];

                $row['title'] = $row['goi_thau'];
                $row['title_a'] = nv_htmlspecialchars($row['title']);
                $row['time'] = $change['chance_time'] > 0 ? nv_date(($prefix_lang == 1 ? 'H:i m/d/Y' : 'H:i d/m/Y'), $change['chance_time']) : '';
                $row['content'] = $change['notify_vip'];
                $tbmt_t = getUrlByLanguage('tbmt');
                if ($site_lang != 'vi') {
                    $row['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $row['alias'] . '-' . $row['id'] . '.html';
                } else {
                    $row['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $row['alias'] . '-' . $row['id'] . '.html';
                }

                $row['link_follow'] = NV_MY_DOMAIN . '/' . $site_lang . '/bidding/follow';
                $xtpl->assign('DATA', $row);
                $arrMess = [
                    'vi' => sprintf(get_lang('vi', 'title_search_by_notify'), date(($prefix_lang == 1 ? 'm/d/Y H:i' : 'd/m/Y H:i')), $row['title']),
                    'en' => sprintf(get_lang('en', 'title_search_by_notify'), date(($prefix_lang == 1 ? 'm/d/Y H:i' : 'd/m/Y H:i')), $row['title'])
                ];

                $arrLink = [
                    'vi' => URL_RE . '?tb=' . $row['id'],
                    'en' => URL_RE . 'en/?tb=' . $row['id']
                ];
                // Thông báo Push Notification cho người dùng
                // insertInform($vip_id, $arrMess, $row['link_view']);
                $arrInform['nv_theme_change_mail'][] = [
                    'vip_id' => $vip_id,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];

                $xtpl->parse('main.change.loop');
            }
        }
        $xtpl->parse('main.change');
    }

    // chỉ lấy đại diện 1 tin theo dõi
    $mail_footer_follow = '';
    if (!empty($array_codetbmt)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date($date_format, $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}

function nv_theme_reason_mail($array_change, $vip_id, &$arrInform, $username)
{
    global $db, $site_lang, $lang_module, $mod_func, $prefix_lang, $date_format, $datetime_format;
    if (!empty($array_change)) {

        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        $array_codetbmt = '';
        foreach ($array_change as $change) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row_reason WHERE bid_id = ' . $change['bid_id'] . '');
            if ($row = $result->fetch()) {

                $tbmt = explode('-', $row['so_tbmt']);
                $array_codetbmt = $tbmt[0];

                $row['title'] = $row['ten_goi_thau'];
                $row['title_a'] = nv_htmlspecialchars($row['ten_goi_thau']);
                $row['time'] = $change['thoi_diem_huy_thau'] > 0 ? nv_date(($prefix_lang == 1 ? 'H:i m/d/Y' : 'H:i d/m/Y'), $change['thoi_diem_huy_thau']) : '';
                $row['content'] = $change['ly_do'];
                $tbmt_t = getUrlByLanguage('tbmt');
                $row['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . change_alias($row['title_a']) . '-' . $row['bid_id'] . '.html';
                $row['link_follow'] = NV_MY_DOMAIN . '/' . $site_lang . '/bidding/follow';
                $arrMess = [
                    'vi' => sprintf(get_lang('vi', 'title_search_by_huy_thau'), $row['title']),
                    'en' => sprintf(get_lang('en', 'title_search_by_huy_thau'), $row['title'])
                ];

                $arrLink = [
                    'vi' => URL_RE . '?tb=' . $row['bid_id'],
                    'en' => URL_RE . 'en/?tb=' . $row['bid_id']
                ];

                // Thông báo Push Notification cho người dùng
                // insertInform($vip_id, $arrMess, $row['link_view']);
                $arrInform['nv_theme_reason_mail'][] = [
                    'vip_id' => $vip_id,
                    'mess' => $arrMess,
                    'link' => $arrLink
                ];

                $xtpl->assign('DATA', $row);
                $xtpl->parse('main.reason.loop');
            }
        }
        $xtpl->parse('main.reason');
    }
    // chỉ lấy đại diện 1 tin theo dõi
    $mail_footer_follow = '';
    if (!empty($array_codetbmt)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date($date_format, $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}
