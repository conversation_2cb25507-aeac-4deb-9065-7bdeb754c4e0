#!/bin/bash

SOURCE="${BASH_SOURCE[0]}"
while [ -h "$SOURCE" ]; do
  TARGET="$(readlink "$SOURCE")"
  if [[ $TARGET == /* ]]; then
    SOURCE="$TARGET"
  else
    DIR="$( dirname "$SOURCE" )"
    SOURCE="$DIR/$TARGET"
  fi
done
DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
cd "$DIR/"
DIR_PATH=$PWD

echo '---------------- <PERSON><PERSON><PERSON> đầu chạy tool ----------------'
echo ''

while : ; do
  php $DIR_PATH/elastic_update_bidding_field.php
  code=$?
  if [[ $code == 1 ]]; then
    echo "Đã hoàn tất VN"
    break
  fi
  sleep 1
done

while : ; do
  php $DIR_PATH/elastic_update_bidding_field.php --site_lang=en
  code=$?
  if [[ $code == 1 ]]; then
    echo "Đã hoàn tất EN"
    break
  fi
  sleep 1
done
