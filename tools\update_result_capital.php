<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
// [detached from 2174788.pts-17.craws]

$filename = NV_ROOTDIR . '/tools/update_result_capital_' . NV_PREFIXLANG . '.txt';
if (file_exists($filename)) {
    $strID = file_get_contents($filename);
    $arrID = explode('_', $strID);
    $_last_id = intval($arrID[0]);
    $max_id = intval($arrID[1]);
} else {
    $_last_id = 0;
    $sql = "SELECT max(id) FROM `" . NV_PREFIXLANG . "_bidding_result`";
    $max_id = $db->query($sql)->fetchColumn();
}

if ($_last_id < 1013422) {
    $_last_id = 1013422;
}

$seperator = ' あ ';

for ($i = 0; $i < 20; $i++) {
    echo "last_id " . NV_PREFIXLANG . ": " . $_last_id . "\n";
    $_last_id2 = $_last_id + 1000;

    $params = [];
    $sql = "SELECT id, code, bidno, bidfieid FROM `" . NV_PREFIXLANG . "_bidding_result` WHERE id > " . $_last_id . " AND id < " . $_last_id2 . " ORDER BY id ASC LIMIT 100";
    $_query = $db->query($sql);
    $array_capital = [];
    $array_business = [];
    $_last_id = 0;
    while ($row = $_query->fetch()) {
        $_last_id = $row['id'];
        if ($_last_id > $max_id) {
            break;
        }
        $res_plans = $db->query('SELECT id, owner_equity FROM nv4_vi_bidding_plans_contract WHERE so_tbmt_mst=' . $db->quote($row['code']) . ' LIMIT 1');
        $khlcnt = $res_plans->fetch();
        $res_plans->closeCursor();
        if (empty($khlcnt) && !empty($row['bidno'])) {
            $res_plans = $db->query('SELECT id, owner_equity FROM nv4_vi_bidding_plans_contract WHERE code=' . $db->quote($row['bidno']) . ' LIMIT 1');
            $khlcnt = $res_plans->fetch();
            $res_plans->closeCursor();
        }
        if (empty($khlcnt)) {
            $res_plans = $db->query('SELECT nguon_von as owner_equity FROM nv4_vi_bidding_detail t1 INNER JOIN nv4_vi_bidding_row t2 ON t1.id=t2.id WHERE t2.so_tbmt = ' . $db->quote($row['code']) . ' LIMIT 1');
            $khlcnt = $res_plans->fetch();
            $res_plans->closeCursor();
        }
        // Lấy content nhà thầu
        $content_business = '';
        $result = $db->query('SELECT GROUP_CONCAT(DISTINCT bidder_name SEPARATOR "' . $seperator . '") AS content_business FROM ' . NV_PREFIXLANG . '_bidding_result_business WHERE resultid = ' . $db->quote($row['id']));
        if ($row2 = $result->fetch()) {
            $content_business = $row2['content_business'];
        }
        $result->closeCursor();
        $content_business = preg_split('/' . $seperator . '/', $content_business);
        $content_business = array_map('trim', $content_business);
        $content_business = array_filter(array_unique($content_business));
        $content_business = implode($seperator, $content_business);
        $content_business = mb_strtolower($content_business);
        $params['body'][] = [
            'update' => [
                '_index' => NV_LANG_ELASTIC . 'dauthau_result',
                '_id' => $row['id']
            ]
        ];
        $params['body'][] = [
            'doc' => [
                'capital' => $khlcnt['owner_equity'] ?? '',
                'content_business' => $content_business,
                'bidfieid' => $row['bidfieid']
            ]
        ];
        $array_capital[$row['id']] = $khlcnt['owner_equity'] ?? '';
        unset($khlcnt);
    }
    $_query->closeCursor();

    if (!empty($params['body'])) {
        $hosts = array(
            $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
        );

        $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
            ->setHosts($hosts)
            ->setRetries(0)
            ->build();
        $responses = $client->bulk($params)->asArray();
        if (!empty($responses['items'])) {
            foreach ($responses['items'] as $value) {
                if ($value['update']['status'] == 200) {
                    $_id = $value['update']['_id'];
                    $exec = $db->exec("UPDATE " . NV_PREFIXLANG . "_bidding_result SET capital = " . $db->quote($array_capital[$_id]) . " WHERE id=" . $_id);
                }
            }
        }
        unset($params);
        file_put_contents($filename, $_last_id . '_' . $max_id);
    }
    if ($_last_id > $max_id) {
        echo ('Thực hiện xong');
        exit(1);
    } elseif (empty($_last_id)) {
        $_last_id = $_last_id2;
        if ($_last_id > $max_id) {
            echo ('Thực hiện xong');
            exit(1);
        }
    }
    usleep(50000); // sleep 0.05 giây
}
