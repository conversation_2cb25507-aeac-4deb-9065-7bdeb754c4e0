<?php
// Xac dinh thu muc goc cua site
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
set_time_limit(0);

$id_tbmt = 0;
if (file_exists(NV_ROOTDIR . '/tools/update_content_notify_no_KQLCNT.txt')) {
    $id_tbmt = file_get_contents(NV_ROOTDIR . '/tools/update_content_notify_no_KQLCNT.txt');
    $id_tbmt = intval($id_tbmt);
}
try {

    $waitTimeoutInSeconds = 2;
    if ($fp = fsockopen($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
        // It worked
        $elastic_online = 1;
    } else {
        // It didn't work
        $elastic_online = 0;
        echo "Server Elasticsearch didn't work: ";
        echo "ERROR: $errCode - $errStr<br />\n";
    }
    fclose($fp);
    if ($elastic_online) {
        $hosts = array(
            $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
        );
        do {
            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();

            $query_url = $db->query('SELECT * FROM nv4_vi_bidding_result WHERE id > ' . $id_tbmt . ' ORDER BY id ASC LIMIT 100');
            $params = [
                'body' => []
            ];
            $id_tbmt = 0;
            while ($row = $query_url->fetch()) {
                $id_tbmt = $row['id'];

                // msc mới bidno là mã gói thầu
                if ($row['is_new_msc'] == 1) {
                    $row['content_full'] = str_replace($row['bidno'], '', $row['content_full']);
                    $row['content_full'] .= ' ' . $row['bidno'];
                }

                // msc cũ bid_code là số tbmt k có version thông báo
                $row['content_full'] = str_replace(' ' . $row['bid_code'] . ' ', '', $row['content_full']);
                $row['content_full'] .= ' ' . $row['bid_code'] . ' ';

                $row['content'] = change_alias($row['content_full']); // Không có dấu tiếng việt
                $row['content'] = str_replace('-', ' ', $row['content']);

                $db->query('UPDATE nv4_vi_bidding_result SET content = ' . $db->quote($row['content']) . ', content_full = ' . $db->quote($row['content_full']) . ', bidno=' . $db->quote($row['bidno']) . ' WHERE id =' . $row['id']);

                $params['body'][] = [
                    'index' => [
                        '_index' => 'dauthau_result',
                        '_id' => $row['id']
                    ]
                ];
                $params['body'][] = $row;
                echo $row['bidno'] . " --> update\n";
            }
            $query_url->closeCursor();

            if (!empty($params['body'])) {
                $responses = $client->bulk($params)->asArray();
                if (empty($responses['errors'])) {
                    unset($responses['items']);
                    echo "update thành công<br><br>\n";
                } else {
                    echo '<pre>';
                    print_r($responses);
                    echo '</pre>';
                }
            } else {
                echo 'Ko co du du lieu';
            }
            if ($id_tbmt > 0) {
                file_put_contents(NV_ROOTDIR . '/tools/update_content_notify_no_KQLCNT.txt', $id_tbmt);
            }
        } while ($id_tbmt > 0);
    }
} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
}
echo '<br><br>Update trong: ' . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
