<?php

/**
 * @Project dauthau.info-crontab-db
 * <AUTHOR> (<EMAIL>)
 * @Copyright (C) 2022 VINADES.,JSC. All rights reserved
 * @Code Ngọc <PERSON>ân (<EMAIL>)
 * <PERSON><PERSON> gộp các bên mời thầu không liên kết được bên mời thầu (do bên mình tự sinh ra từ dữ liệu mời thầu)
 * vào danh sách bên mời thầu không tìm thấy thông tin
 * https://vinades.org/dauthau/dauthau.info/-/issues/2312
 */

// Xac dinh thu muc goc cua site

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$file_id = NV_ROOTDIR . '/data/logs/upsolicitor_unlink_lid.txt';
$file_log = NV_ROOTDIR . '/data/logs/upsolicitor_unlink_log.json';
$file_error = NV_ROOTDIR . '/data/logs/upsolicitor_unlink_error.json';

if (file_exists($file_id)) {
    $_last_id = file_get_contents($file_id);
    $_last_id = intval($_last_id);
} else {
    $_last_id = 0;
}
$limit = 10;
do {
    echo "last id: " . number_format($_last_id) . "\n";
    $_query_1 = $db->query('SELECT id, title FROM nv4_bidding_solicitor WHERE id > ' . $_last_id . ' AND id NOT IN (SELECT id FROM nv4_bidding_solicitor_detail) ORDER BY id ASC LIMIT ' . $limit);
    $array_id = $array_data = $params = $data_logs = [];
    while ($tmp = $_query_1->fetch()) {
        $array_data[$tmp['id']] = $tmp['title'];
        $array_id[] = $tmp['id'];
    }
    $_query_1->closeCursor();

    $_last_id = 0;
    foreach ($array_id as $solicitor_id) {
        $_last_id = $solicitor_id;
        $_log_tmp = [];
        $_title = $array_data[$solicitor_id];
        $_project_arr_id = $_bids_arr_id = $_plan_arr_id = $_open_arr_id = $_result_arr_id = [];

        // Xóa BMT này ở bảng solicitor
        $db->beginTransaction();
        try {
            $exc = $db->exec('DELETE FROM nv4_bidding_solicitor WHERE id = ' . $solicitor_id);
            if ($exc) {
                $params['body'][] = array(
                    'delete' => array(
                        '_index' => 'dauthau_solicitor',
                        '_id' => $solicitor_id
                    )
                );

                // Danh sách các dự án
                $_query_pi = $db->query('SELECT id FROM nv4_vi_project_investment WHERE solicitor_id = ' . $solicitor_id);
                while ($_row1 = $_query_pi->fetch()) {
                    $_project_arr_id[] = $_row1['id'];
                }
                $_query_pi->closeCursor();

                // Danh sách các TBMT
                $_query_b = $db->query('SELECT id FROM nv4_vi_bidding_row WHERE solicitor_id = ' . $solicitor_id);
                while ($_row2 = $_query_b->fetch()) {
                    $_bids_arr_id[] = $_row2['id'];
                }
                $_query_b->closeCursor();

                // Danh sách các KQMT
                $_query_o = $db->query('SELECT id FROM nv4_vi_bidding_open WHERE solicitor_id = ' . $solicitor_id);
                while ($_row3 = $_query_o->fetch()) {
                    $_open_arr_id[] = $_row3['id'];
                }
                $_query_o->closeCursor();

                // Danh sách các KHLCNT
                $_query_p = $db->query('SELECT id FROM nv4_vi_bidding_plans WHERE solicitor_id = ' . $solicitor_id);
                while ($_row4 = $_query_p->fetch()) {
                    $_plan_arr_id[] = $_row4['id'];
                }
                $_query_p->closeCursor();

                // Danh sách các KQLCNT
                $_query_r = $db->query('SELECT id FROM nv4_vi_bidding_result WHERE solicitor_id = ' . $solicitor_id);
                while ($_row5 = $_query_r->fetch()) {
                    $_result_arr_id[] = $_row5['id'];
                }
                $_query_r->closeCursor();

                // Kiểm tra để insert or update vào bảng nv4_bidding_solicitor_unlink_data
                $_solicitor_unlink_id = 0;
                $_solicitor_unlink = $db->query('SELECT id FROM `nv4_bidding_solicitor_unlink_data` WHERE title = ' . $db->quote($_title) . '')->fetchColumn();
                if (empty($_solicitor_unlink)) {
                    // Insert
                    $stmt= $db->prepare('INSERT INTO nv4_bidding_solicitor_unlink_data (title, alias) VALUES (:title, :alias)');
                    $stmt->bindParam(':title', $_title, PDO::PARAM_STR);
                    $stmt->bindParam(':alias', change_alias($_title), PDO::PARAM_STR);
                    $stmt->execute();
                    $_solicitor_unlink_id = $db->lastInsertId();
                } else {
                    // Cập nhật update_data = 0 để thống kê lại
                    $db->exec('UPDATE nv4_bidding_solicitor_unlink_data SET update_data = 0 WHERE id = ' . $_solicitor_unlink);
                }
                $_log_tmp['bmt_name'] = $_title;
                $_log_tmp['bmt_unlink_data'] = $_solicitor_unlink_id;
                $_log_tmp['project'] = implode(', ', $_project_arr_id);
                $_log_tmp['plan'] = implode(', ', $_plan_arr_id);
                $_log_tmp['row'] = implode(', ', $_bids_arr_id);
                $_log_tmp['open'] = implode(', ', $_open_arr_id);
                $_log_tmp['result'] = implode(', ', $_result_arr_id);
                $_log_data = json_encode($_log_tmp, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . "\n";
                // Lưu log dữ liệu thầu lại
                file_put_contents($file_log, $_log_data, FILE_APPEND);

                // Xử lý tìm và cập nhật solicitor_id = 0 cho dự án, tbmt, khlcnt, kqmt, kqlcnt
                $db->exec('UPDATE nv4_vi_project_investment SET solicitor_id = 0, elasticsearch = 9 WHERE solicitor_id = ' . $solicitor_id);
                $db->exec('UPDATE nv4_vi_bidding_row SET solicitor_id = 0, elasticsearch = 19 WHERE solicitor_id = ' . $solicitor_id);
                $db->exec('UPDATE nv4_vi_bidding_open SET solicitor_id = 0, elasticsearch = 9 WHERE solicitor_id = ' . $solicitor_id);
                $db->exec('UPDATE nv4_vi_bidding_result SET solicitor_id = 0, elasticsearch = 19 WHERE solicitor_id = ' . $solicitor_id);
                $db->exec('UPDATE nv4_vi_bidding_plans SET solicitor_id = 0, elasticsearch = 9 WHERE solicitor_id = ' . $solicitor_id);
                $db->exec('UPDATE nv4_en_project_investment SET solicitor_id = 0, elasticsearch = 9 WHERE solicitor_id = ' . $solicitor_id);
                $db->exec('UPDATE nv4_en_bidding_row SET solicitor_id = 0, elasticsearch = 19 WHERE solicitor_id = ' . $solicitor_id);
                $db->exec('UPDATE nv4_en_bidding_open SET solicitor_id = 0, elasticsearch = 9 WHERE solicitor_id = ' . $solicitor_id);
                $db->exec('UPDATE nv4_en_bidding_result SET solicitor_id = 0, elasticsearch = 19 WHERE solicitor_id = ' . $solicitor_id);
                $db->exec('UPDATE nv4_en_bidding_plans SET solicitor_id = 0, elasticsearch = 9 WHERE solicitor_id = ' . $solicitor_id);
            }
            $db->commit();
        } catch (PDOException $e) {
            file_put_contents($file_error, 'Lỗi xóa BMT id = ' . $solicitor_id . ': ' . print_r($e->getMessage(), true)."\n\n", FILE_APPEND);
            $db->rollBack();
            echo '<pre>';
            print_r($e);
            echo '</pre>';
        }
    }

    // Xử lý xóa các btm không liên kết được trên ES
    if (!empty($params)) {
        $hosts = array(
            $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
        );

        $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
            ->setHosts($hosts)
            ->setRetries(0)
            ->build();

        $response = $client->bulk($params);
        echo "delete number: " . sizeof($response['items']) . "\n";
    }
    if ($_last_id > 0) {
        file_put_contents($file_id, $_last_id);
    }
} while ($_last_id > 0);

die('END');
