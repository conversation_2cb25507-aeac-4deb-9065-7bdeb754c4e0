<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$id = 0;

$hosts = array(
    $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
);

$client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
    ->setHosts($hosts)
    ->setRetries(0)
    ->build();
$dem = 0;
do {
    $sql = "SELECT id, code, title, is_new_msc FROM `" . NV_PREFIXLANG . "_bidding_result` WHERE id > " . $id . " ORDER BY id ASC LIMIT 20";
    $_query = $db->query($sql);
    $params = ['body' => []];

    $num_rows = 0;
    while ($row = $_query->fetch()) {
        echo "\n-->> ID: " . $row['id'] . "-MSC: " . $row['is_new_msc'];

        $params['body'][] = [
            'update' => [
                '_index' => NV_LANG_ELASTIC . 'dauthau_result',
                '_id' => $row['id']
            ]
        ];

        $params['body'][] = [
            'doc' => [
                'is_new_msc' => $row['is_new_msc']
            ]
        ];

        $id = $row['id'];
        $num_rows++;
        $dem++;
    }
    $_query->closeCursor();

    if (!empty($params['body'])) {
        // Thực hiện cập nhật
        $response = $client->bulk($params);
    }

    if (empty($num_rows)) {
        echo ("\n-->> Thực hiện xong");
        break;
    }
} while ($num_rows > 0);

die("-->> OK: " . $dem . " data");
