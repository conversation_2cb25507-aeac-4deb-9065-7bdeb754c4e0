#!/bin/bash

SOURCE="${BASH_SOURCE[0]}"
while [ -h "$SOURCE" ]; do
  TARGET="$(readlink "$SOURCE")"
  if [[ $TARGET == /* ]]; then
    SOURCE="$TARGET"
  else
    DIR="$( dirname "$SOURCE" )"
    SOURCE="$DIR/$TARGET"
  fi
done
DIR="$( cd -P "$( dirname "$SOURCE" )" >/dev/null 2>&1 && pwd )"
DIR_PATH=$PWD

while true; do
    php "$DIR_PATH/update_solicitor_unlink_id.php" --my_index=dauthau_open
    if [ ! -f "$DIR_PATH/update_solicitor_unlink_id_scroll.txt" ]; then
        echo "All documents updated."
        break
    fi
    sleep 2
done


while true; do
    php "$DIR_PATH/update_solicitor_unlink_id.php" --my_index=en_dauthau_open
    if [ ! -f "$DIR_PATH/update_solicitor_unlink_id_scroll.txt" ]; then
        echo "All documents updated."
        break
    fi
    sleep 2
done


while true; do
    php "$DIR_PATH/update_solicitor_unlink_id.php" --my_index=dauthau_result
    if [ ! -f "$DIR_PATH/update_solicitor_unlink_id_scroll.txt" ]; then
        echo "All documents updated."
        break
    fi
    sleep 2
done


while true; do
    php "$DIR_PATH/update_solicitor_unlink_id.php" --my_index=en_dauthau_result
    if [ ! -f "$DIR_PATH/update_solicitor_unlink_id_scroll.txt" ]; then
        echo "All documents updated."
        break
    fi
    sleep 2
done
