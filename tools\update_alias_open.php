<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

// SELECT * FROM `nv4_vi_bidding_open` WHERE `so_tbmt`='20170720118-01';

$seperator = ' あ ';
$seperator_replace = ' SpaceKey '; // Do change_alias làm mất ký tự phân cách nên dùng cái này rồi replace lại sau
$_last_id = 0;

$sql = "SELECT max(id) FROM `" . NV_PREFIXLANG . "_bidding_open`";
$max_id = $db->query($sql)->fetchColumn();

try {
    $hosts = array(
        $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();

    do {
        echo "_last_id_" . NV_PREFIXLANG . ": " . $_last_id . "\n";
        $params = [
            'body' => []
        ];

        $_last_id2 = $_last_id + 1000;
        $query_url = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_open WHERE id > ' . $_last_id . ' AND id < ' . $_last_id2 . ' ORDER BY id ASC LIMIT 500');
        $arr_open_sotbmt = $arr_open = [];
        while ($row = $query_url->fetch()) {
            $_last_id = $row['id'];
            $row['content_full'] = $row['so_tbmt'] . $seperator_replace . $row['goi_thau'] . $seperator_replace . $row['ben_moi_thau'];
            $row['goi_thau_search'] = str_replace('-', ' ', change_alias($row['goi_thau']));
            $arr_open_sotbmt[$row['so_tbmt']] = $row['so_tbmt'];
            $arr_open[$row['so_tbmt']] = $row;
        }
        $query_url->closeCursor();

        if (!empty($arr_open_sotbmt)) {
            $query_url = $db->query("SELECT so_tbmt, so_dkkd, ten_nha_thau, ten_lien_danh FROM " . NV_PREFIXLANG . "_" . BIDDING_MODULE . "_open_detail WHERE so_tbmt IN ('" . implode("','", $arr_open_sotbmt) . "')");
            while ($row = $query_url->fetch()) {
                if (isset($arr_open[$row['so_tbmt']])) {
                    $arr_open[$row['so_tbmt']]['content_full'] .= $seperator_replace . $row['so_dkkd'] . $seperator_replace . $row['ten_nha_thau'] . $seperator_replace . $row['ten_lien_danh'];
                }
            }
            $query_url->closeCursor();

            $client->deleteByQuery([
                'index' => NV_LANG_ELASTIC . 'dauthau_open',
                'body' => [
                    'query' => [
                        'terms' => [
                            'so_tbmt.keyword' => array_values($arr_open_sotbmt)
                        ]
                    ]
                ]
            ]);
        }

        foreach ($arr_open as $value) {
            /*
            $params_delete = array();
            $params_delete['index'] = NV_LANG_ELASTIC . 'dauthau_open';
            $params_delete['id'] = $value['so_tbmt'];
            try {
                $client->delete($params_delete);
            } catch (Exception $e) {
                // print_r($e);
            }
            */

            $params['body'][] = [
                'index' => [
                    '_index' => NV_LANG_ELASTIC . 'dauthau_open',
                    '_id' => $value['id']
                ]
            ];

            $_num = ($value['id'] > 542330) ? 110 : 200;
            $value['alias'] = nv_clean_alias($value['goi_thau'], $_num);
            if (empty($value['alias'])) {
                $value['alias'] = nv_clean_alias($value['so_tbmt'], $_num);
            }

            $value['content_full'] = strip_tags($value['content_full']);

            $value['content'] = change_alias($value['content_full']);
            $value['content'] = str_replace('-', ' ', $value['content']);
            $value['content'] = str_replace($seperator_replace, $seperator, $value['content']);
            $value['content_full'] = str_replace($seperator_replace, $seperator, $value['content_full']);

            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_open SET elasticsearch=' . NV_CURRENTTIME . ', content = ' . $db->quote($value['content']) . ', alias = ' . $db->quote($value['alias']) . ', content_full = ' . $db->quote($value['content_full']) . ' WHERE id =' . $value['id']);
            $params['body'][] = $value;
        }

        if (!empty($params['body'])) {
            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();

            $responses = $client->bulk($params)->asArray();
            if (empty($responses['errors'])) {
                unset($responses['items']);
            }
            echo '<pre>';
            print_r($responses);
            echo '</pre>';
            unset($params, $responses);
        } else {
            echo 'Ko co du du lieu';
        }

        if ($_last_id > $max_id) {
            echo ('Thực hiện xong');
            break;
        } elseif (empty($_last_id)) {
            $_last_id = $_last_id2;
            if ($_last_id > $max_id) {
                echo ('Thực hiện xong');
                break;
            }
        }
    } while ($_last_id > 0);
} catch (Exception $e) {
    echo '<pre>';
    print_r($e);
    echo '</pre>';
}

function nv_clean_alias($string, $num = 110, $lower = true)
{
    $search = [
        '&amp;',
        '&#039;',
        '&quot;',
        '&lt;',
        '&gt;',
        '&#x005C;',
        '&#x002F;',
        '&#40;',
        '&#41;',
        '&#42;',
        '&#91;',
        '&#93;',
        '&#33;',
        '&#x3D;',
        '&#x23;',
        '&#x25;',
        '&#x5E;',
        '&#x3A;',
        '&#x7B;',
        '&#x7D;',
        '&#x60;',
        '&#x7E;'
    ];
    $replace = [
        '&',
        '\'',
        '"',
        '<',
        '>',
        '\\',
        '/',
        '(',
        ')',
        '*',
        '[',
        ']',
        '!',
        '=',
        '#',
        '%',
        '^',
        ':',
        '{',
        '}',
        '`',
        '~'
    ];

    $string = str_replace($search, $replace, $string);
    $string = nv_compound_unicode($string);
    $string = nv_EncString($string);
    $string = preg_replace(array(
        '/[^a-zA-Z0-9]/',
        '/[ ]+/',
        '/^[\-]+/',
        '/[\-]+$/'
    ), array(
        ' ',
        '-',
        '',
        ''
    ), $string);

    $len = strlen($string);
    if ($num and $len > $num) {
        $_substring = substr($string, 0, $num);
        while (str_contains($_substring, '-') and substr($string, $num, 1) != '-') {
            --$num;
            $_substring = substr($_substring, 0, $num);
        }
        $string = substr($string, 0, $num);
    }

    if ($lower) {
        $string = strtolower($string);
    }
    return $string;
}

$time_run = number_format((microtime(true) - NV_START_TIME), 2, '.', '');
die("xong: time_run: " . $time_run . "\n");
