<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);

// Xac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';

$date_format = ($prefix_lang == 1) ? 'm/d/Y' : 'd/m/Y';
$datetime_format = ($prefix_lang == 1) ? 'm/d/Y H:i' : 'd/m/Y H:i';
$create_mail_file = NV_ROOTDIR . '/data/create_mail_tbmt_pro1_' . $prefix_lang . '_' . date('Ymd') . '.txt';
if (file_exists(NV_ROOTDIR . '/data/create_mail_tbmt_pro1_' . $prefix_lang . '.txt')) {
    file_put_contents($create_mail_file, "=================================Chay trung nhau luc: " . date("d/m/Y H:i:s") . "================================\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "tbmt_pro1.php Chay trung nhau luc: " . date("d/m/Y H:i:s"));
    exit();
}

file_put_contents(NV_ROOTDIR . '/data/create_mail_tbmt_pro1_' . $prefix_lang . '.txt', NV_CURRENTTIME);

try {

    // Từ bảng tmp tổng hợp lại mail gửi đi cho từng VIP
    // Lấy danh sách các mail chưa gửi trong csdl để tiến hành gửi đi gom theo userid
    $time_start_thisday = mktime(0, 0, 0, date('n'), date('j'), date('Y'));
    $vips = array();
    $list_vipid = array();
    $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_pro1_id WHERE userid IN (SELECT user_id FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE ((' . NV_CURRENTTIME . ' - last_email) >= time_send*3600) AND vip=19 AND status = 1 AND prefix_lang = ' . $prefix_lang . ' ORDER BY last_email ASC) AND send_status = 0 LIMIT 20');
    while ($row = $query->fetch()) {
        $vips[$row['userid']] = array();
        $list_vipid[$row['userid']] = $row['userid'];
    }

    $list_vipid_all = array();
    // lấy các tài khoản mua bằng điểm, chu kỳ mail 6h
    if ($config_bidding['use_reward_points']) {
        $array_id_user = array();
        $query = $db->query('SELECT DISTINCT userid FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_pro1_id WHERE userid IN (SELECT userid FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE ((' . NV_CURRENTTIME . ' - lastmail) >= (6*3600)) AND vip=19 AND prefix_lang = ' . $prefix_lang . ' ORDER BY lastmail ASC) AND send_status = 0 LIMIT 10');
        while ($row = $query->fetch()) {
            $vips[$row['userid']] = [];
            $array_id_user[$row['userid']] = $row['userid'];
        }
        ksort($vips);
        $list_vipid_all = array_merge($list_vipid, $array_id_user);
    } else {
        $list_vipid_all = $list_vipid;
    }

    $list_vipid_all = implode(',', $list_vipid_all);
    if (!empty($list_vipid_all)) {
        $arr_vip = $db->query('SELECT id as cusid, phone, user_id as userid, email, sub_email, time_send, split_email_filters, last_email, active_user, from_time FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE user_id IN ( ' . $list_vipid_all . ') AND vip=19 AND status = 1 AND prefix_lang = ' . $prefix_lang . '');

        while ($vip_data = $arr_vip->fetch()) {
            // nếu vip này bật chế độ gửi email theo tài khoản
            if ($vip_data['active_user'] == 1) {
                $arr_subemail = [];
                $query_permission = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_permission WHERE vipid=' . $vip_data['cusid'] . ' AND totime >' . NV_CURRENTTIME . ' AND send_mail = 1 AND prefix_lang = ' . $prefix_lang . '');
                while ($permission = $query_permission->fetch()) {
                    $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid = ' . $permission['userid'] . '');
                    while ($user = $arr_user->fetch()) {
                        $arr_subemail[$user['email']] = $user['email'];
                    }
                }
                $vip_data['sub_email'] = implode(',', $arr_subemail);
            }

            $vips[$vip_data['userid']] = $vip_data;
            $vips[$vip_data['userid']]['filter'] = array();
            $vips[$vip_data['userid']]['notify'] = array();
            $vips[$vip_data['userid']]['bid'] = array();
            $vips[$vip_data['userid']]['result'] = array();
            $vips[$vip_data['userid']]['result_open'] = array();
            $vips[$vip_data['userid']]['is_vip'] = 1;
        }

        if ($config_bidding['use_reward_points'] and !empty($array_id_user)) {
            $arr_user = $db->query('SELECT t1.userid, t1.email, t2.id as cusid, t2.lastmail, t2.datecreate as from_time FROM nv4_users as t1 INNER JOIN ' . BID_PREFIX_GLOBAL . '_customs_points_email as t2 ON t1.userid= t2.userid WHERE t1.userid IN ( ' . implode(',', $array_id_user) . ') AND t2.prefix_lang = ' . $prefix_lang . '');
            while ($user_data = $arr_user->fetch()) {
                if (!isset($vips[$user_data['userid']]['email'])) {
                    $vips[$user_data['userid']] = $user_data;
                    $vips[$user_data['userid']]['filter'] = [];
                    $vips[$user_data['userid']]['notify'] = '';
                    $vips[$user_data['userid']]['plan'] = [];
                    $vips[$user_data['userid']]['time_send'] = 6;
                    $vips[$user_data['userid']]['sub_email'] = '';
                    $vips[$user_data['userid']]['is_vip'] = 0;
                }
            }
        }
    }

    if (!empty($vips)) {
        // lấy username
        $arr_user = $db->query('SELECT * FROM nv4_users WHERE userid IN ( ' . implode(',', array_keys($vips)) . ')');
        while ($user = $arr_user->fetch()) {
            $vips[$user['userid']]['username'] = $user['username'];
        }

        // Lấy ra tất cả tin đấu thầu trong danh sách gửi đi của tất cả các VIP
        // Lưu nội dung mail vào csdl
        foreach ($vips as $vip_id => $vip) {
            file_put_contents($create_mail_file, 'BEGIN: ' . $vip_id . " " . date("d/m/Y H:i:s") . "\n", FILE_APPEND);
            $arr_bid = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row a INNER JOIN ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_detail b ON a.id=b.id WHERE a.id IN (SELECT bid_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_pro1_id WHERE send_status = 0 AND bid_id>0 AND userid=' . $vip_id . ')');
            while ($bid_row = $query->fetch()) {
                $arr_bid[$bid_row['id']] = $bid_row;
            }

            // Gộp nhóm những mail chung chủ đề lại theo từng VIP
            $array_bid_id = array();
            $query = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_pro1_id WHERE send_status = 0 AND userid=' . $vip_id . ' LIMIT 50');
            while ($bid_data = $query->fetch()) {
                $array_bid_id[$bid_data['id']] = $bid_data['id'];
                if ($bid_data['filter_id'] > 0) {
                    $vip['filter'][$bid_data['filter_id']]['list_bid'][] = $arr_bid[$bid_data['bid_id']];
                    $vip['bid'][] = $bid_data['bid_id'];
                }
                if ($bid_data['result_id'] > 0) {
                    $vip['result'][] = $bid_data['result_id'];
                }
                if ($bid_data['kqmt_id'] > 0) {
                    $vip['result_open'][] = $bid_data['kqmt_id'];
                }
                if ($bid_data['notify_vip'] != '') {
                    $vip['notify'][$bid_data['bid_id']] = $bid_data;
                    $vip['bid'][] = 0;
                }
            }

            // Cập nhật lại lần cuối cùng gửi mail cho các tài khoản VIP, Phần này cần chạy trước beg-----inTransaction để nếu có lỗi thì bỏ qua 1 tài khảo.
            if (sizeof($array_bid_id) < 50) {
                // Nếu 1 lần tổng hợp có > 50 thì không cập nhật ngay last_email để 1 phút sau sẽ chạy tiếp

                if ($vip['is_vip']) {
                    $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs SET last_email = ' . NV_CURRENTTIME . ' WHERE id =' . $vip['cusid'] . ' AND vip = 19');
                } else {
                    // do mua theo bộ lọc nên 1 tài khoản có nhiều row, dẫn tới cần ghi lastmail theo userid
                    $stmt = $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs_points_email SET lastmail=' . NV_CURRENTTIME . ' WHERE userid =' . $vip_id . '');
                }
            } else {
                file_put_contents($create_mail_file, "Con chay nua: " . sizeof($array_bid_id) . " / " . sizeof($arr_bid) . "\n", FILE_APPEND);
            }

            $db->beginTransaction();

            try {
                $data_insert = array();
                $data_insert['addtime'] = NV_CURRENTTIME;
                $data_insert['send_time'] = 0;
                $data_insert['status'] = 0;
                // Gửi mail thông báo mới thầu, kế hoạch chọn nhà thầu, thông báo sơ tuyển nhóm theo từng bộ lọc
                if (!empty($vip['filter'])) {
                    $data_insert['title'] = sprintf($lang_module['mail_title_tbmt'], date($date_format));
                    $data_insert['type'] = 0;
                    $data_insert['vip'] = 19;

                    file_put_contents($create_mail_file, "Begin nv_theme_bidding_mail\n", FILE_APPEND);
                    if ($vip['split_email_filters'] == 1) {
                        //Phân chia email thông báo mời thầu riêng biệt cho từng bộ lọc
                        foreach ($vip['filter'] as $filterId => $filter) {
                            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filterId)->fetch();
                            $title = sprintf($lang_module['mail_title_tbmt_split'], date($date_format)) . (!empty($filterId) ? sprintf($lang_module['with_filter'], $filter_info['title']) : '');
                            $data_insert['content'] = nv_theme_bidding_mail([$filterId => $filter], $vip_id);
                            $data_insert['content'] .= sprintf($lang_module['mail_footer_tbmt'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date($date_format, $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');
                            file_put_contents($create_mail_file, "END nv_theme_bidding_mail filter number $count\n", FILE_APPEND);
                            // Nội dung htm sẽ gửi cho từng khách
                            try {
                                file_put_contents($create_mail_file, "BIGIN INSERT INTO nv4_vi_bidding_mail\n", FILE_APPEND);
                                $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                                $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
                                $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                                $stmt->bindParam(':title', $title, PDO::PARAM_STR);
                                $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                                $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                                $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                                $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                                $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                                $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                                $_mailid = $db->lastInsertId();
                                file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                            } catch (PDOException $e) {
                                print_r('Lỗi thêm mail tin mới vào csdl');
                                file_put_contents($create_mail_file, "ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                                notify_to_slack($create_mail_file, "tbmt_pro1.php ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                                trigger_error($e->getMessage());
                                echo($e->getMessage()); // Remove this line after checks finished
                                break;
                            }
                        }
                    } else {
                        $data_insert['content'] = nv_theme_bidding_mail($vip['filter'], $vip_id);
                        $data_insert['content'] .= sprintf($lang_module['mail_footer_tbmt'], $lang_module['vip' . $data_insert['vip']], NV_SERVER_NAME, nv_date($date_format, $vip['from_time']), $vip['username'], NV_MY_DOMAIN . '/' . $site_lang . '/filters');
                        file_put_contents($create_mail_file, "END nv_theme_bidding_mail\n", FILE_APPEND);
                        // Nội dung htm sẽ gửi cho từng khách
                        try {
                            file_put_contents($create_mail_file, "BIGIN INSERT INTO nv4_vi_bidding_mail\n", FILE_APPEND);
                            $stmt = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                            $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                            $stmt->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                            $stmt->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR);
                            $stmt->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                            $stmt->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                            $stmt->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                            $stmt->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                            $stmt->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                            $stmt->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                            $stmt->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                            $stmt->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                            $stmt->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                            $exc = $stmt->execute();
                            $_mailid = $db->lastInsertId();
                            file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                        } catch (PDOException $e) {
                            print_r('Lỗi thêm mail tin mới vào csdl');
                            file_put_contents($create_mail_file, "ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                            notify_to_slack($create_mail_file, "tbmt_pro1.php ERROR INSERT INTO mail 118: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                            trigger_error($e->getMessage());
                            echo($e->getMessage()); // Remove this line after checks finished
                            break;
                        }
                    }
                }

                if (!empty($vip['notify'])) {
                    $data_insert['title'] = sprintf($lang_module['mail_title_tbmt_update'], date($date_format));
                    $data_insert['type'] = 1;
                    $data_insert['vip'] = 19;
                    $data_insert['content'] = nv_theme_change_mail($vip['notify'], $vip['username']);

                    try {
                        $stmt1 = $db->prepare("INSERT INTO " . BID_PREFIX_GLOBAL . "_mail (userid, main_mail, cc_mail, number_phone, title, content, type, vip, addtime, send_time, status, messageid, reject, bounce, complaint, click, open, failure, prefix_lang) VALUES (:userid, :main_mail, :cc_mail, :number_phone, :title, :content, :type, :vip, :addtime, :send_time, :status, '', '', '', '', '', '', '', :prefix_lang)");
                        $stmt1->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                        $stmt1->bindParam(':main_mail', $vip['email'], PDO::PARAM_STR);
                        $stmt1->bindParam(':cc_mail', $vip['sub_email'], PDO::PARAM_STR, strlen($vip['sub_email']));
                        $stmt1->bindParam(':number_phone', $vip['phone'], PDO::PARAM_STR);
                        $stmt1->bindParam(':title', $data_insert['title'], PDO::PARAM_STR);
                        $stmt1->bindParam(':content', $data_insert['content'], PDO::PARAM_STR, strlen($data_insert['content']));
                        $stmt1->bindParam(':type', $data_insert['type'], PDO::PARAM_INT);
                        $stmt1->bindParam(':vip', $data_insert['vip'], PDO::PARAM_INT);
                        $stmt1->bindParam(':addtime', $data_insert['addtime'], PDO::PARAM_INT);
                        $stmt1->bindParam(':send_time', $data_insert['send_time'], PDO::PARAM_INT);
                        $stmt1->bindParam(':status', $data_insert['status'], PDO::PARAM_INT);
                        $stmt1->bindParam(':prefix_lang', $prefix_lang, PDO::PARAM_INT);
                        $exc1 = $stmt1->execute();

                        $_mailid = $db->lastInsertId();
                        file_put_contents($create_mail_file, "MailID: " . $_mailid . ";\n", FILE_APPEND);
                    } catch (PDOException $e) {
                        file_put_contents($create_mail_file, "ERROR INSERT INTO mail 175: " . $vip_id . "; " . print_r($e, true) . "\n\n", FILE_APPEND);
                        notify_to_slack($create_mail_file, "tbmt_pro1.php ERROR INSERT INTO mail 175: " . $vip_id . "; " . print_r($e, true) . "\n\n");
                    }
                }

                echo "vip_id = " . $vip_id . "\n";

                // Cập nhật lại trạng thái cho bảng tmp những tin đã chuyển qua mail rồi
                if (!empty($array_bid_id)) {
                    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_pro1_id SET send_status = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_bid_id) . ') AND send_status = 0');
                }
                $db->commit();
                file_put_contents($create_mail_file, "number bid = " . sizeof($array_bid_id) . ": " . implode(',', $array_bid_id) . "\n", FILE_APPEND);
                file_put_contents($create_mail_file, "END: " . $vip_id . "; " . date("d/m/Y H:i:s") . "\n\n", FILE_APPEND);
                // print_r($array_bid_id);
            } catch (PDOException $e) {
                file_put_contents($create_mail_file, 'rollBack: ' . $vip_id . "\n\n", FILE_APPEND);
                $db->rollBack();
                echo '<pre>';
                print_r($e);
            }
        }
    }

    unlink(NV_ROOTDIR . '/data/create_mail_tbmt_pro1_' . $prefix_lang . '.txt');
} catch (PDOException $e) {
    file_put_contents($create_mail_file, 'ERROR: ' . print_r($e, true) . "\n\n", FILE_APPEND);
    notify_to_slack($create_mail_file, "tbmt_pro1.php ERROR: " . print_r($e, true) . "\n\n");
}
echo "Đã thực hiện xong trong: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . "\n";

function nv_theme_bidding_mail($array_filter, $vip_id)
{
    global $db, $create_mail_file, $site_lang, $lang_module, $mod_func, $prefix_lang, $date_format, $datetime_format;

    $arr_field = array(
        1 => 'Hàng hóa',
        2 => 'Xây lắp',
        3 => 'Tư vấn',
        4 => 'Phi tư vấn',
        5 => 'Hỗn hợp'
    );

    if (!empty($array_filter)) {
        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        $xtpl->assign('LANG', $lang_module);
        foreach ($array_filter as $filter_id => $arr_list) {
            // Lấy tên bộ lọc
            $filter_info = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE id=' . $filter_id)->fetch();

            // link sửa bộ lọc
            $xtpl->assign('LINK_EDIT', NV_MY_DOMAIN . '/' . $site_lang . '/filters?userid=' . $vip_id . '&amp;id=' . $filter_id);
            $from = $filter_info['time_find'] > 0 ? nv_date($date_format, NV_CURRENTTIME - 86400 * $filter_info['time_find']) : nv_date($date_format, NV_CURRENTTIME - 86400 * 14);
            $filter_info['link_search'] = '';
            $filter_info['link_search_detail'] = NV_MY_DOMAIN . '/' . $site_lang . '/detail?';
            if ($filter_info['key_search'] != '') {
                $filter_info['link_search'] .= '&q=' . $filter_info['key_search'];
            }
            if ($filter_info['key_search2'] != '') {
                $filter_info['link_search'] .= '&q2=' . $filter_info['key_search2'];
            }
            $filter_info['link_search'] .= '&sfrom=' . $from;
            $filter_info['link_search'] .= '&sto=' . nv_date($date_format, NV_CURRENTTIME);
            $filter_info['link_search'] .= '&is_advance=1';
            $filter_info['link_search'] .= '&userid=' . $vip_id;
            $filter_info['link_search'] .= '&vip=' . $filter_info['vip_use'];
            $filter_info['link_search'] .= '&search_type_content=' . $filter_info['search_type'];
            $filter_info['link_search'] .= '&search_one_key=' . $filter_info['search_one_key'];

            if ($filter_info['cat'] > 0) {
                $filter_info['link_search'] .= '&cat=' . $filter_info['cat'];
            }
            if ($filter_info['field'] != '') {
                $array_field = explode(',', $filter_info['field']);
                foreach ($array_field as $field) {
                    $filter_info['link_search'] .= '&field[]=' . $field;
                }
            }
            if ($filter_info['without_key'] != '') {
                $filter_info['link_search'] .= '&without_key=' . $filter_info['without_key'];
            }
            if ($filter_info['type_org'] > 0) {
                $filter_info['link_search'] .= '&type_org=' . $filter_info['type_org'];
            }
            if ($filter_info['money_from'] > 0) {
                $filter_info['link_search'] .= '&money_from=' . number_format($filter_info['money_from'], 0, '.', ',');
            }
            if ($filter_info['money_to'] > 0) {
                $filter_info['link_search'] .= '&money_to=' . number_format($filter_info['money_to'], 0, '.', ',');
            }
            if ($filter_info['price_from'] > 0) {
                $filter_info['link_search'] .= '&price_from=' . number_format($filter_info['price_from'], 0, '.', ',');
            }
            if ($filter_info['price_to'] > 0) {
                $filter_info['link_search'] .= '&price_to=' . number_format($filter_info['price_to'], 0, '.', ',');
            }

            $filter_info['link_search_detail'] = $filter_info['link_search_detail'] . $filter_info['link_search'];
            $xtpl->assign('LINK_FILTER', $filter_info['link_search_detail']);

            // ghi log bộ lọc
            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastemail = ' . NV_CURRENTTIME . ', stat_totalemail = stat_totalemail + ' . 1 . ' WHERE id=' . $filter_id);

            $xtpl->assign('FILTER_NAME', $filter_info['title']);

            if (!empty($arr_list['list_bid'])) {
                if (sizeof($arr_list['list_bid']) > 50) {
                    $xtpl->assign('FILTER_NUMBER', number_format(sizeof($arr_list['list_bid'])));
                    $xtpl->parse('main.filter.number');
                }

                file_put_contents($create_mail_file, 'list_bid: ' . sizeof($arr_list['list_bid']) . "\n", FILE_APPEND);
                $i_break = 0;

                // lọc bỏ tin trùng nhau
                $arr_stbmt = array();
                foreach ($arr_list['list_bid'] as $array_data) {
                    $array_data['so_tbmt'] = explode('-', $array_data['so_tbmt']);
                    if (!isset($arr_stbmt[$array_data['so_tbmt'][0]])) {
                        $arr_stbmt[$array_data['so_tbmt'][0]] = $array_data['so_tbmt'][1];
                    } else {
                        $array_data['so_tbmt'][1] = intval($array_data['so_tbmt'][1]);
                        $old = intval($arr_stbmt[$array_data['so_tbmt'][0]]);
                        if ($old < $array_data['so_tbmt'][1]) {
                            $arr_stbmt[$array_data['so_tbmt'][0]] = '0' . $array_data['so_tbmt'][1];
                        }
                    }
                }
                // hiển thị
                foreach ($arr_list['list_bid'] as $array_data) {
                    $so_tbmt = explode('-', $array_data['so_tbmt']);
                    if (isset($arr_stbmt[$so_tbmt[0]]) && intval($arr_stbmt[$so_tbmt[0]]) == intval($so_tbmt[1])) {
                        ++$i_break;
                        if ($i_break > 50) {
                            break;
                        }
                        $array_data['title'] = $array_data['goi_thau'];
                        $array_data['title_a'] = nv_htmlspecialchars($array_data['goi_thau']);

                        // Bôi vàng những từ trùng với từ khóa tìm kiếm, tìm kiếm tuyệt đối
                        $arr_key = explode(',', $filter_info['key_search']);

                        foreach ($arr_key as $key) {
                            $key = trim($key);
                            $array_data['goi_thau'] = BoldKeywordInStr(strip_tags($array_data['goi_thau']), $key);
                            $array_data['ben_moi_thau'] = strip_tags(BoldKeywordInStr($array_data['ben_moi_thau'], $key));
                        }
                        $array_data['ngay_dang_tai'] = nv_date(($prefix_lang == 1 ? 'H:i m/d/y' : 'H:i d/m/y'), $array_data['ngay_dang_tai']);
                        $tbmt_t = getUrlByLanguage('tbmt');
                        if ($site_lang != 'vi') {
                            $array_data['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                        } else {
                            $array_data['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . $array_data['alias'] . '-' . $array_data['id'] . '.html';
                        }

                        $check = md5($vip_id . $so_tbmt[0]);
                        $array_data['link_follow'] = NV_MY_DOMAIN . '/' . $site_lang . '/bidding/follow?bid_id=' . $array_data['id'] . '&bid_code=' . $so_tbmt[0] . '&vipid=' . $vip_id . '&check=' . $check;

                        $xtpl->assign('DATA', $array_data);

                        if ($array_data['chu_dau_tu'] != '') {
                            $xtpl->parse('main.filter.content.chu_dau_tu');
                        }

                        if ($array_data['ten_du_an'] != '') {
                            $xtpl->parse('main.filter.content.ten_du_an');
                        }

                        // Kiểm tra xem có giá gói thầu k
                        if ($array_data['price'] > 0) {
                            $xtpl->assign('PRICE_FM', number_format($array_data['price'], 0, ',', '.') . ' VND');
                            $xtpl->parse('main.filter.content.price');
                        }

                        if ($array_data['notify_chance_time'] == 1) {
                            $content_chance = $db->query('SELECT content_chance FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_update WHERE bid_id=' . $array_data['id'] . ' ORDER BY addtime DESC')->fetchColumn();
                            $xtpl->assign('CONTENT_CHANCE', $content_chance);
                            $xtpl->parse('main.filter.content.notify_chance_time');
                        } else {
                            // Tạo link theo dõi tin thầu
                            $xtpl->parse('main.filter.content.follow');
                        }

                        if (intval($so_tbmt[1]) > 0) {
                            $xtpl->parse('main.filter.content.notify_old');
                        }

                        if ($array_data['note'] != '') {
                            $xtpl->parse('main.filter.content.note');
                        }

                        $xtpl->parse('main.filter.content');
                    }
                }
            }
            $xtpl->parse('main.filter');
        }
    }
    $xtpl->parse('main');
    return $xtpl->text('main');
}

function nv_theme_change_mail($array_change, $username)
{
    global $db, $mod_func, $site_lang, $prefix_lang, $date_format, $datetime_format;

    if (!empty($array_change)) {

        $xtpl = new XTemplate('email_new_bidding.tpl', NV_ROOTDIR . '/create_mail');
        include NV_ROOTDIR . '/create_mail/' . $site_lang . '.php';
        $xtpl->assign('LANG', $lang_module);
        $array_codetbmt ='';
        foreach ($array_change as $change) {
            $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE id = ' . $change['bid_id'] . '');
            if ($row = $result->fetch()) {
                $tbmt = explode('-', $row['so_tbmt']);
                $array_codetbmt = $tbmt[0];
                $row['title'] = $row['goi_thau'];
                $row['title_a'] = nv_htmlspecialchars($row['title']);
                $row['time'] = $change['chance_time'] > 0 ? nv_date(($prefix_lang == 1 ? 'H:i m/d/Y' : 'H:i d/m/Y'), $change['chance_time']) : '';                $row['content'] = $change['notify_vip'];
                $tbmt_t = getUrlByLanguage('tbmt');
                if ($site_lang != 'vi') {
                    $row['link_view'] = NV_MY_DOMAIN . '/' . $site_lang . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . change_alias($row['title_a']) . '-' . $row['id'] . '.html';
                } else {
                    $row['link_view'] = NV_MY_DOMAIN . '/' . $mod_func['view'] . '/' . $tbmt_t . '/' . change_alias($row['title_a']) . '-' . $row['id'] . '.html';
                }

                $row['link_follow'] = NV_MY_DOMAIN . '/' . $site_lang . '/bidding/follow';
                $xtpl->assign('DATA', $row);
                $xtpl->parse('main.change.loop');
            }
        }
        $xtpl->parse('main.change');
    }

    // chỉ lấy đại diện 1 tin theo dõi
    $mail_footer_follow = '';
    if (!empty($array_codetbmt)) {
        $result = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_follow WHERE bid_code = ' . $db->quote($array_codetbmt) . '');
        if ($row = $result->fetch()) {
            $mail_footer_follow = sprintf($lang_module['mail_footer_follow'], nv_date($date_format, $row['date_follow']), NV_SERVER_NAME, $username);
        }
    }

    $xtpl->parse('main');
    return $xtpl->text('main') . $mail_footer_follow;
}
