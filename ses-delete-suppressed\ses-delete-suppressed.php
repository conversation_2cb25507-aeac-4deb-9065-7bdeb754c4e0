<?php
$json = file_get_contents('/root/ses-delete-suppressed/ses-delete-suppressed.json');

$array_expression = json_decode($json, true);
$content = "#!/bin/sh\n";
$i = 0;
foreach ($array_expression['SuppressedDestinationSummaries'] as $row) {
    $content .= "echo \"delete suppressed email: " . $row['EmailAddress'] . "\"\n";
    $content .= "aws sesv2 delete-suppressed-destination --email-address " . $row['EmailAddress'] . "\n";
    $content .= "sleep 1\n";
    ++ $i;
}

file_put_contents('/root/ses-delete-suppressed/ses-delete-suppressed-email.sh', $content);
die('END');