<?php
namespace Aws\ApiGatewayManagementApi;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AmazonApiGatewayManagementApi** service.
 * @method \Aws\Result deleteConnection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConnectionAsync(array $args = [])
 * @method \Aws\Result getConnection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConnectionAsync(array $args = [])
 * @method \Aws\Result postToConnection(array $args = [])
 * @method \GuzzleHttp\Promise\Promise postToConnectionAsync(array $args = [])
 */
class ApiGatewayManagementApiClient extends AwsClient {}
