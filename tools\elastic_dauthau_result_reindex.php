<?php

// <PERSON>ac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
define('NV_SYSTEM', true);
require NV_ROOTDIR . '/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

$index = NV_LANG_ELASTIC . 'dauthau_result';
$index2 = NV_LANG_ELASTIC . 'dauthau_result_2';
$table = NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result';
$alias = NV_LANG_ELASTIC . 'dauthau_result';

/*
* 1. Tạo index dauthau_result_2
* 2. Mapping cho index này với id_province là text
* 3. Reindex dauthau_result -> dauthau_result_2
* 4. Downtime
* 5. DELETE dauthau_result
* 6. <PERSON><PERSON><PERSON> alias dauthau_result trỏ đến dauthau_result_2
*/

// <PERSON><PERSON>u l<PERSON>y tất cả các cột thì gán $columns= ''
$columns = 'id,code,title,alias,open_time,finish_time,post_time,date_approval,solicitor_id,type_bid_id,type_choose_id,type_choose,type_bid,price_estimate,bid_price,win_price_number,bid_price_number,no_business_licence,project_name,type_data,get_time,time_cancel,reason,step_cancel,investor,investor_id,tender_price,bidder_name,win_price,cat,id_province,plan_code,is_aita,content,content_full,time_open_static,document_approval,reason,num_goods,bidno,elasticsearch';

$hosts = array(
    $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
);
$client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
    ->setHosts($hosts)
    ->setRetries(0)
    ->build();

// Bước 1: Tạo index
echo "\033[32mStep 1:\033[0m Create index \033[34m$index2\033[0m\n";
$res = mapping_index($index2, $table, $columns, force_text_cols: 'id_province');
if (isset($res['index']) and $res['index'] == $index2) {
    echo "Index is created successfully\n";
} else {
    echo "Index creating failed\n";
    exit();
}
$start_time = time();

// Bước 2: Reindex
echo "\033[32mStep 2:\033[0m Reindex \033[34m$index\033[0m to \033[34m$index2\033[0m\n";
$res = es_reindex($index, $index2);
if (isset($res['failures']) && $res['failures'] == []) {
    echo "Index reindexing successfully\n";
} else {
    echo "Index reindexing failed\n";
    exit();
}

// Bước 3: Xóa index
echo "\033[32mStep 3:\033[0m Delete \033[34m$index\033[0m\n";
$res = es_delete_index($index);
if (!empty($res['acknowledged'])) {
    echo "Index is deleted\n";
} else {
    echo "Cannot delete index\n";
}

// Bước 4: Tạo alias
echo "\033[32mStep 4:\033[0m Add alias \033[35m$alias\033[0m point to \033[34m$index2\033[0m\n";
$res = es_create_alias($index2, $alias);
if (!empty($res['acknowledged'])) {
    echo "Alias is successfully created\n";
} else {
    echo "Alias cannot be created\n";
}
$db->exec('UPDATE ' . NV_PREFIXLANG . '_bidding_result SET elasticsearch = 0 WHERE elasticsearch >= ' . $start_time);

$time_run = number_format((microtime(true) - NV_START_TIME), 2, '.', '');
die("\n\nxong: time_run: " . $time_run . "\n");

function mapping_index($index, $table, $columns = '', $force_text_cols = '', $force_int_cols = '') {
    global $db, $client;
    $properties = []; // Xác định kiểu dữ liệu trên elastic cho các cột dữ liệu tự động
    $columns_arr = !empty($columns) ? explode(',', $columns) : [];
    $columns_arr = array_map('trim', $columns_arr);

    $force_text_arr = array_map('trim', (!empty($force_text_cols) ? explode(',', $force_text_cols) : []));
    $force_int_arr = array_map('trim', (!empty($force_int_cols) ? explode(',', $force_int_cols) : []));

    $sql = 'SHOW COLUMNS FROM ' . $table;
    $result = $db->query($sql);
    while ($row = $result->fetch()) {
        if (in_array($row['field'], $columns_arr) or empty($columns_arr)) {
            if (preg_match('/([a-z0-9]+)\(([0-9]+)\)/', $row['type'], $m)) {
                $row['type'] = $m[1];
            }

            // number: https://www.elastic.co/guide/en/elasticsearch/reference/current/sql-data-types.html
            // https://www.elastic.co/guide/en/elasticsearch/reference/current/number.html
            if (in_array($row['field'], $force_text_arr)) {
                $type = 'text';
            } elseif (in_array($row['field'], $force_int_arr)) {
                $type = 'integer';
            } elseif ($row['type'] == 'tinyint' or $row['type'] == 'smallint' or $row['type'] == 'short') {
                $type = 'short';
            } elseif ($row['type'] == 'mediumint' or $row['type'] == 'int' or $row['type'] == 'integer') {
                $type = 'integer';
            } elseif ($row['type'] == 'bigint' or $row['type'] == 'long') {
                $type = 'long';
            } elseif ($row['type'] == 'double') {
                $type = 'double';
            } elseif ($row['type'] == 'float') {
                $type = 'float';
            } elseif ($row['type'] == 'varchar' or $row['type'] == 'text' or $row['type'] == 'mediumtext'  or $row['type'] == 'longtext') {
                $type = 'text';
            } else {
                $type = '';
                die("Chưa xác định kiểu dữ liệu: " . $row['type'] . ", xem thêm tại: // https://www.elastic.co/guide/en/elasticsearch/reference/current/mapping-types.html\n");
            }
            $properties[$row['field']] = [
                'type' => $type
            ];
            if ($type == 'text') {
                $properties[$row['field']]['fields']['keyword'] = [
                    'ignore_above' => 256,
                    'type' => 'keyword'
                ];
            }
        }
    }

    $params = [
        'index' => $index,
        'body' => [
            'settings' => [
                'number_of_shards' => 1,
                'index' => [
                    'max_result_window' => 500000
                ]
            ],
            'mappings' => [
                'properties' => $properties
            ]
        ]
    ];

    $responses = $client->indices()->create($params)->asArray();
    return $responses;
}

function es_reindex ($old_index, $new_index) {
    global $client;
    $params = [
        'body' => [
            "source" => [
                "index" => $old_index
            ],
            "dest" => [
                "index" => $new_index
            ]
        ]
    ];
    $responses = $client->reindex($params)->asArray();
    return $responses;
}

function es_delete_index($index)
{
    global $client;
    $params = [
        'index' => $index,
    ];

    $responses = $client->indices()->delete($params)->asArray();
    return $responses;
}

function es_create_alias($index, $alias)
{
    global $client;

    $params = [];
    $params['body'] = array(
        'actions' => array(
            array(
                'add' => array(
                    'index' => $index,
                    'alias' => $alias
                )
            )
        )
    );
    $response = $client->indices()->updateAliases($params)->asArray();
    return $response;
}

function es_delete_alias($index, $name)
{
    global $client;
    $params = [
        'index' => $index,
        'name' => $name,
    ];
    $responses = $client->indices()->deleteAlias($params)->asArray();
    return $responses;
}
