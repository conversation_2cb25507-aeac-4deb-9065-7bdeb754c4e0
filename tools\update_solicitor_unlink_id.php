<?php

define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$my_index = $request_mode->get('my_index', '');
if (empty($my_index)) {
    die('No Elasticsearch Index');
}

if ($my_index == 'dauthau_result' or $my_index == 'en_dauthau_result') {
    $config_bidding['elas_host'] = $config_bidding['elas_result_host'];
    $config_bidding['elas_port'] = $config_bidding['elas_result_port'];

    $config_bidding['elas_user'] = $config_bidding['elas_result_user'];
    $config_bidding['elas_pass'] = $config_bidding['elas_result_pass'];
}

$hosts = array(
            $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
);

$client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
    ->setHosts($hosts)
    ->setRetries(0)
    ->build();

if (ob_get_level()) {
    ob_end_clean();
}

// Đọc scroll_id từ file
$scrollIdFile = NV_ROOTDIR . '/tools/update_solicitor_unlink_id_scroll.txt';
$scrollTimeout = '1m';
$batchSize = 1000;

if (file_exists($scrollIdFile)) {
    $scrollId = file_get_contents($scrollIdFile);

    // Lấy batch tiếp theo
    $scrollParams = [
        'scroll_id' => $scrollId,
        'scroll' => $scrollTimeout
    ];
    $response = $client->scroll($scrollParams);
    $scrollId = $response['_scroll_id'];
    $hits = $response['hits']['hits'];
    file_put_contents($scrollIdFile, $scrollId);

    if (count($hits) == 0) {
        $params = [
            'scroll_id' => $scrollId
        ];
        $response = $client->clearScroll($params);
        if (isset($response['succeeded']) && $response['succeeded']) {
            echo "Scroll cleared successfully.\n";
        } else {
            echo "Failed to clear scroll.\n";
        }
        unlink($scrollIdFile); // Xóa file nếu không còn tài liệu nào để xử lý
        exit("All documents updated.\n");
    }
} else {
    // Bắt đầu phiên scroll để lấy các tài liệu
    $params = [
        'index' => $my_index,
        'body' => [
            'query' => [
                'match_all' => new \stdClass()
            ],
            '_source' => false, // Chỉ lấy _id
            'sort' => [
                'id' => [
                    'order' => 'asc' // Hoặc 'desc' để sắp xếp theo thứ tự giảm dần
                ]
            ]
        ],
        'scroll' => $scrollTimeout,
        'size' => $batchSize
    ];
    $response = $client->search($params);
    $scrollId = $response['_scroll_id'];
    $hits = $response['hits']['hits'];

    if (count($hits) == 0) {
        exit("No documents to update.\n");
    }

    file_put_contents($scrollIdFile, $scrollId);
}

if (count($hits) > 0) {
    // Tạo các hành động cập nhật cho Bulk API
    $last_id = 0;
    $bulkParams = ['body' => []];
    foreach ($hits as $hit) {
        $last_id = $hit['_id'];
        $bulkParams['body'][] = [
            'update' => [
                '_index' => $my_index,
                '_id' => $hit['_id']
            ]
        ];
        $bulkParams['body'][] = [
            'doc' => [
                'solicitor_unlink_id' => 0
            ]
        ];
    }
    echo $my_index . "\t" . number_format($last_id) . "\n";

    // Thực hiện cập nhật hàng loạt
    $response = $client->bulk($bulkParams);
    unset($response);
}

// Giải phóng bộ nhớ sau mỗi batch
unset($bulkParams);
unset($hits);
