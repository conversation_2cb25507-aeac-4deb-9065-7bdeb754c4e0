<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC.
 * All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */

/*
 * Thuật toán như sau:
 * - Bảng nv4_vi_bidding_logs_pro có trường
 * - status: Trạng thái bằng 0 là đang chạy, nếu truy cập lại thì tiếp tục chạy.
 * - user: ID của users đã chạy đến, Mỗi lần tìm kiếm chỉ khoảng 2 tài khoản để test. (Sau đó có thể nâng lên 50 khi số lựong nhiều)
 * - Bảng nv4_vi_bidding_logs_pro được thêm mới khi không tìm thấy status = 0 và có thêm bài đăng.
 * - <PERSON>hi tìm kiếm theo từng tài khỏan, sẽ tì<PERSON> lần luợt bộ lọc của từng người, nếu tìm thấy thì lưu vào bảng nv4_vi_bidding_mail để chương trình send_mail.php lấy ra để gửi.
 */
$arr_field = array(
    1 => 'Hàng hóa',
    2 => 'Xây lắp',
    3 => 'Tư vấn',
    4 => 'Phi tư vấn',
    5 => 'Hỗn hợp'
);
$filters_mail_tbmt_log = NV_ROOTDIR . '/data/filters_mail_tbmt_pro1_log_' . $prefix_lang . '_' . date('Ymd') . '.txt';
$new_loop = 0;
$arr_id_vip = [];
$arr_vip = [];
// Bước 1: Kiểm tra có bảng logs nào đang chạy không, nếu không thì bắt đầu chạy mới, nếu có thì tiếp tục chạy đến userid tiếp theo
try {
    $logs_info = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_pro WHERE status=0 limit 1')->fetch();
} catch (PDOException $e) {
    print_r('Lỗi select logs');
    trigger_error($e->getMessage());
    die($e->getMessage()); // Remove this line after checks finished
}

if (!empty($logs_info)) {
    $start_id = $logs_info['from_id'];
    $end_id = $logs_info['to_id'];
    $last_userid = $logs_info['userid'];
} else {
    // Nếu không có phần nào đang chạy thì kiểm tra xem có tin đấu thầu mới không. Nếu có tin mới thì chạy vòng lặp mới, nếu không thì kết thúc
    $last_logs = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_pro ORDER BY id DESC limit 1')->fetch();
    $info = $db->query('SELECT max(id) as to_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row')->fetch();
    if (empty($last_logs)) {
        $start_id = 0; // Chạy lần đầu
        $end_id = $info['to_id'];
        $last_userid = 0;
        $new_loop = 1;
    } else {
        if ($last_logs['to_id'] < $info['to_id']) {
            // Nếu có tin thầu mới hoặc kế hoạch chọn nhà thầu mới, thông báo mời sơ tuyển mới thì lấy ra các bài mới để chạy vòng lặp mới
            $last_userid = 0;
            $start_id = $last_logs['to_id']; // Lấy id tin mời thầu của lần cuối cùng chạy
            $end_id = $info['to_id']; // Lấy id mời thầu lớn nhất của dữ liệu hiện có
            $new_loop = 1;
        } else {
            // Nếu không có dữ liệu mới để chạy thì dừng lại ngay
            file_put_contents($filters_mail_tbmt_log, "Lỗi select logs L71: Không có tin mới trong csdl. Hãy chạy tiếp khi có bài đăng mới\n", FILE_APPEND);
            die('Không có tin mới');
        }
    }
}

$time = time();
$start_time = $time;
if ($new_loop) {
    try {
        // thêm logs mới vào csdl
        $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_pro (from_id, to_id, from_time, to_time, total_time, userid, status) VALUES (:from_id, :to_id, :from_time, :to_time, 0, 0, 0)');
        $stmt->bindParam(':from_id', $start_id, PDO::PARAM_INT);
        $stmt->bindParam(':to_id', $end_id, PDO::PARAM_INT);
        $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
        $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
        $exc = $stmt->execute();
        $logs_id = $db->lastInsertId();
        $run_time = 0;
    } catch (PDOException $e) {
        file_put_contents($filters_mail_tbmt_log, "Lỗi thêm logs mới vào csdl L92: " . print_r($e, true) . "\n", FILE_APPEND);
        die('Lỗi thêm logs mới');
    }
} else {
    $logs_id = $logs_info['id'];
    $run_time = $logs_info['total_time'];
}

// Bước 2: Lấy ra ds các VIP mỗi lần 50 người
// 2.1 Lấy danh sách các VIP còn hạn sử dụng
$number_user_scan_all = 100;
$time_start_thisday = mktime(date('H'), date('i'), date('s'), date('n'), date('j'), date('Y'));
$query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id >' . $last_userid . ' AND vip = 19 AND prefix_lang = ' . $prefix_lang . ' ORDER BY user_id ASC limit ' . $number_user_scan_all);
$number_user_scan = 0;
$user_id_end = 0;
while ($vip_info = $query_vip->fetch()) {
    ++$number_user_scan;
    $user_id_end = $vip_info['user_id'];
    $arr_vip[$vip_info['user_id']]['from_time'] = $vip_info['from_time'];
    $arr_vip[$vip_info['user_id']]['to_time'] = $vip_info['end_time'];
    $arr_vip[$vip_info['user_id']]['filter'] = [];
    $arr_vip[$vip_info['user_id']]['vip'][$vip_info['vip']] = $vip_info['vip'];

    $arr_id_vip[$vip_info['user_id']] = $vip_info['user_id'];
}

if ($config_bidding['use_reward_points']) {
    // 2.2 Lấy các user đăng ký gửi mail bằng điểm
    $array_id_user = $array_id_customs_points_email = [];
    // lấy thêm các user mà lớn hơn $user_id_end của vòng chạy cuối cùng
    if ($new_loop) {
        $last_logs['userid'] = (empty($last_logs['userid'])) ? 0 : $last_logs['userid'];
        $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_logs['userid'] . ' AND vip = 19 AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC');
        while ($points_email_info = $query_user->fetch()) {
            $arr_vip[$points_email_info['userid']]['vip'][$points_email_info['vip']] = $points_email_info['vip'];
            $arr_vip[$points_email_info['userid']]['to_time'] = $points_email_info['dateexpired'];
            $arr_vip[$points_email_info['userid']]['filter'][$points_email_info['filterid']] = [];
            $arr_vip[$points_email_info['userid']]['bidding'] = [];

            $array_id_user[$points_email_info['userid']] = $points_email_info['userid'];
            $array_id_customs_points_email[$points_email_info['userid']] = $points_email_info['id'];
        }
    }

    $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_email WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_userid . ' AND userid < ' . $user_id_end . ' AND vip = 19 AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC limit ' . $number_user_scan_all);
    while ($points_email_info = $query_user->fetch()) {
        $arr_vip[$points_email_info['userid']]['vip'][$points_email_info['vip']] = $points_email_info['vip'];
        $arr_vip[$points_email_info['userid']]['to_time'] = $points_email_info['dateexpired'];
        $arr_vip[$points_email_info['userid']]['filter'][$points_email_info['filterid']] = [];
        $arr_vip[$points_email_info['userid']]['bidding'] = [];

        $array_id_user[$points_email_info['userid']] = $points_email_info['userid'];
        $array_id_customs_points_email[$points_email_info['userid']] = $points_email_info['id'];
    }

    // 2.3 Lấy các user đăng ký xuất excel bằng điểm
    // lấy thêm các user mà lớn hơn $user_id_end của vòng chạy cuối cùng
    $array_id_customs_points_excel = [];
    if ($new_loop) {
        $last_logs['userid'] = (empty($last_logs['userid'])) ? 0 : $last_logs['userid'];
        $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_excel WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_logs['userid'] . ' AND vip = 19 AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC');
        while ($points_excel_info = $query_user->fetch()) {
            $arr_vip[$points_excel_info['userid']]['vip'][$points_excel_info['vip']] = $points_excel_info['vip'];
            $arr_vip[$points_excel_info['userid']]['to_time'] = $points_excel_info['dateexpired'];
            $arr_vip[$points_excel_info['userid']]['filter'][$points_excel_info['filterid']] = [];
            $arr_vip[$points_excel_info['userid']]['bidding'] = [];

            $array_id_user[$points_excel_info['userid']] = $points_excel_info['userid'];
            $array_id_customs_points_excel[$points_excel_info['userid']] = $points_excel_info['id'];
        }
    }

    // các user đăng ký xuất excel bằng điểm được lấy theo limit 50 và theo giới hạn các tk trong phạm vi của nhóm vip trên
    $query_user = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_customs_points_excel WHERE dateexpired >= ' . $time_start_thisday . ' AND userid >' . $last_userid . ' AND userid < ' . $user_id_end . ' AND vip = 19 AND prefix_lang = ' . $prefix_lang . ' ORDER BY userid ASC limit ' . $number_user_scan_all);
    while ($points_excel_info = $query_user->fetch()) {
        $arr_vip[$points_excel_info['userid']]['vip'][$points_excel_info['vip']] = $points_excel_info['vip'];
        $arr_vip[$points_excel_info['userid']]['to_time'] = $points_excel_info['dateexpired'];
        $arr_vip[$points_excel_info['userid']]['filter'][$points_excel_info['filterid']] = [];
        $arr_vip[$points_excel_info['userid']]['bidding'] = [];

        $array_id_user[$points_excel_info['userid']] = $points_excel_info['userid'];
        $array_id_customs_points_excel[$points_excel_info['userid']] = $points_excel_info['id'];
    }

    ksort($arr_vip);
    $arr_id_all = array_merge($arr_id_vip, $array_id_user);
} else {
    $arr_id_all = $arr_id_vip;
}

// Bước 3: Lấy ra ds các bộ lọc của VIP đc chọn
if (!empty($arr_id_all)) {
    // thông tin user
    $query_user = $db->query('SELECT userid, email FROM ' . NV_USERS_GLOBALTABLE . ' WHERE userid IN (' . implode(',', $arr_id_all) . ')');
    while ($user = $query_user->fetch()) {
        $user_all[$user['userid']] = $user['email'];
    }

    $query_filter = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE userid IN (' . implode(',', $arr_id_all) . ') AND (status=1 OR send_excel = 1) AND vip_use=19 AND prefix_lang = ' . $prefix_lang . '');
    while ($result = $query_filter->fetch()) {
        if (in_array($result['vip_use'], $arr_vip[$result['userid']]['vip'])) {
            $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
            $arr_vip[$result['userid']]['email'] = isset($user_all[$result['userid']]) ? $user_all[$result['userid']] : '';
        } else if (isset($arr_vip[$result['userid']]['filter'][$result['id']])) {
            $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
            $arr_vip[$result['userid']]['email'] = isset($user_all[$result['userid']]) ? $user_all[$result['userid']] : '';
        }
    }
}

// Bước 4: Lấy ra các tin trong ds tin mới mà thỏa mãn bộ lọc tương ứng với từng VIP để thêm vào bảng _tmp
if (empty($arr_vip)) {
    // Nếu không lấy được VIP nào thì coi như đã chạy xong một vòng và update lại status là đã chạy xong
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $change = $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_pro SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_tbmt_log, "Đã quét xong một lượt các vip. L172 Tổng thời gian cho một lượt:" . $total_time . "\n", FILE_APPEND);
    die();
} else {
    $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_pro SET userid =' . $user_id_end . ' WHERE id =' . $logs_id);
    $db->beginTransaction();
    try {
        foreach ($arr_vip as $vip_id => $vip) {
            echo "Bắt đầu quét:" . $vip_id . "\n";
            file_put_contents($filters_mail_tbmt_log, "L175 Bắt đầu quét:" . $vip_id . "\n", FILE_APPEND);
            // Lấy ra danh sách các tin mời thầu, KHLCNT, TBMST thỏa mãn điều kiện tìm kiếm từ các bộ lọc
            if (!empty($vip['filter'])) {
                foreach ($vip['filter'] as $filter) {
                    $check_request = 0;
                    if (!empty($filter)) {
                        // Từ bộ lọc chọn ra điều kiện lọc where tương ứng
                        $where = 'id>' . $start_id . ' AND id<=' . $end_id; // Điều kiện lọc tin mời thầu

                        if ($filter['time_find'] > 0) {
                            $time_find = mktime(0, 0, 0, date('n'), date('j'), date('Y')) - 86400 * $filter['time_find'];
                            $where .= ($filter['type'] == 0) ? ' AND ngay_dang_tai>=' . $time_find : ' AND den_ngay>=' . $time_find;
                        }
                        if ($filter['cat'] > 0) {
                            $where .= $filter['cat'] == 1 ? " AND type_bid=1" : " AND type_bid=0";
                        }

                        if ($filter['type_org'] == 2) {
                            $where .= " AND type_org=1";
                        }
                        // Tìm kiếm theo phân mục
                        if (!empty($filter['phanmuc'])) {
                            $where .= ' AND (';
                            $filter['phanmuc'] = explode(',', $filter['phanmuc']);
                            $num = 0;
                            foreach ($filter['phanmuc'] as $value) {
                                if ($num == 0) {
                                    $where .= ' FIND_IN_SET(' . $value . ', phanmuc)';
                                } else {
                                    $where .= ' OR FIND_IN_SET(' . $value . ', phanmuc)';
                                }
                                ++$num;
                            }
                            $where .= ')';
                        }

                        // Tìm kiếm theo tỉnh thành
                        if (!empty($filter['idprovince'])) {
                            $filter['idprovince'] = explode(',', $filter['idprovince']);
                            foreach ($filter['idprovince'] as $key_idp) {
                                if ($key_idp == 0) {
                                    $where .= ' AND ( province_id = 0)';
                                } else {
                                    $where .= ' AND FIND_IN_SET ( (' . implode(',', $key_idp) . ') , province_id)';
                                }
                            }
                        }

                        if ($filter['money_from'] > 0) {
                            // tìm theo số tiền đảm bảo từ
                            $where .= " AND money_bid >= " . $filter['money_from'];
                        }
                        if ($filter['money_to'] > 0) {
                            // tìm theo số tiền đảm bảo tới
                            $where .= " AND money_bid <= " . $filter['money_to'];
                        }

                        if ($filter['price_from'] > 0) {
                            // tìm theo giá mời thầu
                            $where .= " AND price >= " . $filter['price_from'];
                        }
                        if ($filter['price_to'] > 0) {
                            // tìm theo giá mời thầu
                            $where .= " AND price <= " . $filter['price_to'];
                        }

                        if ($filter['field'] != '') {
                            $where .= " AND linh_vuc_thong_bao IN (" . $filter['field'] . ")";
                        }

                        if ($filter['type_choose_id']) {
                            $where .= " AND type_choose_id = " . $filter['type_choose_id'];
                        }

                        if ($filter['phuongthuc'] != '') {
                            $where .= " AND phuong_thuc_num IN (" . $filter['phuongthuc'] . ")";
                        }
                        $where .= " AND pham_vi != 1";

                        if (!empty($filter['without_key'])) {
                            // Tìm theo từ khóa loại trừ
                            $arr_key = explode(',', $filter['without_key']);
                            foreach ($arr_key as $key) {
                                if ($filter['search_type'] == 1) {
                                    $key = trim($key);
                                    $where .= " AND content_full NOT LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
                                } else {
                                    $key = str_replace('-', ' ', change_alias($key));
                                    $key = trim($key);
                                    $where .= " AND content NOT LIKE " . $db->quote('%' . $key . '%');
                                }
                            }
                        }

                        if (!empty($filter['key_search2'])) {
                            // Tìm theo từ khóa cùng
                            $arr_key = explode(',', $filter['key_search2']);
                            $operators = !empty($filter['search_one_key']) ? ' OR ' : ' AND ';
                            foreach ($arr_key as $key) {
                                if ($filter['search_type'] == 1) {
                                    $key = trim($key);
                                    $where .= $operators . "content_full LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
                                } else {
                                    $key = str_replace('-', ' ', change_alias($key));
                                    $key = trim($key);
                                    $where .= $operators . "content LIKE " . $db->quote('%' . $key . '%');
                                }
                            }
                        }

                        if (!empty($filter['key_search'])) {
                            $where .= ' AND (';
                            $num = 0;
                            $arr_key = explode(',', $filter['key_search']);
                            foreach ($arr_key as $key) {
                                if ($filter['search_type'] == 1) {
                                    $key = trim($key);
                                    if ($num == 0) {
                                        $where .= "content_full LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
                                    } else {
                                        $where .= " OR content_full LIKE " . $db->quote('%' . $key . '%') . ' COLLATE utf8_bin';
                                    }
                                } else {
                                    $key = str_replace('-', ' ', change_alias($key));
                                    $key = trim($key);
                                    if ($num == 0) {
                                        $where .= "content LIKE " . $db->quote('%' . $key . '%');
                                    } else {
                                        $where .= " OR content LIKE " . $db->quote('%' . $key . '%');
                                    }
                                }
                                $num++;
                            }
                            $where .= ')';
                        }

                        // Select tin đầu thầu thỏa mãn từng bộ lọc
                        $sql = 'SELECT id from ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_row WHERE ' . $where;
                        try {
                            $result = $db->query($sql);
                        } catch (PDOException $e) {
                            print_r('Lỗi chọn tin thầu thỏa mãn bộ lọc');
                            echo '<pre>';
                            print_r($e);
                            echo '</pre>';
                            die($sql); // Remove this line after checks finished
                        }
                        $array_id = [];
                        $num_row = 0;
                        while ($bidding = $result->fetch()) {
                            $num_row++;
                            if ($filter['vip_use'] == 19) {
                                // Nếu tìm ra tin thầu thỏa mãn điều kiện thì thêm bảng tmp
                                $stmt = $db->prepare('INSERT IGNORE INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_pro1_id(userid, filter_id, bid_id, notify_vip, send_status, addtime) VALUES (:userid, :filter_id, :bid_id, :notify_vip , :send_status, :addtime)');

                                $notify = '';
                                $time = NV_CURRENTTIME;
                                $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':bid_id', $bidding['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':notify_vip', $notify, PDO::PARAM_STR);
                                $stmt->bindValue(':send_status', ($filter['status'] == 0 ? -100 : 0), PDO::PARAM_INT);
                                $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                $exc = $stmt->execute();
                            }

                            // Thêm vào bảng request nếu khách hàng yêu cầu gửi excel
                            if ($filter['send_excel'] && $filter['vip_use'] == 19) { //
                                if (empty($check_request)) {
                                    $check_request = $db->query('SELECT id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_request WHERE status = 0 AND type_request = 0 AND filter_type = 1 AND filter_id = ' . $filter['id'])->fetch();
                                }

                                $email = '';
                                if (empty($check_request)) {
                                    // thêm vào bảng request
                                    // mức độ ưu tiên
                                    if (empty($priority)) {
                                        $priority = $db->query('SELECT count(*) FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_request WHERE userid=' . $vip_id . ' AND addtime >= ' . mktime(0, 0, 0, date('m', NV_CURRENTTIME), date('d', NV_CURRENTTIME), date('Y', NV_CURRENTTIME)))->fetchColumn();
                                    } else {
                                        $priority++;
                                    }

                                    // các điều kiện lọc
                                    $array_param = [
                                        'title' => $filter['title'],
                                        'vip_use' => $filter['vip_use'],
                                        'key' => $filter['key_search'],
                                        'key2' => $filter['key_search2'],
                                        'search_type_content' => $filter['search_type'],
                                        'without_key' => $filter['without_key'],
                                        'time_find' => $filter['time_find'],
                                        'invest_from' => $filter['invest_from'],
                                        'invest_to' => $filter['invest_to'],
                                        'price_plan_from' => $filter['price_plan_from'],
                                        'price_plan_to' => $filter['price_plan_to'],
                                        'par_search' => $filter['par_search'],
                                        'goods_search' => $filter['goods_search'],
                                        'searchkind' => $filter['searchkind']
                                    ];

                                    $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_request (userid, email, request_params, addtime, send_mail, priority, filter_id, filter_type) VALUES (:userid, :email, :request_params, :addtime, :send_mail, :priority, :filter_id, :filter_type)');

                                    $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                    $email = !empty($vip['email']) ? $vip['email'] : '';
                                    $stmt->bindParam(':email', $email, PDO::PARAM_STR);
                                    $stmt->bindValue(':request_params', json_encode($array_param, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT), PDO::PARAM_STR);
                                    $stmt->bindValue(':addtime', NV_CURRENTTIME, PDO::PARAM_INT);
                                    $stmt->bindValue(':send_mail', 1, PDO::PARAM_INT);
                                    $stmt->bindValue(':priority', !empty($priority) ? $priority : 0, PDO::PARAM_INT);
                                    $stmt->bindValue(':filter_id', $filter['id'], PDO::PARAM_INT);
                                    $stmt->bindValue(':filter_type', 1, PDO::PARAM_INT);

                                    $exc = $stmt->execute();
                                    if ($exc) {
                                        $check_request = $db->lastInsertId();
                                    }
                                }
                            }
                        }

                        // ghi log bộ lọc
                        if ($num_row > 0) {
                            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastresult = ' . NV_CURRENTTIME . ', stat_totalresult = stat_totalresult + ' . $num_row . ' WHERE id=' . $filter['id']);
                        }
                    }
                }
            }
            // ghi lại logs cho quá trình tìm kiếm tin cho mỗi vip
            $time = NV_CURRENTTIME;
            $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_pro SET to_time = :to_time, userid = :userid WHERE id =' . $logs_id);
            $_vip_id = ($vip_id >= $user_id_end) ? $user_id_end : $vip_id;
            $stmt->bindParam(':userid', $_vip_id, PDO::PARAM_INT);
            $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
            $exc = $stmt->execute();
        }
        $db->commit();
    } catch (PDOException $e) {
        file_put_contents($filters_mail_tbmt_log, "Lỗi INSERT  L401:\n" . print_r($e, true) . "\n", FILE_APPEND);
        $db->rollBack();
        notify_to_slack($filters_mail_tbmt_log, "filters_mail_tbmt_mysql_pro1.php Lỗi INSERT  L401:\n" . print_r($e, true) . "\n");
        die(); // Remove this line after checks finished
    }
    // Sau khi chạy xong một vòng 50 VIP thì thông báo chạy xong và thời gian chạy cho một vòng
    $time = time();
    $this_time = $time - NV_CURRENTTIME;
    $total_time = intval($run_time + $this_time);
    try {
        if ($number_user_scan < $number_user_scan_all) {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_pro SET to_time = ' . $time . ', total_time = ' . $total_time . ', status=1 where id =' . $logs_id);
        } else {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_logs_pro SET to_time = ' . $time . ', total_time = ' . $total_time . ' where id =' . $logs_id);
        }
        if (sizeof($arr_id_vip)) {
            $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs  SET filters_mail = ' . NV_CURRENTTIME . ' WHERE user_id IN (' . implode(',', $arr_id_vip) . ') AND vip=19 AND prefix_lang = ' . $prefix_lang . '');
        }
        if ($config_bidding['use_reward_points']) {
            if (sizeof($array_id_customs_points_email) > 0) {
                $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs_points_email  SET timefilter = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_id_customs_points_email) . ') AND prefix_lang = ' . $prefix_lang . '');
            }

            if (sizeof($array_id_customs_points_excel) > 0) {
                $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs_points_excel  SET timefilter = ' . NV_CURRENTTIME . ' WHERE id IN (' . implode(',', $array_id_customs_points_excel) . ') AND prefix_lang = ' . $prefix_lang . '');
            }
        }
    } catch (PDOException $e) {
        file_put_contents($filters_mail_tbmt_log, "Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP  L415:\n" . print_r($e, true) . "\n", FILE_APPEND);
        notify_to_slack($filters_mail_tbmt_log, "filters_mail_tbmt_mysql_pro1.php Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP  L415:\n" . print_r($e, true) . "\n");
        die();
    }

    $note = 'L420 Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
    file_put_contents($filters_mail_tbmt_log, $note . "\n", FILE_APPEND);
    die($note . "\n");
}
