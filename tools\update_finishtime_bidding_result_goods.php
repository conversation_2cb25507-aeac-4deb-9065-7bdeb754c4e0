<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
$hosts = array(
    $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
);

$client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
    ->setHosts($hosts)
    ->setRetries(0)
    ->build();

$my_index = 'dauthau_result_goods';
try {
    // Get mappings
    $params = [
        'index' => $my_index
    ];
    $response = $client->indices()->getMapping($params);
    $properties = $response[$my_index]['mappings']['properties'];
    $properties['finishtime']['type'] = 'long';
    $properties['finishtime']['null_value'] = '0';

    $params = [
        'index' => $my_index,
        'body' => [
            'properties' => $properties
        ]
    ];

    // Update the index mapping
    $client->indices()->putMapping($params);

    // Cập nhật giá trị mặc định = 0
    $params = [
        'index' => $my_index,
        'body' => [
            'query' => [
                'match_all' => (object)[]
            ],
            'script' => [
                'source' => "ctx._source.finishtime = 0"
            ]
        ]
    ];

    $response = $client->updateByQuery($params);
    print_r($response);
    die('đã thực hiện xong');
} catch (Exception $e) {

}

die('lỗi gì đó');
