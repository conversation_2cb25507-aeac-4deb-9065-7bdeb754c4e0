<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 * Công cụ xóa doanh nghiệp nước ngoài khỏi bảng vi_active_profile_dtnet_mkt
 */

if (isset($_GET['response_headers_detect'])) {
    exit(0);
}

define('NV_SYSTEM', true);
define('NV_IS_CONSOLE', true);
// <PERSON><PERSON><PERSON> đ<PERSON>nh thư mục gốc của site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

set_time_limit(0);

$id_log_file = NV_ROOTDIR . '/tools_2025/last_foreign_company_id.log';
if (file_exists($id_log_file)) {
    list($last_id, $id_max) = explode('_', file_get_contents($id_log_file));
    $last_id = intval($last_id);
    $id_max = intval($id_max);
} else {
    $last_id = $db->query("SELECT MIN(id) FROM " . BUSINESS_PREFIX_GLOBAL . "_addinfo")->fetchColumn();
    $id_max = $db->query("SELECT MAX(id) FROM " . BUSINESS_PREFIX_GLOBAL . "_addinfo")->fetchColumn();

    if (empty($last_id)) {
        $last_id = 0;
    }

    file_put_contents($id_log_file, $last_id . '_' . $id_max);
}

echo "========= BẮT ĐẦU XÓA DOANH NGHIỆP NƯỚC NGOÀI =========\n";
echo 'Bắt đầu từ ID: ' . $last_id . " - ID max: " . $id_max . "\n";

$next_id = $last_id + 100;
if ($next_id > $id_max) {
    $next_id = $id_max;
}

$count_foreign_companies = 0;

$sql = "SELECT id, so_dkkd, tax_nation FROM " . BUSINESS_PREFIX_GLOBAL . "_addinfo WHERE id > " . $last_id . " AND id <= " . $next_id . " ORDER BY id ASC";
$result = $db->query($sql);

$foreign_business_codes = [];
$newid = $last_id;
$numRows = 0;
$company_ids = [];

while ($row = $result->fetch()) {
    $numRows++;
    $newid = $row['id'];

    if (!empty($row['tax_nation']) && $row['tax_nation'] != 'VN') {
        $foreign_business_codes[] = $row['so_dkkd'];
    }
}

if (!empty($foreign_business_codes)) {
    $quoted_codes = [];
    foreach ($foreign_business_codes as $code) {
        $quoted_codes[] = $db->quote($code);
    }
    $foreign_business_codes_str = implode(',', $quoted_codes);

    $query2 = $db->query("SELECT id FROM " . $config['prefix'] . "_vi_active_profile_dtnet_mkt WHERE contact_id IN (" . $foreign_business_codes_str . ") ORDER BY id ASC");

    while ($row = $query2->fetch()) {
        $company_ids[] = $row['id'];
        $count_foreign_companies++;
    }
}

echo "Tìm thấy " . $count_foreign_companies . " doanh nghiệp nước ngoài trong bảng vi_active_profile_dtnet_mkt\n";

if (!empty($company_ids)) {
    $company_ids_str = implode(',', $company_ids);

    $db->query("UPDATE " . $config['prefix'] . "_vi_active_dtnet_mail SET bidding_mail_id = -17 WHERE bidding_mail_id IN (" . $company_ids_str . ")");
    echo "- Đã cập nhật trạng thái trong bảng vi_active_dtnet_mail cho " . count($company_ids) . " doanh nghiệp\n";

    $db->query("DELETE FROM " . $config['prefix'] . "_vi_active_profile_dtnet_mkt WHERE id IN (" . $company_ids_str . ")");
    echo "- Đã xóa " . count($company_ids) . " doanh nghiệp khỏi bảng vi_active_profile_dtnet_mkt\n";
}

file_put_contents($id_log_file, $newid . '_' . $id_max);

if ($numRows == 0) {
    if ($next_id < $id_max) {
        file_put_contents($id_log_file, $next_id . '_' . $id_max);
        echo "Không tìm thấy doanh nghiệp nước ngoài nào trong khoảng ID từ " . $last_id . " đến " . $next_id . ". Tiếp tục với ID tiếp theo.\n";
    } else {
        echo "Đã xử lý hết tất cả doanh nghiệp nước ngoài.\n";
        exit(1);
    }
}

echo "\n=================================\n";
echo "Đã xử lý " . $numRows . " dòng (ID: " . $last_id . " đến " . $newid . ")\n";
echo "Tổng số doanh nghiệp nước ngoài đã xử lý: " . $count_foreign_companies . "\n";
echo "=================================\n";

echo "\nThời gian thực hiện: " . number_format((microtime(true) - NV_START_TIME), 3, '.', '') . " giây\n";
echo "-------------------------------------------\n";
