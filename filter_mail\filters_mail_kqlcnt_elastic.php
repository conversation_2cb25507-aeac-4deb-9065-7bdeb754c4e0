<?php

/**
 * @Project NUKEVIET 4.x
 * <AUTHOR> <<EMAIL>>
 * @Copyright (C) 2014 VINADES.,JSC. All rights reserved
 * @License GNU/GPL version 2 or any later version
 * @Createdate 31/05/2010, 00:36
 */
$waitTimeoutInSeconds = 2;
$filters_mail_kqlcnt_log = NV_ROOTDIR . '/data/filters_mail_kqlcnt_log_' . $prefix_lang . '_' . date('Ymd') . '.txt';

if ($fp = fsockopen($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
    // It worked
    $elastic_online = 1;
} else {
    // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
    file_put_contents($filters_mail_kqlcnt_log, "Server Elasticsearch didn't work.\n", FILE_APPEND);
    die();
}
fclose($fp);
if ($elastic_online) {
    $hosts = array(
        $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
    );
    $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
        ->setHosts($hosts)
        ->setRetries(0)
        ->build();
}
/*
 * Thuật toán như sau:
 * - Bảng nv4_vi_bidding_kqlcnt_logs có trường
 * - status: Trạng thái bằng 0 là đang chạy, nếu truy cập lại thì tiếp tục chạy.
 * - user: ID của users đã chạy đến, Mỗi lần tìm kiếm chỉ khoảng 2 tài khoản để test. (Sau đó có thể nâng lên 50 khi số lựong nhiều)
 * - Bảng nv4_vi_bidding_kqlcnt_logs được thêm mới khi không tìm thấy status = 0 và có thêm bài đăng.
 * - Khi tìm kiếm theo từng tài khỏan, sẽ tìm lần luợt bộ lọc của từng người, nếu tìm thấy thì lưu vào bảng nv4_vi_bidding_mail để chương trình send_mail.php lấy ra để gửi.
 */

/*
 * Dùng ?testuserid=USERID để kiểm tra trực tiếp đối với thành viên
 * Nếu testuserid hệ thống sẽ bỏ qua ghi dữ liệu vào CSLD chỉ hiển thị kết quả
 */

$testUserid = 0;

if (!$testUserid) {
    $new_loop = 0;
    // Bước 1: Kiểm tra có bảng logs nào đang chạy không, nếu không thì bắt đầu chạy mới, nếu có thì tiếp tục chạy đến userid tiếp theo
    $logs_info = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs WHERE status=0 ORDER BY id DESC limit 1')->fetch();

    if (!empty($logs_info)) {
        echo "\n\nLogid: " . $logs_info['id'] . " date: " . nv_date('H:i:s d/m/Y', $logs_info['from_time']) . "\n";
        $start_id = $logs_info['from_id'];
        $end_id = $logs_info['to_id'];
        $last_userid = $logs_info['userid'];
    } else {
        // Nếu không có phần nào đang chạy thì kiểm tra xem có tin đấu thầu mới không. Nếu có tin mới thì chạy vòng lặp mới, nếu không thì kết thúc
        $last_logs = $db->query('SELECT * FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs ORDER BY id DESC limit 1')->fetch();
        // $info = $db->query('SELECT max(id) as to_id FROM nv4_vi_bidding_result')->fetch();
        $array_query_elastic = array();
        $array_query_elastic['size'] = 1;
        $array_query_elastic['sort'] = [
            [
                "id" => [
                    "order" => "desc"
                ]
            ]
        ];
        $array_query_elastic['_source'] = array(
            'id'
        );
        $params = array();
        $params['index'] = NV_LANG_ELASTIC . 'dauthau_result';
        // $params['type'] = 'nv4_vi_bidding_result';
        $params['body'] = $array_query_elastic;
        $response = $client->search($params)->asArray();
        if ($response['hits']['total']['value'] > 0) {
            $info['to_id'] = $response['hits']['hits'][0]['_source']['id'];
        }
        if (!isset($info['to_id']) or $info['to_id'] <= 0) {
            file_put_contents($filters_mail_kqlcnt_log, "Lỗi maxid ES \n", FILE_APPEND);
            die('Lỗi');
        }

        if (empty($last_logs)) {
            $start_id = 0; // Chạy lần đầu
            $end_id = $info['to_id'];
            $last_userid = 0;
            $new_loop = 1;
        } else {
            if ($last_logs['to_id'] < $info['to_id']) {
                // Nếu có tin thầu mới hoặc kế hoạch chọn nhà thầu mới, thông báo mời sơ tuyển mới thì lấy ra các bài mới để chạy vòng lặp mới
                $last_userid = 0;
                $start_id = $last_logs['to_id']; // Lấy id tin mời thầu của lần cuối cùng chạy
                $end_id = $info['to_id']; // Lấy id mời thầu lớn nhất của dữ liệu hiện có
                $new_loop = 1;
            } else {
                // Nếu không có dữ liệu mới để chạy thì dừng lại ngay
                file_put_contents($filters_mail_kqlcnt_log, "Lỗi select logs L71: Không có tin mới trong csdl. Hãy chạy tiếp khi có bài đăng mới\n", FILE_APPEND);
                die('Không có tin mới');
            }
        }
    }

    $time = time();
    $start_time = $time;
    if ($new_loop) {
        if ($end_id > $start_id + 500) {
            // Nếu có quá nhiều thì chỉ tìm kiếm trong 500
            $end_id = $start_id + 500;
        }

        if ($start_id == $end_id) {
            die('No Data new');
        }

        try {
            // thêm logs mới vào csdl
            $stmt = $db->prepare('INSERT INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs (from_id, to_id, from_time, to_time, total_time, userid, status) VALUES (:from_id, :to_id, :from_time, :to_time, 0, 0, 0)');
            $stmt->bindParam(':from_id', $start_id, PDO::PARAM_INT);
            $stmt->bindParam(':to_id', $end_id, PDO::PARAM_INT);
            $stmt->bindParam(':from_time', $time, PDO::PARAM_INT);
            $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
            $exc = $stmt->execute();
            $logs_id = $db->lastInsertId();
            $run_time = 0;
        } catch (PDOException $e) {
            file_put_contents($filters_mail_kqlcnt_log, "Lỗi thêm logs mới vào csdl L92: " . print_r($e, true) . "\n", FILE_APPEND);
            die('Lỗi thêm logs mới');
        }
    } else {
        $logs_id = $logs_info['id'];
        $run_time = $logs_info['total_time'];
    }
} else {
    // Khi test thành viên
    $start_id = 0;
    $info = $db->query('SELECT max(id) as to_id FROM ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_result')->fetch();
    $end_id = $info['to_id']; // Lấy id mời thầu lớn nhất của dữ liệu hiện có
    $end_id = 721420;
    $time = time();
    $start_time = $time;
    $run_time = 0;
}

$arr_id_vip = array();
$arr_vip = array();

// Bước 2: Lấy ra ds các VIP mỗi lần 100 người
// 2.1 Lấy danh sách các VIP còn hạn sử dụng
$time_start_thisday = mktime(date('H'), date('i'), date('s'), date('n'), date('j'), date('Y'));

$number_user_scan_all = 50;
if ($testUserid) {
    // Test 1 tài khoản VIP
    $query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id =' . $testUserid . ' AND vip = 7 AND prefix_lang = ' . $prefix_lang . '');
} else {
    // Lấy tài khoản vip
    $query_vip = $db->query('SELECT user_id, from_time, end_time, vip FROM ' . BID_PREFIX_GLOBAL . '_customs WHERE status=1 AND from_time <= ' . $time_start_thisday . ' AND end_time>=' . $time_start_thisday . ' AND user_id >' . $last_userid . ' AND vip = 7 AND prefix_lang = ' . $prefix_lang . ' ORDER BY user_id ASC limit ' . $number_user_scan_all);
}
$number_user_scan = 0;
$user_id_end = 0;
while ($vip_info = $query_vip->fetch()) {
    ++$number_user_scan;
    $user_id_end = $vip_info['user_id'];
    $arr_vip[$vip_info['user_id']]['from_time'] = $vip_info['from_time'];
    $arr_vip[$vip_info['user_id']]['to_time'] = $vip_info['end_time'];
    $arr_vip[$vip_info['user_id']]['filter'] = array();
    $arr_vip[$vip_info['user_id']]['bidding'] = array();
    $arr_vip[$vip_info['user_id']]['vip'][$vip_info['vip']] = $vip_info['vip'];
    $arr_id_vip[$vip_info['user_id']] = $vip_info['user_id'];
}

$arr_id_all = $arr_id_vip;

// Xuất thông tin test tài khoản VIP
if ($testUserid) {
    if (empty($arr_vip)) {
        echo ("User này không có tài khoản VIP<br/>");
    } else {
        echo ("Tài khoản VIP OK<br/>" . debugArray($arr_vip) . "<br/><br/>");
    }
}

// Bước 3: Lấy ra ds các bộ lọc của VIP đc chọn
if (!empty($arr_id_all)) {
    $query_filter = $db->query('SELECT * FROM ' . BID_PREFIX_GLOBAL . '_filter WHERE userid IN (' . implode(',', $arr_id_all) . ') AND status=1 AND vip_use=7 AND prefix_lang = ' . $prefix_lang . '');
    while ($result = $query_filter->fetch()) {
        if (in_array($result['vip_use'], $arr_vip[$result['userid']]['vip'])) {
            $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
        } else if (isset($arr_vip[$result['userid']]['filter'][$result['id']])) {
            $arr_vip[$result['userid']]['filter'][$result['id']] = $result;
        }
    }
}

// Xuất thông tin test bộ lọc
if ($testUserid) {
    if (empty($arr_vip[$testUserid]['filter'])) {
        echo ("User này không có bộ lọc<br/>");
    } else {
        echo ("Bộ lọc OK<br/>" . debugArray($arr_vip[$testUserid]['filter']) . "<br/><br/>");
    }
}

// Bước 4: Lấy ra các tin trong ds tin mới mà thỏa mãn bộ lọc tương ứng với từng VIP để thêm vào bảng _tmp
if (empty($arr_vip)) {
    if ($testUserid) {
        die("Kết thúc test thành viên<br />");
    }

    // Nếu không lấy được VIP nào thì coi như đã chạy xong một vòng và update lại status là đã chạy xong
    $time = NV_CURRENTTIME;
    $total_time = $time - $start_time;
    $change = $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs SET status = 1, to_time = ' . $db->quote($time) . ', total_time = ' . $db->quote($total_time) . ' WHERE id=' . $logs_id);
    file_put_contents($filters_mail_kqlcnt_log, "Đã quét xong một lượt các vip. L172 Tổng thời gian cho một lượt:" . $total_time . "\n", FILE_APPEND);
    die();
} else {
    if (!$testUserid) {
        $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs SET userid =' . $user_id_end . ' WHERE id =' . $logs_id);
    }

    file_put_contents($filters_mail_kqlcnt_log, "ArrayID KQLCNT:" . $start_id . "-" . $end_id . ";\n", FILE_APPEND);

    $db->beginTransaction();

    try {
        foreach ($arr_vip as $vip_id => $vip) {
            echo "Bắt đầu quét:" . $vip_id . "\n";
            file_put_contents($filters_mail_kqlcnt_log, "Bắt đầu quét:" . $vip_id . "; ", FILE_APPEND);
            // Lấy ra danh sách các tin mời thầu, KHLCNT, TBMST thỏa mãn điều kiện tìm kiếm từ các bộ lọc
            $arr_id = [];
            if (!empty($vip['filter'])) {
                foreach ($vip['filter'] as $filter) {
                    if (!empty($filter)) {
                        file_put_contents($filters_mail_kqlcnt_log, "id bộ lọc:" . $filter['id'] . "; ", FILE_APPEND);

                        // Từ bộ lọc chọn ra điều kiện lọc where tương ứng
                        $search_elastic = array();
                        $search_elastic['must'][]['range']['id'] = [
                            "gt" => $start_id,
                            "lte" => $end_id
                        ];

                        // tìm kiếm theo khoảng thời gian
                        if ($filter['time_find'] > 0) {
                            $time_find = mktime(0, 0, 0, date('n'), date('j'), date('Y')) - 86400 * $filter['time_find'];
                            $search_elastic['must'][]['range']['finish_time'] = [
                                "gte" => $time_find
                            ];
                        }
                        if ($filter['cat'] > 0) {
                            $search_elastic['must'][] = [
                                'match' => [
                                    'type_data' => [
                                        'query' => $filter['cat'] == 1 ? "1" : "0"
                                    ]
                                ]
                            ];
                        }
                        // Tìm kiếm theo tỉnh thành
                        if (!empty($filter['idprovince'])) {
                            $filter['idprovince'] = explode(',', $filter['idprovince']);
                            $search_idprovince = array();
                            foreach ($filter['idprovince'] as $key_idp) {
                                $search_idprovince[] = [
                                    'term' => [
                                        'id_province' => $key_idp
                                    ]
                                ];
                            }
                            $search_elastic['must'][]['bool']['should'] = $search_idprovince;
                        }

                        if ($filter['type_kqlcnt'] == 1) {
                            $search_elastic['must'][] = [
                                'term' => [
                                    'type_kqlcnt' => 0 
                                ]
                            ];
                        } elseif ($filter['type_kqlcnt'] == 2) {
                            $search_elastic['must'][] = [
                                'range' => [
                                    'type_kqlcnt' => [
                                        'gt' => 0 
                                    ]
                                ]
                            ];
                        }
                        if ($filter['type_bid'] > 0) {
                            $search_elastic['must'][] = [
                                'match' => [
                                    'type_bid_id' => [
                                        'query' => $filter['type_bid']
                                    ]
                                ]
                            ];
                        }
                        if ($filter['type_choose_id'] > 0) {
                            $search_elastic['must'][] = [
                                'match' => [
                                    'type_choose_id' => [
                                        'query' => $filter['type_choose_id']
                                    ]
                                ]
                            ];
                        }
                        
                        // Tìm theo lĩnh vực
                        if (!empty($filter['bidfieid'])) {
                            $search_field = array();
                            $filter['bidfieid'] = explode(',', $filter['bidfieid']);
                            foreach ($filter['bidfieid'] as $key) {
                                $search_field[] = [
                                    'term' => [
                                        'bidfieid.keyword' => $key
                                    ]
                                ];
                            }
                            $search_elastic['must'][]['bool']['should'] = $search_field;
                        }

                        // tìm theo giá mời thầu
                        if ($filter['price_plan_from'] > 0 and $filter['price_plan_to'] > 0) {
                            $search_elastic['must'][]['range']['bid_price_number'] = [
                                "gte" => $filter['price_plan_from'],
                                "lte" => $filter['price_plan_to']
                            ];
                        } else {
                            if ($filter['price_plan_from'] > 0) {
                                $search_elastic['must'][]['range']['bid_price_number'] = [
                                    "gte" => $filter['price_plan_from']
                                ];
                            }
                            if ($filter['price_plan_to'] > 0) {
                                $search_elastic['must'][]['range']['bid_price_number'] = [
                                    "lte" => $filter['price_plan_to']
                                ];
                            }
                        }

                        if ($filter['win_price_from'] > 0 and $filter['win_price_to'] > 0) {
                            $search_elastic['must'][]['range']['win_price_number'] = [
                                "gte" => $filter['win_price_from'],
                                "lte" => $filter['win_price_to']
                            ];
                        } else {
                            if ($filter['win_price_from'] > 0) {
                                $search_elastic['must'][]['range']['win_price_number'] = [
                                    "gte" => $filter['win_price_from']
                                ];
                            }
                            if ($filter['win_price_to'] > 0) {
                                $search_elastic['must'][]['range']['win_price_number'] = [
                                    "lte" => $filter['win_price_to']
                                ];
                            }
                        }

                        if ($filter['search_type'] == 1) {
                            if ($filter['par_search']) {
                                $search_fields = ['code', 'title'];
                            } else {
                                $search_fields = ['content_full'];
                            }
                        } else {
                            if ($filter['par_search']) {
                                $search_fields = ['code', 'title_search'];
                            } else {
                                $search_fields = ['content'];
                            }
                        }
                        if ($filter['goods_search'] == 1) {
                            $search_fields[] = 'content_goods';
                        } elseif ($filter['goods_search'] == 2) {
                            $search_fields = ['content_goods'];
                        }
                        // từ khóa loại trừ
                        if (!empty($filter['without_key'])) {
                            $arr_key = explode(',', $filter['without_key']);
                            foreach($search_fields as $f) {
                                foreach ($arr_key as $key) {
                                    if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                                        continue;
                                    }
                                    if (empty($key)){
                                        continue;
                                    }
                                    if ($filter['search_type'] == 1) {
                                        $key = trim($key);
                                        $search_elastic['must_not'][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    } else {
                                        if ($f == 'content_goods') {
                                            $key = strtolower($key);
                                        } else {
                                            $key = str_replace('-', ' ', change_alias($key));
                                        }

                                        $key = trim($key);
                                        $search_elastic['must_not'][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    }
                                }
                            }
                        }

                        if (!empty($filter['key_search2'])) {
                            // Tìm theo từ khóa cùng
                            $arr_key = explode(',', $filter['key_search2']);
                            $search_bosung_should = [];
                            foreach($search_fields as $f) {
                                foreach ($arr_key as $key) {
                                    if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                                        continue;
                                    }
                                    if (empty($key)){
                                        continue;
                                    }
                                    if (!empty($filter['search_one_key'])) {
                                        //Một trong các từ khóa là điều kiện bắt buộc
                                        if ($filter['search_type'] == 1) {
                                            $key = trim($key);
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        } else {
                                            $key = str_replace('-', ' ', change_alias($key));
                                            $key = trim($key);
                                            $search_bosung_should[] = [
                                                "match_phrase" => [
                                                    $f => ($f != 'content_goods' ? $key : strtolower($key))
                                                ]
                                            ];
                                        }
                                    } else {
                                        if ($filter['search_type'] == 1) {
                                            $key = trim($key);
                                            $search_elastic['must'][] = [
                                                "match_phrase" => [
                                                    $f => $key
                                                ]
                                            ];
                                        } else {
                                            $key = str_replace('-', ' ', change_alias($key));
                                            $key = trim($key);
                                            $search_elastic['must'][] = [
                                                "match_phrase" => [
                                                    $f => ($f != 'content_goods' ? $key : strtolower($key))
                                                ]
                                            ];
                                        }
                                    }
                                }
                            }
                            if (!empty($search_bosung_should)) {
                                $search_elastic['must'][] = [
                                    "bool" => [
                                        "should" => $search_bosung_should,
                                        "minimum_should_match" => 1
                                    ]
                                ];
                            }
                        }

                        if (!empty($filter['key_search'])) {
                            $arr_key = explode(',', $filter['key_search']);
                            if (!empty($filter['searchkind'])) {
                                $arr_key = array_map(function ($a) {return explode(' ', $a);}, $arr_key);
                                $arr_key = new RecursiveIteratorIterator(new RecursiveArrayIterator($arr_key));
                            }
                            $keyword_type = $filter['searchkind'] <= 1 ? 'should' : 'must';
                            foreach($search_fields as $f) {
                                foreach ($arr_key as $key) {
                                    if (!preg_match('/[0-9]/', $key) && $f == 'code') {
                                        continue;
                                    }
                                    if (empty($key)){
                                        continue;
                                    }
                                    if ($filter['search_type'] == 0 && $f != 'content_goods') {
                                        $key = str_replace('-', ' ', change_alias($key));
                                    } else if ($filter['search_type'] == 0 && $f == 'content_goods') {
                                        $key = strtolower($key);
                                    }
                                    $key = trim($key);
                                    if (preg_match('/^[0-9]+?$/', $key)) { // Nếu keyword chỉ là dãy số thì tìm wildcard
                                        $search_elastic[$keyword_type][] = [
                                            "wildcard" => [
                                                $f => [
                                                    "value" => '*' . $key . '*'
                                                ]
                                            ]
                                        ];
                                    } else {
                                        $search_elastic[$keyword_type][] = [
                                            "match_phrase" => [
                                                $f => $key
                                            ]
                                        ];
                                    }
                                }
                            }
                        }

                        // Chỉ lọc ra những KQLCNT của msc mới
                        $search_elastic['must'][] = [
                            'match' => [
                                'is_new_msc' => [
                                    'query' => 1
                                ]
                            ]
                        ];
                        $search_elastic['must_not'][] = [
                            'match' => [
                                'type_kqlcnt' => [
                                    'query' => 2
                                ]
                            ]
                        ];

                        if (!empty($search_elastic['should'])) {
                            $search_elastic['minimum_should_match'] = '1';
                            $search_elastic['boost'] = '1.0';
                        }

                        $array_query_elastic = array();
                        if (!empty($search_elastic)) {
                            $array_query_elastic['query']['bool'] = $search_elastic;
                        }
                        $array_query_elastic['size'] = 500;
                        $array_query_elastic['sort'] = [
                            [
                                "finish_time" => [
                                    "order" => "desc"
                                ]
                            ]
                        ];

                        if ($testUserid) {
                            echo "Tìm kiếm với query: <br />" . debugArray($array_query_elastic) . "<br /><br />";
                        }

                        $array_query_elastic['_source'] = array(
                            'id'
                        );

                        $params = array();
                        $params['index'] = NV_LANG_ELASTIC . 'dauthau_result';
                        $params['body'] = $array_query_elastic;
                        $response = $client->search($params)->asArray();
                        
                        if ($testUserid) {
                            echo "Kết quả: <br />" . debugArray($response) . "<br /><br />";
                        }

                        $num_row = $response['hits']['total']['value'];
                        foreach ($response['hits']['hits'] as $value) {
                            if (!empty($value['_source'])) {
                                $bidding = $value['_source'];
                                $arr_id[$bidding['id']] = $bidding['id'];
                                if (!$testUserid) {
                                    // Nếu tìm ra tin thầu thỏa mãn điều kiện thì thêm bảng tmp
                                    $stmt = $db->prepare('INSERT IGNORE INTO ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_bidkqlc_id(userid, filter_id, bid_id, send_status, addtime) VALUES (:userid, :filter_id, :bid_id, 0, :addtime)');
                                    $time = NV_CURRENTTIME;
                                    $stmt->bindParam(':userid', $vip_id, PDO::PARAM_INT);
                                    $stmt->bindParam(':filter_id', $filter['id'], PDO::PARAM_INT);
                                    $stmt->bindParam(':bid_id', $bidding['id'], PDO::PARAM_INT);
                                    $stmt->bindParam(':addtime', $time, PDO::PARAM_INT);
                                    $exc = $stmt->execute();
                                }
                            }
                        }

                        // ghi log bộ lọc
                        if ($num_row > 0) {
                            $db->exec('UPDATE ' . BID_PREFIX_GLOBAL . '_filter SET stat_lastresult = ' . NV_CURRENTTIME . ', stat_totalresult = stat_totalresult + ' . $num_row . ' WHERE id=' . $filter['id']);
                        }
                    }
                }
            }
            file_put_contents($filters_mail_kqlcnt_log, " Bid:" . implode(',', $arr_id) . "\n", FILE_APPEND);
            if (!$testUserid) {
                // ghi lại logs cho quá trình tìm kiếm tin cho mỗi vip
                $time = NV_CURRENTTIME;
                $stmt = $db->prepare('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs SET to_time = :to_time, userid = :userid WHERE id =' . $logs_id);
                $_vip_id = ($vip_id >= $user_id_end) ? $user_id_end : $vip_id;
                $stmt->bindParam(':userid', $_vip_id, PDO::PARAM_INT);
                $stmt->bindParam(':to_time', $time, PDO::PARAM_INT);
                $exc = $stmt->execute();
            }
        }
        $db->commit();
    } catch (PDOException $e) {
        file_put_contents($filters_mail_kqlcnt_log, "Lỗi INSERT  L401:\n" . print_r($e, true) . "\n", FILE_APPEND);
        $db->rollBack();
        notify_to_slack($filters_mail_kqlcnt_log, "filters_mail_kqlcnt_elastic.php Lỗi INSERT  L401:\n" . print_r($e, true) . "\n");
        die(); // Remove this line after checks finished
    }

    // Dừng nếu test
    if ($testUserid) {
        die("Kết thúc test thành viên<br />");
    }

    // Sau khi chạy xong một vòng 50 VIP thì thông báo chạy xong và thời gian chạy cho một vòng
    $time = time();
    $this_time = $time - NV_CURRENTTIME;
    $total_time = intval($run_time + $this_time);
    try {
        if ($number_user_scan < $number_user_scan_all) {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ', status=1 where id =' . $logs_id);
        } else {
            $db->query('UPDATE ' . NV_PREFIXLANG . '_' . BIDDING_MODULE . '_kqlcnt_logs SET to_time = ' . $time . ', total_time = ' . $total_time . ' where id =' . $logs_id);
        }
        if (sizeof($arr_id_vip)) {
            $db->query('UPDATE ' . BID_PREFIX_GLOBAL . '_customs  SET filters_mail = ' . NV_CURRENTTIME . ' WHERE user_id IN (' . implode(',', $arr_id_vip) . ') AND vip = 7 AND prefix_lang = ' . $prefix_lang . '');
        }
    } catch (PDOException $e) {
        file_put_contents($filters_mail_kqlcnt_log, "Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP  L415:\n" . print_r($e, true) . "\n", FILE_APPEND);
        notify_to_slack($filters_mail_kqlcnt_log, "filters_mail_kqlcnt_elastic.php Lỗi update logs sau khi chạy một vòng " . $number_user_scan_all . " VIP  L415:\n" . print_r($e, true) . "\n");
        die();
    }

    $note = 'L420 Đã quét xong. Thời gian bắt đầu:' . nv_date('H:i:s d/m/Y', $start_time) . ', thời gian kết thúc:' . nv_date('H:i:s d/m/Y', $time) . ', thời gian chạy:' . $this_time;
    file_put_contents($filters_mail_kqlcnt_log, $note . "\n", FILE_APPEND);
    die($note . "\n");
}

/**
 *
 * @param array $data
 * @return string
 */
function debugArray($data)
{
    return "<pre><code>" . print_r($data, true) . "</code></pre>";
}
