<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';

$filename = NV_ROOTDIR . '/tools/update_alias_plans_project_' . NV_PREFIXLANG . '.txt';
if (file_exists($filename)) {
    $_last_id = intval(file_get_contents($filename));
} else {
    $_last_id = 0;
}

$sql = "SELECT max(id) FROM " . NV_PREFIXLANG . "_bidding_plans_project";
$max_id = $db->query($sql)->fetchColumn();
do {
    echo "last_id " . NV_PREFIXLANG . ": " . $_last_id . "\n";
    $_last_id2 = $_last_id + 1000;

    $params = [];
    $sql = "SELECT id, code, title FROM " . NV_PREFIXLANG . "_bidding_plans_project WHERE id > " . $_last_id . " AND id < " . $_last_id2 . " ORDER BY id ASC LIMIT 500";
    $_query = $db->query($sql);
    $array_alias = [];
    $_last_id = 0;

    while ($row = $_query->fetch()) {
        $_last_id = $row['id'];
        if ($_last_id > $max_id) {
            break;
        }

        $_num = ($row['id'] > 1310) ? 110 : 200; // Số này cố định khi chạy chính thức
        $alias = nv_clean_alias($row['title'], $_num);
        if (empty($alias)) {
            $alias = nv_clean_alias($row['title'], $_num);
        }
        $sql = "UPDATE " . NV_PREFIXLANG . "_bidding_plans_project SET alias = " . $db->quote($alias) . " WHERE id = " . $row['id'];
        $db->exec($sql);
    }
    $_query->closeCursor();

    if ($_last_id > $max_id) {
        echo ('Thực hiện xong');
        break;
    } elseif (empty($_last_id)) {
        $_last_id = $_last_id2;
        if ($_last_id > $max_id) {
            echo ('Thực hiện xong');
            break;
        }
    }
} while ($_last_id > 0);

die("Thực hiện xong");

function nv_clean_alias($string, $num = 110, $lower = true)
{
    $search = [
        '&amp;',
        '&#039;',
        '&quot;',
        '&lt;',
        '&gt;',
        '&#x005C;',
        '&#x002F;',
        '&#40;',
        '&#41;',
        '&#42;',
        '&#91;',
        '&#93;',
        '&#33;',
        '&#x3D;',
        '&#x23;',
        '&#x25;',
        '&#x5E;',
        '&#x3A;',
        '&#x7B;',
        '&#x7D;',
        '&#x60;',
        '&#x7E;'
    ];
    $replace = [
        '&',
        '\'',
        '"',
        '<',
        '>',
        '\\',
        '/',
        '(',
        ')',
        '*',
        '[',
        ']',
        '!',
        '=',
        '#',
        '%',
        '^',
        ':',
        '{',
        '}',
        '`',
        '~'
    ];

    $string = str_replace($search, $replace, $string);
    $string = nv_compound_unicode($string);
    $string = nv_EncString($string);
    $string = preg_replace(array(
        '/[^a-zA-Z0-9]/',
        '/[ ]+/',
        '/^[\-]+/',
        '/[\-]+$/'
    ), array(
        ' ',
        '-',
        '',
        ''
    ), $string);

    $len = strlen($string);
    if ($num and $len > $num) {
        $_substring = substr($string, 0, $num);
        while (str_contains($_substring, '-') and substr($string, $num, 1) != '-') {
            --$num;
            $_substring = substr($_substring, 0, $num);
        }
        $string = substr($string, 0, $num);
    }

    if ($lower) {
        $string = strtolower($string);
    }
    return $string;
}

die('đã thực hiện xong');
