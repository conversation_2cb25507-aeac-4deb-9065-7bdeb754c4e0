<?php
define('NV_SYSTEM', true);
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));
require NV_ROOTDIR . '/mainfile.php';
// Thực hiện lấy dữ liệu từ trên ES xuống

$waitTimeoutInSeconds = 2;
if ($fp = fsockopen($config_bidding['elas_result_host'], $config_bidding['elas_result_port'], $errCode, $errStr, $waitTimeoutInSeconds)) {
        // It worked
    $elastic_online = 1;
    fclose($fp);
} else {
        // It didn't work
    $elastic_online = 0;
    echo "Server Elasticsearch didn't work: ";
    echo "ERROR: $errCode - $errStr<br />\n";
}

$hosts = array(
    $config_bidding['elas_result_host'] . ':' . $config_bidding['elas_result_port']
);

$client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_result_user'], $config_bidding['elas_result_pass'])
    ->setHosts($hosts)
    ->setRetries(0)
    ->build();

// Lấy danh sách hàng hoá
$params = [
    'index' => 'dauthau_result_goods',
    'body'  => [
        'size' => 2000,
        '_source' => [
            'id'
        ],
        'query' => [
            'bool' => [
                'must' => [
                    [
                        'match' => [
                            'goods_name' => 'Triglycerides'
                        ]
                    ]
                ]
            ]
        ]
    ]
];
$response = $client->search($params);

$arrIdGoods = [];
// Hiển thị kết quả
if (isset($response['hits']['hits'])) {
    foreach ($response['hits']['hits'] as $value) {
        if (!empty($value['_source'])) {
            $row = $value['_source'];
            $arrIdGoods[] = $row['id'];
        }
    }
}

if (empty($arrIdGoods)) {
    echo("\n>> Không có dữ liệu hàng hoá");
    exit(1);
}

// Lấy danh sách hàng hoá
$sqlGoods = $db->query("SELECT * FROM nv4_bidding_result_goods WHERE id IN (" . implode(',', $arrIdGoods) . ")");

$arrInsert = [];
$arrKey = [];
$arrIdResult = [];
while ($row = $sqlGoods->fetch()) {
    unset($row['id']);
    if (empty($arrKey)) {
        $arrKey = array_keys($row);
    }

    $arrInsert[] = '(' . implode(',', array_map([$db, 'quote'], array_values($row))) . ')';
    $arrIdResult[$row['result_id']] = $row['result_id'];
}
$sqlGoods->closeCursor();

if (!empty($arrInsert)) {
    $insertGooods = 'INSERT INTO nv4_bidding_result_goods (' . implode(',', $arrKey) .') VALUES ' . implode(',', $arrInsert);
    if (!file_exists(NV_ROOTDIR . '/tools/data_goods.sql')) {
        file_put_contents(NV_ROOTDIR . '/tools/data_goods.sql', $insertGooods);
        echo "\n>> Đã lưu xong dữ liệu goods";
    }
    unset($arrKey);
    unset($arrInsert);

    $params = [
        'index' => 'dauthau_result',
        'body'  => [
            'from' => 0,
            'size' => 2000,
            'query' => [
                'terms' => [
                    '_id' => array_values($arrIdResult)
                ]
            ]
        ]
    ];

    $response = $client->search($params);

    // Hiển thị kết quả
    if (isset($response['hits']['hits'])) {
        foreach ($response['hits']['hits'] as $value) {
            if (!empty($value['_source'])) {
                $row = $value['_source'];
                if (empty($arrKey)) {
                    $arrKey = array_keys($row);
                }

                $arrInsert[] = '(' . implode(',', array_map([$db, 'quote'], array_values($row))) . ')';
            }
        }
    }

    if (!empty($arrInsert)) {
        $insertResult = 'INSERT INTO nv4_vi_bidding_result (' . implode(',', $arrKey) .') VALUES ' . implode(',', $arrInsert);
        if (!file_exists(NV_ROOTDIR . '/tools/data_result.sql')) {
            file_put_contents(NV_ROOTDIR . '/tools/data_result.sql', $insertResult);
            echo "\n>> Đã lưu xong dữ liệu result";
        }
    }

    // Lưu dữ liệu bảng result_business
    $sqlResultBusiness = $db->query("SELECT * FROM nv4_vi_bidding_result_business WHERE resultid IN (" . implode(',', array_values($arrIdResult)) . ")");

    $arrInsert = [];
    $arrKey = [];
    while ($row = $sqlResultBusiness->fetch()) {
        unset($row['id']);
        if (empty($arrKey)) {
            $arrKey = array_keys($row);
        }

        $arrInsert[] = '(' . implode(',', array_map([$db, 'quote'], array_values($row))) . ')';
    }
    $sqlResultBusiness->closeCursor();

    if (!empty($arrInsert)) {
        $insertResultBusiness = 'INSERT INTO nv4_vi_bidding_result_business (' . implode(',', $arrKey) .') VALUES ' . implode(',', $arrInsert);
        if (!file_exists(NV_ROOTDIR . '/tools/data_result_business.sql')) {
            file_put_contents(NV_ROOTDIR . '/tools/data_result_business.sql', $insertResultBusiness);
            echo "\n>> Đã lưu xong dữ liệu result business";
        }
    }
}


echo "\nThoi gian thuc hien = " . number_format((microtime(true) - NV_START_TIME), 3, '.', '');
echo "\n> Đã chạy xong";
echo "\n-------------------------------------------\n";
