<?php

// <PERSON>ac dinh thu muc goc cua site
define('NV_SYSTEM', true);

// <PERSON>ac dinh thu muc goc cua site
define('NV_ROOTDIR', str_replace('\\', '/', realpath(pathinfo(__file__, PATHINFO_DIRNAME) . '/../')));

require NV_ROOTDIR . '/mainfile.php';
if (ob_get_level()) {
    ob_end_clean();
}

// Các thể loại đấu thầu rộng rãi
const HTLC_DTRR = [
    'DTRR', 'Đ<PERSON>u thầu rộng rãi', '<PERSON><PERSON>u thầu rộng rãi trong nước', '<PERSON><PERSON><PERSON> thầu rộng rãi quốc tế',
    'Competitive Bidding', 'National Competitive Bidding', 'International Open Bidding', 'International Competitive Bidding'
];

// <PERSON><PERSON>c thể loại đấu thầu hạn chế
const HTLC_DTHC = [
    'DTHC', 'Đ<PERSON>u thầu hạn chế', '<PERSON><PERSON><PERSON> thầu hạn chế trong nước', '<PERSON><PERSON><PERSON> thầu hạn chế quốc tế',
    'Shortened competitive bidding', 'National shortened competitive bidding', 'International shortened competitive bidding'
];

// Các thể loại chào hàng cạnh tranh
const HTLC_CHCT = [
    'CHCT', 'Chào hàng cạnh tranh', 'Chào hàng cạnh tranh trong nước', 'Chào hàng cạnh tranh quốc tế',
    'Competitive Offer', 'National competitive offer', 'International competitive offer'
];

// Các thể loại chào hàng cạnh tranh rút gọn
const HTLC_CHCTRG = [
    'CHCTRG', 'Chào hàng cạnh tranh rút gọn', 'Chào hàng cạnh tranh rút gọn trong nước', 'Chào hàng cạnh tranh rút gọn quốc tế',
    'Shortened competitive offer', 'National shortened competitive offer', 'International shortened competitive offer'
];

$id = 0;
$dem = 0;
try {

    $maxid = $db->query("SELECT MAX(id) FROM `nv4_vi_bidding_row`")->fetchColumn();

    do {
        $num_rows = 0;
        $id2 = $id + 1000;

        $query_url = $db->query("SELECT * FROM `nv4_vi_bidding_row` WHERE id > " . $id . " AND id < " . $id2 . " ORDER BY `id` ASC LIMIT 100");
        $id = 0;
        
        $violated_ids = [];
        $params = [];
        
        while ($bidding_row = $query_url->fetch()) {
            $is_violated = 0;
            $trong_pham_vi = true;

            if ($bidding_row['pham_vi'] == 'Ngoài phạm vi điều chỉnh của Luật đấu thầu' or $bidding_row['pham_vi'] == 1 or $bidding_row['type_bid'] == 1) $trong_pham_vi = false;

            if ($trong_pham_vi) {
                $linh_vuc_thong_bao = $bidding_row['linh_vuc_thong_bao_id'] ?? $bidding_row['linh_vuc_thong_bao'];
                
                $pub_y = getdate($bidding_row['ngay_dang_tai'])['year'];
                
                if ($pub_y > 2019) {
                    switch ($pub_y) {
                        case 2020:
                            if ((($linh_vuc_thong_bao != 2 and $bidding_row['price'] < 5000000000) or
                                ($linh_vuc_thong_bao == 2 and $bidding_row['price'] < 10000000000))) {
                                $is_violated = 1;
                            }
                            break;
                        case 2021:
                            if ((($linh_vuc_thong_bao != 2 and $bidding_row['price'] < 10000000000) or
                                ($linh_vuc_thong_bao == 2 and $bidding_row['price'] < 20000000000))) {
                                $is_violated = 1;
                            }
                            break;
                        case 2022:
                            if (isMixPt21Pt22($bidding_row)) break;
                            if (
                                in_array($bidding_row['hinh_thuc_lua_chon'], array_merge(HTLC_DTRR, HTLC_CHCT, HTLC_CHCTRG))
                                and ((in_array($linh_vuc_thong_bao, [1, 3, 4]) and $bidding_row['price'] <= 10000000000)
                                    or (in_array($linh_vuc_thong_bao, [2]) and $bidding_row['price'] <= 20000000000)
                                )
                            ) {
                                $is_violated = 1;
                            }
                            break;
                        case 2023:
                            if (isMixPt21Pt22($bidding_row)) break;
                            if ((in_array($bidding_row['hinh_thuc_lua_chon'], array_merge(HTLC_DTRR, HTLC_DTHC, HTLC_CHCT, HTLC_CHCTRG)))
                                and in_array($linh_vuc_thong_bao, [1, 2, 3, 4])
                                and $bidding_row['price'] <= 200000000000
                            ) {
                                $is_violated = 1;
                            }
                            break;
                        default: // >= 2024
                            if (isMixPt21Pt22($bidding_row) || isTbmtQt($bidding_row)) break;
                            if ((in_array($bidding_row['hinh_thuc_lua_chon'], array_merge(HTLC_DTRR, HTLC_DTHC, HTLC_CHCT, HTLC_CHCTRG)))
                                and in_array($linh_vuc_thong_bao, [1, 2, 3, 4])
                                and $bidding_row['price'] <= 500000000000
                            ) {
                                $is_violated = 1;
                            }
                    }
                }
            }
            
            if ($is_violated) {
                $violated_ids[] = $bidding_row['id'];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'dauthau_bidding',
                        '_id' => $bidding_row['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 1,
                    ]
                ];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'en_dauthau_bidding',
                        '_id' => $bidding_row['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 1,
                    ]
                ];
            } else {
                $params['body'][] = [
                    'update' => [
                        '_index' => 'dauthau_bidding',
                        '_id' => $bidding_row['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 0,
                    ]
                ];
                $params['body'][] = [
                    'update' => [
                        '_index' => 'en_dauthau_bidding',
                        '_id' => $bidding_row['id']
                    ]
                ];
                $params['body'][] = [
                    'doc' => [
                        'is_violated' => 0,
                    ]
                ];
            }
            
            $id = $bidding_row['id'];
            $num_rows++;
            $dem++;
        }
        $query_url->closeCursor();
        
        if (!empty($violated_ids)) {
            $str_ids = implode(',', $violated_ids);
            $db->exec('UPDATE nv4_vi_bidding_row SET is_violated = 1 WHERE id IN (' . $str_ids . ')');
            $db->exec('UPDATE nv4_en_bidding_row SET is_violated = 1 WHERE id IN (' . $str_ids . ')');
        }
        
        if (!empty($params['body'])) {
            $hosts = array(
                $config_bidding['elas_host'] . ':' . $config_bidding['elas_port']
            );
    
            $client = Elastic\Elasticsearch\ClientBuilder::create()->setBasicAuthentication($config_bidding['elas_user'], $config_bidding['elas_pass'])
                ->setHosts($hosts)
                ->setRetries(0)
                ->build();
            $responses = $client->bulk($params)->asArray();
            $params = null;
            if (!empty($responses['items'])) {
                foreach ($responses['items'] as $value) {
                    if ($value['update']['status'] == 404) {
                        if (str_starts_with($value['update']['_index'], 'en_')) {
                            $db->exec('UPDATE nv4_en_bidding_row SET elasticsearch=0 WHERE id = ' . $value['update']['_id']);
                        } else {
                            $db->exec('UPDATE nv4_vi_bidding_row SET elasticsearch=0 WHERE id = ' . $value['update']['_id']);
                        }
                    } elseif ($value['update']['status'] != 200) {
                        print_r("Xử lý id: \033[33m" . $value['update']['_id']);
                        print_r("\033[31m ERROR\033[0m\n");
                        file_put_contents(NV_ROOTDIR . '/tools/check_violate_tbmt.log', (str_starts_with($value['update']['_index'], 'en_') ? 'en' : 'vi')  . ' ' . $value['update']['_id'] . PHP_EOL, FILE_APPEND);
                    }
                }
            }
            $responses = null;
            $client = null;
        }

        echo 'Check violate TBMT id: ' . number_format($id) . '/' . number_format($maxid) . PHP_EOL;
        if ($id == 0 and $id2 < $maxid) {
            $id = $id2;
        } elseif ($id == 0 and $id2 >= $maxid) {
            break;
        }
    } while ($id < $maxid);
} catch (PDOException $e) {
    trigger_error(print_r($e, true));
    die($e->getMessage());
}
echo "Totally checked violate TBMT: " . $dem . " - Kết thúc!!!\n\n";

function isTbmtQt($item)
{
    return $item['id_hinhthucluachon'] == 2 || $item['id_hinhthucluachon'] == 0;
}

function isMixPt21Pt22($item)
{
    return $item['linh_vuc'] == 5 || $item['linh_vuc_thong_bao'] == 5 ||
        $item['phuong_thuc_num'] == 21 || $item['phuong_thuc_num'] == 22;
}
